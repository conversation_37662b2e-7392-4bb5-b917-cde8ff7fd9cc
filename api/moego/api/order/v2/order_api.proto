// (-- api-linter: core::0136::request-message-name=disabled
//     aip.dev/not-precedent: API layer naming. --)
// (-- api-linter: core::0136::response-message-name=disabled
//     aip.dev/not-precedent: API layer naming. --)

syntax = "proto3";

package moego.api.order.v2;

import "google/type/money.proto";
import "moego/models/order/v1/order_detail_models.proto";
import "moego/models/order/v1/order_enums.proto";
import "moego/models/order/v1/order_models.proto";
import "moego/models/payment/v2/payment_models.proto";
import "moego/service/order/v2/order_service.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/api/order/v2;orderapipb";
option java_multiple_files = true;
option java_package = "com.moego.idl.api.order.v2";

// Order service.
service OrderService {
  // 预览创建 Sales 订单
  rpc PreviewCreateOrder(PreviewCreateOrderParams) returns (PreviewCreateOrderResult);
  // 创建 Sales 订单.
  rpc CreateOrder(CreateOrderParams) returns (CreateOrderResult);

  // 创建支付单据并且发起支付
  rpc PayOrder(PayOrderParams) returns (PayOrderResult);
}

// pay order params
message PayOrderParams {
  // 订单层所需要的参数
  // 支付的订单
  int64 order_id = 1 [(validate.rules).int64 = {gt: 0}];
  // total amount：  total_amount = amount + payment_tips
  // 支付金额 = 填写的金额
  google.type.Money amount = 2 [(validate.rules).message.required = true];
  // payment tips before create = 支付前选择的tips
  // (-- api-linter: core::0140::prepositions=disabled
  //     aip.dev/not-precedent: 想不出更好的命名。 --)
  optional google.type.Money payment_tips_before_create = 3;

  // 支付层所需要的参数
  // 支付类型
  moego.models.payment.v2.PaymentModel.PaymentType payment_type = 11;
  // 支付方式
  moego.models.payment.v2.PaymentMethod.MethodType payment_method_type = 12;
  // 支付凭证
  moego.models.payment.v2.PaymentMethod.Detail payment_method_detail = 13;
  // 是否添加cv fee,不传的时候后端判断
  optional bool add_convenience_fee = 14;
  // paid by, 一般是 customer name
  string payer = 15;
  // payment description
  string payment_description = 16;
}

// pay order result
message PayOrderResult {
  // Order payment.
  models.order.v1.OrderPaymentModel order_payment = 1 [(validate.rules).message.required = true];
  // raw payment result，渠道返回的原始数据，用于前端加载第三方支付组件
  // e.g. adyen 3ds2:
  // `{
  //   "resultCode": "IdentifyShopper",
  //   "action": {
  //     "paymentData": "Ab02b4c0!BQABAgCuZFJrQOjSsl\\/zt+...",
  //     "paymentMethodType": "scheme",
  //     "authorisationToken": "Ab02b4c0!BQABAgAvrX03p...",
  //     "subtype": "fingerprint",
  //     "token": "eyJ0aHJlZURTTWV0aG9kTm90aWZpY...",
  //     "type": "threeDS2"
  //   }
  // }`
  string raw_payment_result = 2;
}

// Preview create order request.
message PreviewCreateOrderParams {
  // SourceType
  models.order.v1.OrderSourceType source_type = 1 [(validate.rules).enum = {defined_only: true}];
  // SourceID
  int64 source_id = 2 [(validate.rules).int64 = {gte: 0}];
  // Business ID.
  int64 business_id = 3 [(validate.rules).int64 = {gt: 0}];
  // Customer ID.
  int64 customer_id = 4 [(validate.rules).int64 = {gt: 0}];

  // Order items.
  repeated service.order.v2.PreviewCreateOrderRequest.CartItem items = 11 [(validate.rules).repeated = {min_items: 1}];
  // tips amount.
  google.type.Money tips_amount = 12;
  // Promotions.
  oneof promotions {
    // 自动 apply 可用的优惠. 不含 store credit.
    bool auto_apply_promotions = 13;
    // 手动指定的优惠.
    service.order.v2.PreviewCreateOrderRequest.AppliedPromotions applied_promotions = 14;
  }
}

// Preview create order result.
message PreviewCreateOrderResult {
  // Order.
  models.order.v1.OrderDetailView order = 1;

  // applied promotions list.
  service.order.v2.PreviewCreateOrderRequest.AppliedPromotions applied_promotions = 2;
}

// Create order params.
message CreateOrderParams {
  // Pre-defined source type.
  models.order.v1.OrderSourceType source_type = 1 [(validate.rules).enum = {
    defined_only: true
    not_in: [0]
  }];
  // Source ID. 结合 source type 表示不同的 ID.
  int64 source_id = 2 [(validate.rules).int64 = {gt: 0}];
  // Business ID.
  int64 business_id = 3 [(validate.rules).int64 = {gt: 0}];
  // Staff ID. 0 for pay online
  int64 staff_id = 4 [(validate.rules).int64 = {gte: 0}];
  // Customer ID.
  int64 customer_id = 5 [(validate.rules).int64 = {gt: 0}];
  // order title
  string title = 6;
  // description
  string description = 7;

  // Order items.
  repeated service.order.v2.PreviewCreateOrderRequest.CartItem items = 11 [(validate.rules).repeated = {min_items: 1}];
  // Tips amount.
  google.type.Money tips_amount = 12;
  // Applied promotions.
  service.order.v2.PreviewCreateOrderRequest.AppliedPromotions applied_promotions = 13;
}

// Create order result.
message CreateOrderResult {
  // Order.
  models.order.v1.OrderDetailView order = 1;
}
