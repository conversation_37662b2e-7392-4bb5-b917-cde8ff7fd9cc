syntax = "proto3";

package moego.api.order.v1;

import "moego/models/offering/v1/service_enum.proto";
import "moego/models/order/v1/service_charge_def.proto";
import "moego/models/order/v1/service_charge_enums.proto";
import "moego/models/order/v1/service_charge_model.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/api/order/v1;orderapipb";
option java_multiple_files = true;
option java_package = "com.moego.idl.api.order.v1";

// get service charge request params
message GetCompanyServiceChargeListParams {
  // is active
  optional bool is_active = 1;
  //bid list
  repeated int64 business_ids = 2;
  // surcharge type
  optional moego.models.order.v1.SurchargeType surcharge_type = 3 [(validate.rules).enum = {
    not_in: [0]
    defined_only: true
  }];
}

// add service charge request params
message AddCompanyServiceChargeParams {
  // name
  string name = 1 [(validate.rules).string = {
    min_len: 1
    max_len: 150
  }];
  // split method
  optional string description = 2 [(validate.rules).string = {
    min_len: 0
    max_len: 1000
  }];
  // price, must be positive
  double price = 3 [(validate.rules).double = {gte: 0}];
  // tax id, 0 or null means no tax
  int32 tax_id = 4 [(validate.rules).int32 = {gte: 0}];
  // is mandatory
  // deprecated by freeman since /2024/9/24, use auto_apply_status instead
  optional bool is_mandatory = 5 [deprecated = true];
  // is active
  optional bool is_active = 6;
  //is all location
  optional bool is_all_location = 7;
  // apply to upcoming
  optional bool apply_upcoming_appt = 8;
  //service charge location override data
  repeated moego.models.order.v1.ServiceChargeLocationOverride location_override_list = 9;
  // auto apply status
  optional moego.models.order.v1.ServiceCharge.AutoApplyStatus auto_apply_status = 10 [(validate.rules).enum = {
    not_in: [0]
    defined_only: true
  }];
  // auto apply condition
  optional moego.models.order.v1.ServiceCharge.AutoApplyCondition auto_apply_condition = 11 [(validate.rules).enum = {
    not_in: [0]
    defined_only: true
  }];
  // auto apply time, unit: minute
  optional int32 auto_apply_time = 12 [(validate.rules).int32 = {
    gte: 0
    lte: 1440
  }];
  // auto support service
  repeated models.offering.v1.ServiceItemType service_item_types = 13 [(validate.rules).repeated = {
    min_items: 0
    unique: true
    items: {
      enum: {
        defined_only: true
        not_in: [0]
      }
    }
  }];
  // auto apply time type
  optional moego.models.order.v1.ServiceCharge.AutoApplyTimeType auto_apply_time_type = 14 [(validate.rules).enum = {
    not_in: [0]
    defined_only: true
  }];
  // apply type
  optional moego.models.order.v1.ServiceCharge.ApplyType apply_type = 15 [(validate.rules).enum = {
    not_in: [0]
    defined_only: true
  }];
  // surcharge type
  optional moego.models.order.v1.SurchargeType surcharge_type = 16 [(validate.rules).enum = {
    not_in: [0]
    defined_only: true
  }];
  // charge method
  optional moego.models.order.v1.ChargeMethod charge_method = 17 [(validate.rules).enum = {
    not_in: [0]
    defined_only: true
  }];
  // food source ids
  optional moego.models.order.v1.FoodSourceDef food_source = 18;

  // 24-hours period rule
  // charge type
  optional moego.models.order.v1.ServiceCharge.TimeBasedPricingType time_based_pricing_type = 19 [(validate.rules).enum = {
    not_in: [0]
    defined_only: true
  }];
  // multiple pets charge type
  optional moego.models.order.v1.ServiceCharge.MultiplePetsChargeType multiple_pets_charge_type = 20 [(validate.rules).enum = {
    not_in: [0]
    defined_only: true
  }];
  // 24-hours period rule
  repeated moego.models.order.v1.ServiceChargeExceedHourRule hourly_exceed_rules = 21 [(validate.rules).repeated = {
    min_items: 0
    max_items: 30
    items: {
      message: {required: true}
    }
  }];

  // whether the service charge is available for all services
  optional bool enable_service_filter = 22;

  // service filters
  repeated moego.models.order.v1.ServiceFilter service_filter_rules = 23;
}

// update service charge request params
message UpdateCompanyServiceChargeParams {
  // id, exist for update
  int64 id = 1 [(validate.rules).int64 = {gt: 0}];
  // name
  optional string name = 2 [(validate.rules).string = {
    min_len: 1
    max_len: 150
  }];
  // split method
  optional string description = 3 [(validate.rules).string = {
    min_len: 0
    max_len: 1000
  }];
  // price, must be positive
  double price = 4 [(validate.rules).double = {gte: 0}];
  // tax id, 0 or null means no tax
  int32 tax_id = 5 [(validate.rules).int32 = {gte: 0}];
  // is mandatory, preserved 8-19 for future use
  // deprecated by freeman since /2024/9/24, use auto_apply_status instead
  optional bool is_mandatory = 6 [deprecated = true];
  // is active
  optional bool is_active = 7;
  //is all location
  optional bool is_all_location = 8;
  // apply to upcoming
  optional bool apply_upcoming_appt = 9;
  //single location id
  optional int64 single_location_id = 10;
  //service charge location override data
  repeated moego.models.order.v1.ServiceChargeLocationOverride location_override_list = 11;
  // auto apply status
  optional moego.models.order.v1.ServiceCharge.AutoApplyStatus auto_apply_status = 12 [(validate.rules).enum = {
    not_in: [0]
    defined_only: true
  }];
  // auto apply condition
  optional moego.models.order.v1.ServiceCharge.AutoApplyCondition auto_apply_condition = 13 [(validate.rules).enum = {
    not_in: [0]
    defined_only: true
  }];
  // auto apply time, unit: minute
  optional int32 auto_apply_time = 14 [(validate.rules).int32 = {
    gte: 0
    lte: 1440
  }];
  // auto support service
  repeated models.offering.v1.ServiceItemType service_item_types = 15 [(validate.rules).repeated = {
    min_items: 0
    unique: true
    items: {
      enum: {
        defined_only: true
        not_in: [0]
      }
    }
  }];
  // auto apply time type
  optional moego.models.order.v1.ServiceCharge.AutoApplyTimeType auto_apply_time_type = 16 [(validate.rules).enum = {
    not_in: [0]
    defined_only: true
  }];
  // apply type
  optional moego.models.order.v1.ServiceCharge.ApplyType apply_type = 17 [(validate.rules).enum = {
    not_in: [0]
    defined_only: true
  }];
  // charge method
  optional moego.models.order.v1.ChargeMethod charge_method = 18 [(validate.rules).enum = {
    not_in: [0]
    defined_only: true
  }];
  // food source
  optional moego.models.order.v1.FoodSourceDef food_source = 19;

  // Exceed 24-hours period rule
  // charge type
  optional moego.models.order.v1.ServiceCharge.TimeBasedPricingType time_based_pricing_type = 20 [(validate.rules).enum = {
    not_in: [0]
    defined_only: true
  }];
  // multiple pets charge type
  optional moego.models.order.v1.ServiceCharge.MultiplePetsChargeType multiple_pets_charge_type = 21 [(validate.rules).enum = {
    not_in: [0]
    defined_only: true
  }];
  // 24-hours period rule
  repeated moego.models.order.v1.ServiceChargeExceedHourRule hourly_exceed_rules = 22 [(validate.rules).repeated = {
    min_items: 0
    max_items: 30
    items: {
      message: {required: true}
    }
  }];

  // whether the service charge is available for all services
  optional bool enable_service_filter = 23;

  // service filters
  repeated moego.models.order.v1.ServiceFilter service_filter_rules = 24;
}

// get Company service charge list response
message GetCompanyServiceChargeListResult {
  // service charge list
  repeated moego.models.order.v1.ServiceCharge service_charge = 1;
}

// list associated food source params
message ListSurchargeAssociatedFoodSourceParams {
  // exist service charge id
  optional int64 exist_service_charge_id = 1 [(validate.rules).int64 = {gt: 0}];
}

// list associated food source result
message ListSurchargeAssociatedFoodSourceResult {
  // food source ids
  repeated int64 food_source_ids = 1;
}

// service charge company api
service ServiceChargeCompanyService {
  // get service charge list
  rpc GetCompanyServiceChargeList(GetCompanyServiceChargeListParams) returns (GetCompanyServiceChargeListResult);
  // add service charge
  rpc AddCompanyServiceCharge(AddCompanyServiceChargeParams) returns (moego.models.order.v1.ServiceCharge);
  // update service charge
  rpc UpdateCompanyServiceCharge(UpdateCompanyServiceChargeParams) returns (moego.models.order.v1.ServiceCharge);
  // list associated food source
  rpc ListSurchargeAssociatedFoodSource(ListSurchargeAssociatedFoodSourceParams) returns (ListSurchargeAssociatedFoodSourceResult);
}
