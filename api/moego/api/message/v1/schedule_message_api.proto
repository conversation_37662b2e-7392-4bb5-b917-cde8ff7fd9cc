syntax = "proto3";

package moego.api.message.v1;

import "google/protobuf/timestamp.proto";
import "moego/models/business_customer/v1/business_customer_contact_models.proto";
import "moego/models/business_customer/v1/business_customer_models.proto";
import "moego/models/message/v1/auto_message_template_defs.proto";
import "moego/models/message/v1/auto_message_template_models.proto";
import "moego/models/message/v1/message_enums.proto";
import "moego/models/message/v1/schedule_message_defs.proto";
import "moego/models/message/v1/schedule_message_models.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/api/message/v1;messageapipb";
option java_multiple_files = true;
option java_package = "com.moego.idl.api.message.v1";

// Create a schedule message for SMS type request
message CreateScheduleMessageParams {
  // receipt customer id
  int64 customer_id = 1 [(validate.rules).int64.gt = 0];
  // message send method. It can be a custom message or an auto message
  oneof send_method {
    option (validate.required) = true;
    // custom content, the message manually entered by the user in the input box
    // the phone number to send can be specified.
    models.message.v1.ScheduleMessageCustomDef by_custom_content = 2;
    // auto message template for appointment
    // the phone number to send cannot be specified. The default is primary phone number.
    models.message.v1.AutoMessageAppointmentDef by_auto_message = 3;
  }
  // send out at, future delivery time
  google.protobuf.Timestamp send_out_at = 4;
  // method, default is SMS
  models.message.v1.Method method = 5 [(validate.rules).enum = {defined_only: true}];
}

// Create a schedule message for SMS type response
message CreateScheduleMessageResult {
  // schedule message id
  int64 schedule_message_id = 1 [(validate.rules).int64.gt = 0];
}

// Update a schedule message for SMS type params
message UpdateScheduleMessageParams {
  // schedule message id
  int64 schedule_message_id = 1 [(validate.rules).int64.gt = 0];
  // custom content, the message manually entered by the user in the input box
  // It can only be custom content, and the auto message is not allowed to be modified.
  // the phone number and content to send can be specified
  optional models.message.v1.ScheduleMessageCustomDef by_custom_content = 2;
  // send out at, delivery time
  optional google.protobuf.Timestamp send_out_at = 3;
}

// Update a schedule message for SMS type result
message UpdateScheduleMessageResult {}

// Delete a schedule message params
message DeleteScheduleMessageParams {
  // schedule message id
  int64 schedule_message_id = 1 [(validate.rules).int64.gt = 0];
}

// Delete a schedule message result
message DeleteScheduleMessageResult {}

// Get a schedule message params
message GetScheduleMessageParams {
  // schedule message id
  int64 schedule_message_id = 1 [(validate.rules).int64.gt = 0];
}

// Get a schedule message result
message GetScheduleMessageResult {
  // schedule message
  models.message.v1.ScheduleMessagePublicView schedule_message = 1;
  // customer view
  models.business_customer.v1.BusinessCustomerModelNameView customer = 2;
}

// Get scheduled messages params
message GetScheduleMessagesParams {
  // receipt customer id
  optional int64 customer_id = 1 [(validate.rules).int64.gt = 0];
  // method filter
  repeated models.message.v1.Method methods = 8 [(validate.rules).repeated = {
    items: {
      enum: {
        defined_only: true
        not_in: [0]
      }
    }
  }];
}

// Get scheduled messages result
message GetScheduleMessagesResult {
  // schedule message list
  repeated models.message.v1.ScheduleMessagePublicView schedule_messages = 1;
  // customer list
  repeated models.business_customer.v1.BusinessCustomerModelNameView customers = 2;
  // auto message template list
  repeated models.message.v1.AutoMessageTemplatePublicView auto_messages = 3;
  // customer contact list
  repeated models.business_customer.v1.BusinessCustomerContactPublicView contacts = 4;
}

// Send scheduled message params
message SendScheduleMessageParams {
  // schedule message id
  int64 schedule_message_id = 1 [(validate.rules).int64.gt = 0];
}

// Send scheduled message result
message SendScheduleMessageResult {}

// Used for SMS type scheduled task management
service ScheduleMessageService {
  // Create a schedule message
  rpc CreateScheduleMessage(CreateScheduleMessageParams) returns (CreateScheduleMessageResult);

  // Incremental update a schedule message
  rpc UpdateScheduleMessage(UpdateScheduleMessageParams) returns (UpdateScheduleMessageResult);

  // Delete a schedule message
  rpc DeleteScheduleMessage(DeleteScheduleMessageParams) returns (DeleteScheduleMessageResult);

  // Get a specific schedule message detail
  rpc GetScheduleMessage(GetScheduleMessageParams) returns (GetScheduleMessageResult);

  // Get the schedule message list of business or specific customers
  rpc GetScheduleMessages(GetScheduleMessagesParams) returns (GetScheduleMessagesResult);

  // Send the schedule message now
  rpc SendScheduleMessage(SendScheduleMessageParams) returns (SendScheduleMessageResult);
}
