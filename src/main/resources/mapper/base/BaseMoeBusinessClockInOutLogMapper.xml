<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.moego.svc.organization.mapper.base.BaseMoeBusinessClockInOutLogMapper">
  <resultMap id="BaseResultMap" type="com.moego.svc.organization.entity.MoeBusinessClockInOutLog">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="business_id" jdbcType="INTEGER" property="businessId" />
    <result column="account_id" jdbcType="INTEGER" property="accountId" />
    <result column="staff_id" jdbcType="INTEGER" property="staffId" />
    <result column="clock_date" jdbcType="DATE" property="clockDate" />
    <result column="clock_in_time" jdbcType="BIGINT" property="clockInTime" />
    <result column="clock_out_time" jdbcType="BIGINT" property="clockOutTime" />
    <result column="update_time" jdbcType="BIGINT" property="updateTime" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="company_id" jdbcType="BIGINT" property="companyId" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    id, business_id, account_id, staff_id, clock_date, clock_in_time, clock_out_time, 
    update_time, status, company_id
  </sql>
  <select id="selectByExample" parameterType="com.moego.svc.organization.entity.MoeBusinessClockInOutLogExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from moe_business_clock_in_out_log
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select 
    <include refid="Base_Column_List" />
    from moe_business_clock_in_out_log
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from moe_business_clock_in_out_log
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <delete id="deleteByExample" parameterType="com.moego.svc.organization.entity.MoeBusinessClockInOutLogExample">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from moe_business_clock_in_out_log
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.moego.svc.organization.entity.MoeBusinessClockInOutLog">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into moe_business_clock_in_out_log (business_id, account_id, staff_id, 
      clock_date, clock_in_time, clock_out_time, 
      update_time, status, company_id
      )
    values (#{businessId,jdbcType=INTEGER}, #{accountId,jdbcType=INTEGER}, #{staffId,jdbcType=INTEGER}, 
      #{clockDate,jdbcType=DATE}, #{clockInTime,jdbcType=BIGINT}, #{clockOutTime,jdbcType=BIGINT}, 
      #{updateTime,jdbcType=BIGINT}, #{status,jdbcType=TINYINT}, #{companyId,jdbcType=BIGINT}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.moego.svc.organization.entity.MoeBusinessClockInOutLog">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into moe_business_clock_in_out_log
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="businessId != null">
        business_id,
      </if>
      <if test="accountId != null">
        account_id,
      </if>
      <if test="staffId != null">
        staff_id,
      </if>
      <if test="clockDate != null">
        clock_date,
      </if>
      <if test="clockInTime != null">
        clock_in_time,
      </if>
      <if test="clockOutTime != null">
        clock_out_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="companyId != null">
        company_id,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="businessId != null">
        #{businessId,jdbcType=INTEGER},
      </if>
      <if test="accountId != null">
        #{accountId,jdbcType=INTEGER},
      </if>
      <if test="staffId != null">
        #{staffId,jdbcType=INTEGER},
      </if>
      <if test="clockDate != null">
        #{clockDate,jdbcType=DATE},
      </if>
      <if test="clockInTime != null">
        #{clockInTime,jdbcType=BIGINT},
      </if>
      <if test="clockOutTime != null">
        #{clockOutTime,jdbcType=BIGINT},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=BIGINT},
      </if>
      <if test="status != null">
        #{status,jdbcType=TINYINT},
      </if>
      <if test="companyId != null">
        #{companyId,jdbcType=BIGINT},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.moego.svc.organization.entity.MoeBusinessClockInOutLogExample" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select count(*) from moe_business_clock_in_out_log
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update moe_business_clock_in_out_log
    <set>
      <if test="row.id != null">
        id = #{row.id,jdbcType=INTEGER},
      </if>
      <if test="row.businessId != null">
        business_id = #{row.businessId,jdbcType=INTEGER},
      </if>
      <if test="row.accountId != null">
        account_id = #{row.accountId,jdbcType=INTEGER},
      </if>
      <if test="row.staffId != null">
        staff_id = #{row.staffId,jdbcType=INTEGER},
      </if>
      <if test="row.clockDate != null">
        clock_date = #{row.clockDate,jdbcType=DATE},
      </if>
      <if test="row.clockInTime != null">
        clock_in_time = #{row.clockInTime,jdbcType=BIGINT},
      </if>
      <if test="row.clockOutTime != null">
        clock_out_time = #{row.clockOutTime,jdbcType=BIGINT},
      </if>
      <if test="row.updateTime != null">
        update_time = #{row.updateTime,jdbcType=BIGINT},
      </if>
      <if test="row.status != null">
        status = #{row.status,jdbcType=TINYINT},
      </if>
      <if test="row.companyId != null">
        company_id = #{row.companyId,jdbcType=BIGINT},
      </if>
    </set>
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update moe_business_clock_in_out_log
    set id = #{row.id,jdbcType=INTEGER},
      business_id = #{row.businessId,jdbcType=INTEGER},
      account_id = #{row.accountId,jdbcType=INTEGER},
      staff_id = #{row.staffId,jdbcType=INTEGER},
      clock_date = #{row.clockDate,jdbcType=DATE},
      clock_in_time = #{row.clockInTime,jdbcType=BIGINT},
      clock_out_time = #{row.clockOutTime,jdbcType=BIGINT},
      update_time = #{row.updateTime,jdbcType=BIGINT},
      status = #{row.status,jdbcType=TINYINT},
      company_id = #{row.companyId,jdbcType=BIGINT}
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.moego.svc.organization.entity.MoeBusinessClockInOutLog">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update moe_business_clock_in_out_log
    <set>
      <if test="businessId != null">
        business_id = #{businessId,jdbcType=INTEGER},
      </if>
      <if test="accountId != null">
        account_id = #{accountId,jdbcType=INTEGER},
      </if>
      <if test="staffId != null">
        staff_id = #{staffId,jdbcType=INTEGER},
      </if>
      <if test="clockDate != null">
        clock_date = #{clockDate,jdbcType=DATE},
      </if>
      <if test="clockInTime != null">
        clock_in_time = #{clockInTime,jdbcType=BIGINT},
      </if>
      <if test="clockOutTime != null">
        clock_out_time = #{clockOutTime,jdbcType=BIGINT},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=BIGINT},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=TINYINT},
      </if>
      <if test="companyId != null">
        company_id = #{companyId,jdbcType=BIGINT},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.moego.svc.organization.entity.MoeBusinessClockInOutLog">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update moe_business_clock_in_out_log
    set business_id = #{businessId,jdbcType=INTEGER},
      account_id = #{accountId,jdbcType=INTEGER},
      staff_id = #{staffId,jdbcType=INTEGER},
      clock_date = #{clockDate,jdbcType=DATE},
      clock_in_time = #{clockInTime,jdbcType=BIGINT},
      clock_out_time = #{clockOutTime,jdbcType=BIGINT},
      update_time = #{updateTime,jdbcType=BIGINT},
      status = #{status,jdbcType=TINYINT},
      company_id = #{companyId,jdbcType=BIGINT}
    where id = #{id,jdbcType=INTEGER}
  </update>
</mapper>