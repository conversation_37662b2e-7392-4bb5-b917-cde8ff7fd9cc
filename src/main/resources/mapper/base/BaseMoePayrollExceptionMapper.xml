<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.moego.svc.organization.mapper.base.BaseMoePayrollExceptionMapper">
  <resultMap id="BaseResultMap" type="com.moego.svc.organization.entity.MoePayrollException">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="business_id" jdbcType="INTEGER" property="businessId" />
    <result column="service_id" jdbcType="INTEGER" property="serviceId" />
    <result column="rate" jdbcType="DECIMAL" property="rate" />
    <result column="is_all_staff" jdbcType="BIT" property="isAllStaff" />
    <result column="is_delete" jdbcType="BIT" property="isDelete" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="company_id" jdbcType="BIGINT" property="companyId" />
  </resultMap>
  <resultMap extends="BaseResultMap" id="ResultMapWithBLOBs" type="com.moego.svc.organization.entity.MoePayrollException">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <result column="staff_id_list" jdbcType="LONGVARCHAR" property="staffIdList" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    id, business_id, service_id, rate, is_all_staff, is_delete, create_time, update_time, 
    company_id
  </sql>
  <sql id="Blob_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    staff_id_list
  </sql>
  <select id="selectByExampleWithBLOBs" parameterType="com.moego.svc.organization.entity.MoePayrollExceptionExample" resultMap="ResultMapWithBLOBs">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from moe_payroll_exception
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByExample" parameterType="com.moego.svc.organization.entity.MoePayrollExceptionExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from moe_payroll_exception
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="ResultMapWithBLOBs">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select 
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from moe_payroll_exception
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from moe_payroll_exception
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <delete id="deleteByExample" parameterType="com.moego.svc.organization.entity.MoePayrollExceptionExample">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from moe_payroll_exception
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.moego.svc.organization.entity.MoePayrollException">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into moe_payroll_exception (business_id, service_id, rate, 
      is_all_staff, is_delete, create_time, 
      update_time, company_id, staff_id_list
      )
    values (#{businessId,jdbcType=INTEGER}, #{serviceId,jdbcType=INTEGER}, #{rate,jdbcType=DECIMAL}, 
      #{isAllStaff,jdbcType=BIT}, #{isDelete,jdbcType=BIT}, #{createTime,jdbcType=TIMESTAMP}, 
      #{updateTime,jdbcType=TIMESTAMP}, #{companyId,jdbcType=BIGINT}, #{staffIdList,jdbcType=LONGVARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.moego.svc.organization.entity.MoePayrollException">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into moe_payroll_exception
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="businessId != null">
        business_id,
      </if>
      <if test="serviceId != null">
        service_id,
      </if>
      <if test="rate != null">
        rate,
      </if>
      <if test="isAllStaff != null">
        is_all_staff,
      </if>
      <if test="isDelete != null">
        is_delete,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="companyId != null">
        company_id,
      </if>
      <if test="staffIdList != null">
        staff_id_list,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="businessId != null">
        #{businessId,jdbcType=INTEGER},
      </if>
      <if test="serviceId != null">
        #{serviceId,jdbcType=INTEGER},
      </if>
      <if test="rate != null">
        #{rate,jdbcType=DECIMAL},
      </if>
      <if test="isAllStaff != null">
        #{isAllStaff,jdbcType=BIT},
      </if>
      <if test="isDelete != null">
        #{isDelete,jdbcType=BIT},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="companyId != null">
        #{companyId,jdbcType=BIGINT},
      </if>
      <if test="staffIdList != null">
        #{staffIdList,jdbcType=LONGVARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.moego.svc.organization.entity.MoePayrollExceptionExample" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select count(*) from moe_payroll_exception
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update moe_payroll_exception
    <set>
      <if test="row.id != null">
        id = #{row.id,jdbcType=INTEGER},
      </if>
      <if test="row.businessId != null">
        business_id = #{row.businessId,jdbcType=INTEGER},
      </if>
      <if test="row.serviceId != null">
        service_id = #{row.serviceId,jdbcType=INTEGER},
      </if>
      <if test="row.rate != null">
        rate = #{row.rate,jdbcType=DECIMAL},
      </if>
      <if test="row.isAllStaff != null">
        is_all_staff = #{row.isAllStaff,jdbcType=BIT},
      </if>
      <if test="row.isDelete != null">
        is_delete = #{row.isDelete,jdbcType=BIT},
      </if>
      <if test="row.createTime != null">
        create_time = #{row.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="row.updateTime != null">
        update_time = #{row.updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="row.companyId != null">
        company_id = #{row.companyId,jdbcType=BIGINT},
      </if>
      <if test="row.staffIdList != null">
        staff_id_list = #{row.staffIdList,jdbcType=LONGVARCHAR},
      </if>
    </set>
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExampleWithBLOBs" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update moe_payroll_exception
    set id = #{row.id,jdbcType=INTEGER},
      business_id = #{row.businessId,jdbcType=INTEGER},
      service_id = #{row.serviceId,jdbcType=INTEGER},
      rate = #{row.rate,jdbcType=DECIMAL},
      is_all_staff = #{row.isAllStaff,jdbcType=BIT},
      is_delete = #{row.isDelete,jdbcType=BIT},
      create_time = #{row.createTime,jdbcType=TIMESTAMP},
      update_time = #{row.updateTime,jdbcType=TIMESTAMP},
      company_id = #{row.companyId,jdbcType=BIGINT},
      staff_id_list = #{row.staffIdList,jdbcType=LONGVARCHAR}
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update moe_payroll_exception
    set id = #{row.id,jdbcType=INTEGER},
      business_id = #{row.businessId,jdbcType=INTEGER},
      service_id = #{row.serviceId,jdbcType=INTEGER},
      rate = #{row.rate,jdbcType=DECIMAL},
      is_all_staff = #{row.isAllStaff,jdbcType=BIT},
      is_delete = #{row.isDelete,jdbcType=BIT},
      create_time = #{row.createTime,jdbcType=TIMESTAMP},
      update_time = #{row.updateTime,jdbcType=TIMESTAMP},
      company_id = #{row.companyId,jdbcType=BIGINT}
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.moego.svc.organization.entity.MoePayrollException">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update moe_payroll_exception
    <set>
      <if test="businessId != null">
        business_id = #{businessId,jdbcType=INTEGER},
      </if>
      <if test="serviceId != null">
        service_id = #{serviceId,jdbcType=INTEGER},
      </if>
      <if test="rate != null">
        rate = #{rate,jdbcType=DECIMAL},
      </if>
      <if test="isAllStaff != null">
        is_all_staff = #{isAllStaff,jdbcType=BIT},
      </if>
      <if test="isDelete != null">
        is_delete = #{isDelete,jdbcType=BIT},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="companyId != null">
        company_id = #{companyId,jdbcType=BIGINT},
      </if>
      <if test="staffIdList != null">
        staff_id_list = #{staffIdList,jdbcType=LONGVARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKeyWithBLOBs" parameterType="com.moego.svc.organization.entity.MoePayrollException">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update moe_payroll_exception
    set business_id = #{businessId,jdbcType=INTEGER},
      service_id = #{serviceId,jdbcType=INTEGER},
      rate = #{rate,jdbcType=DECIMAL},
      is_all_staff = #{isAllStaff,jdbcType=BIT},
      is_delete = #{isDelete,jdbcType=BIT},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      company_id = #{companyId,jdbcType=BIGINT},
      staff_id_list = #{staffIdList,jdbcType=LONGVARCHAR}
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.moego.svc.organization.entity.MoePayrollException">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update moe_payroll_exception
    set business_id = #{businessId,jdbcType=INTEGER},
      service_id = #{serviceId,jdbcType=INTEGER},
      rate = #{rate,jdbcType=DECIMAL},
      is_all_staff = #{isAllStaff,jdbcType=BIT},
      is_delete = #{isDelete,jdbcType=BIT},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      company_id = #{companyId,jdbcType=BIGINT}
    where id = #{id,jdbcType=INTEGER}
  </update>
</mapper>