<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.moego.svc.organization.mapper.base.BaseMoeStaffPayrollSettingMapper">
  <resultMap id="BaseResultMap" type="com.moego.svc.organization.entity.MoeStaffPayrollSetting">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="business_id" jdbcType="INTEGER" property="businessId" />
    <result column="staff_id" jdbcType="INTEGER" property="staffId" />
    <result column="service_commission_enable" jdbcType="BIT" property="serviceCommissionEnable" />
    <result column="service_commission_type" jdbcType="TINYINT" property="serviceCommissionType" />
    <result column="tier_type" jdbcType="TINYINT" property="tierType" />
    <result column="service_pay_rate" jdbcType="DECIMAL" property="servicePayRate" />
    <result column="addon_pay_rate" jdbcType="DECIMAL" property="addonPayRate" />
    <result column="hourly_commission_enable" jdbcType="BIT" property="hourlyCommissionEnable" />
    <result column="hourly_pay" jdbcType="DECIMAL" property="hourlyPay" />
    <result column="tips_commission_enable" jdbcType="BIT" property="tipsCommissionEnable" />
    <result column="tips_pay_rate" jdbcType="DECIMAL" property="tipsPayRate" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="company_id" jdbcType="BIGINT" property="companyId" />
  </resultMap>
  <resultMap extends="BaseResultMap" id="ResultMapWithBLOBs" type="com.moego.svc.organization.entity.MoeStaffPayrollSetting">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <result column="service_tier_rate_config" jdbcType="LONGVARCHAR" property="serviceTierRateConfig" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    id, business_id, staff_id, service_commission_enable, service_commission_type, tier_type, 
    service_pay_rate, addon_pay_rate, hourly_commission_enable, hourly_pay, tips_commission_enable, 
    tips_pay_rate, create_time, update_time, company_id
  </sql>
  <sql id="Blob_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    service_tier_rate_config
  </sql>
  <select id="selectByExampleWithBLOBs" parameterType="com.moego.svc.organization.entity.MoeStaffPayrollSettingExample" resultMap="ResultMapWithBLOBs">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from moe_staff_payroll_setting
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByExample" parameterType="com.moego.svc.organization.entity.MoeStaffPayrollSettingExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from moe_staff_payroll_setting
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="ResultMapWithBLOBs">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select 
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from moe_staff_payroll_setting
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from moe_staff_payroll_setting
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <delete id="deleteByExample" parameterType="com.moego.svc.organization.entity.MoeStaffPayrollSettingExample">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from moe_staff_payroll_setting
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.moego.svc.organization.entity.MoeStaffPayrollSetting">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into moe_staff_payroll_setting (business_id, staff_id, service_commission_enable, 
      service_commission_type, tier_type, service_pay_rate, 
      addon_pay_rate, hourly_commission_enable, hourly_pay, 
      tips_commission_enable, tips_pay_rate, create_time, 
      update_time, company_id, service_tier_rate_config
      )
    values (#{businessId,jdbcType=INTEGER}, #{staffId,jdbcType=INTEGER}, #{serviceCommissionEnable,jdbcType=BIT}, 
      #{serviceCommissionType,jdbcType=TINYINT}, #{tierType,jdbcType=TINYINT}, #{servicePayRate,jdbcType=DECIMAL}, 
      #{addonPayRate,jdbcType=DECIMAL}, #{hourlyCommissionEnable,jdbcType=BIT}, #{hourlyPay,jdbcType=DECIMAL}, 
      #{tipsCommissionEnable,jdbcType=BIT}, #{tipsPayRate,jdbcType=DECIMAL}, #{createTime,jdbcType=TIMESTAMP}, 
      #{updateTime,jdbcType=TIMESTAMP}, #{companyId,jdbcType=BIGINT}, #{serviceTierRateConfig,jdbcType=LONGVARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.moego.svc.organization.entity.MoeStaffPayrollSetting">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into moe_staff_payroll_setting
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="businessId != null">
        business_id,
      </if>
      <if test="staffId != null">
        staff_id,
      </if>
      <if test="serviceCommissionEnable != null">
        service_commission_enable,
      </if>
      <if test="serviceCommissionType != null">
        service_commission_type,
      </if>
      <if test="tierType != null">
        tier_type,
      </if>
      <if test="servicePayRate != null">
        service_pay_rate,
      </if>
      <if test="addonPayRate != null">
        addon_pay_rate,
      </if>
      <if test="hourlyCommissionEnable != null">
        hourly_commission_enable,
      </if>
      <if test="hourlyPay != null">
        hourly_pay,
      </if>
      <if test="tipsCommissionEnable != null">
        tips_commission_enable,
      </if>
      <if test="tipsPayRate != null">
        tips_pay_rate,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="companyId != null">
        company_id,
      </if>
      <if test="serviceTierRateConfig != null">
        service_tier_rate_config,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="businessId != null">
        #{businessId,jdbcType=INTEGER},
      </if>
      <if test="staffId != null">
        #{staffId,jdbcType=INTEGER},
      </if>
      <if test="serviceCommissionEnable != null">
        #{serviceCommissionEnable,jdbcType=BIT},
      </if>
      <if test="serviceCommissionType != null">
        #{serviceCommissionType,jdbcType=TINYINT},
      </if>
      <if test="tierType != null">
        #{tierType,jdbcType=TINYINT},
      </if>
      <if test="servicePayRate != null">
        #{servicePayRate,jdbcType=DECIMAL},
      </if>
      <if test="addonPayRate != null">
        #{addonPayRate,jdbcType=DECIMAL},
      </if>
      <if test="hourlyCommissionEnable != null">
        #{hourlyCommissionEnable,jdbcType=BIT},
      </if>
      <if test="hourlyPay != null">
        #{hourlyPay,jdbcType=DECIMAL},
      </if>
      <if test="tipsCommissionEnable != null">
        #{tipsCommissionEnable,jdbcType=BIT},
      </if>
      <if test="tipsPayRate != null">
        #{tipsPayRate,jdbcType=DECIMAL},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="companyId != null">
        #{companyId,jdbcType=BIGINT},
      </if>
      <if test="serviceTierRateConfig != null">
        #{serviceTierRateConfig,jdbcType=LONGVARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.moego.svc.organization.entity.MoeStaffPayrollSettingExample" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select count(*) from moe_staff_payroll_setting
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update moe_staff_payroll_setting
    <set>
      <if test="row.id != null">
        id = #{row.id,jdbcType=INTEGER},
      </if>
      <if test="row.businessId != null">
        business_id = #{row.businessId,jdbcType=INTEGER},
      </if>
      <if test="row.staffId != null">
        staff_id = #{row.staffId,jdbcType=INTEGER},
      </if>
      <if test="row.serviceCommissionEnable != null">
        service_commission_enable = #{row.serviceCommissionEnable,jdbcType=BIT},
      </if>
      <if test="row.serviceCommissionType != null">
        service_commission_type = #{row.serviceCommissionType,jdbcType=TINYINT},
      </if>
      <if test="row.tierType != null">
        tier_type = #{row.tierType,jdbcType=TINYINT},
      </if>
      <if test="row.servicePayRate != null">
        service_pay_rate = #{row.servicePayRate,jdbcType=DECIMAL},
      </if>
      <if test="row.addonPayRate != null">
        addon_pay_rate = #{row.addonPayRate,jdbcType=DECIMAL},
      </if>
      <if test="row.hourlyCommissionEnable != null">
        hourly_commission_enable = #{row.hourlyCommissionEnable,jdbcType=BIT},
      </if>
      <if test="row.hourlyPay != null">
        hourly_pay = #{row.hourlyPay,jdbcType=DECIMAL},
      </if>
      <if test="row.tipsCommissionEnable != null">
        tips_commission_enable = #{row.tipsCommissionEnable,jdbcType=BIT},
      </if>
      <if test="row.tipsPayRate != null">
        tips_pay_rate = #{row.tipsPayRate,jdbcType=DECIMAL},
      </if>
      <if test="row.createTime != null">
        create_time = #{row.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="row.updateTime != null">
        update_time = #{row.updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="row.companyId != null">
        company_id = #{row.companyId,jdbcType=BIGINT},
      </if>
      <if test="row.serviceTierRateConfig != null">
        service_tier_rate_config = #{row.serviceTierRateConfig,jdbcType=LONGVARCHAR},
      </if>
    </set>
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExampleWithBLOBs" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update moe_staff_payroll_setting
    set id = #{row.id,jdbcType=INTEGER},
      business_id = #{row.businessId,jdbcType=INTEGER},
      staff_id = #{row.staffId,jdbcType=INTEGER},
      service_commission_enable = #{row.serviceCommissionEnable,jdbcType=BIT},
      service_commission_type = #{row.serviceCommissionType,jdbcType=TINYINT},
      tier_type = #{row.tierType,jdbcType=TINYINT},
      service_pay_rate = #{row.servicePayRate,jdbcType=DECIMAL},
      addon_pay_rate = #{row.addonPayRate,jdbcType=DECIMAL},
      hourly_commission_enable = #{row.hourlyCommissionEnable,jdbcType=BIT},
      hourly_pay = #{row.hourlyPay,jdbcType=DECIMAL},
      tips_commission_enable = #{row.tipsCommissionEnable,jdbcType=BIT},
      tips_pay_rate = #{row.tipsPayRate,jdbcType=DECIMAL},
      create_time = #{row.createTime,jdbcType=TIMESTAMP},
      update_time = #{row.updateTime,jdbcType=TIMESTAMP},
      company_id = #{row.companyId,jdbcType=BIGINT},
      service_tier_rate_config = #{row.serviceTierRateConfig,jdbcType=LONGVARCHAR}
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update moe_staff_payroll_setting
    set id = #{row.id,jdbcType=INTEGER},
      business_id = #{row.businessId,jdbcType=INTEGER},
      staff_id = #{row.staffId,jdbcType=INTEGER},
      service_commission_enable = #{row.serviceCommissionEnable,jdbcType=BIT},
      service_commission_type = #{row.serviceCommissionType,jdbcType=TINYINT},
      tier_type = #{row.tierType,jdbcType=TINYINT},
      service_pay_rate = #{row.servicePayRate,jdbcType=DECIMAL},
      addon_pay_rate = #{row.addonPayRate,jdbcType=DECIMAL},
      hourly_commission_enable = #{row.hourlyCommissionEnable,jdbcType=BIT},
      hourly_pay = #{row.hourlyPay,jdbcType=DECIMAL},
      tips_commission_enable = #{row.tipsCommissionEnable,jdbcType=BIT},
      tips_pay_rate = #{row.tipsPayRate,jdbcType=DECIMAL},
      create_time = #{row.createTime,jdbcType=TIMESTAMP},
      update_time = #{row.updateTime,jdbcType=TIMESTAMP},
      company_id = #{row.companyId,jdbcType=BIGINT}
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.moego.svc.organization.entity.MoeStaffPayrollSetting">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update moe_staff_payroll_setting
    <set>
      <if test="businessId != null">
        business_id = #{businessId,jdbcType=INTEGER},
      </if>
      <if test="staffId != null">
        staff_id = #{staffId,jdbcType=INTEGER},
      </if>
      <if test="serviceCommissionEnable != null">
        service_commission_enable = #{serviceCommissionEnable,jdbcType=BIT},
      </if>
      <if test="serviceCommissionType != null">
        service_commission_type = #{serviceCommissionType,jdbcType=TINYINT},
      </if>
      <if test="tierType != null">
        tier_type = #{tierType,jdbcType=TINYINT},
      </if>
      <if test="servicePayRate != null">
        service_pay_rate = #{servicePayRate,jdbcType=DECIMAL},
      </if>
      <if test="addonPayRate != null">
        addon_pay_rate = #{addonPayRate,jdbcType=DECIMAL},
      </if>
      <if test="hourlyCommissionEnable != null">
        hourly_commission_enable = #{hourlyCommissionEnable,jdbcType=BIT},
      </if>
      <if test="hourlyPay != null">
        hourly_pay = #{hourlyPay,jdbcType=DECIMAL},
      </if>
      <if test="tipsCommissionEnable != null">
        tips_commission_enable = #{tipsCommissionEnable,jdbcType=BIT},
      </if>
      <if test="tipsPayRate != null">
        tips_pay_rate = #{tipsPayRate,jdbcType=DECIMAL},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="companyId != null">
        company_id = #{companyId,jdbcType=BIGINT},
      </if>
      <if test="serviceTierRateConfig != null">
        service_tier_rate_config = #{serviceTierRateConfig,jdbcType=LONGVARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKeyWithBLOBs" parameterType="com.moego.svc.organization.entity.MoeStaffPayrollSetting">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update moe_staff_payroll_setting
    set business_id = #{businessId,jdbcType=INTEGER},
      staff_id = #{staffId,jdbcType=INTEGER},
      service_commission_enable = #{serviceCommissionEnable,jdbcType=BIT},
      service_commission_type = #{serviceCommissionType,jdbcType=TINYINT},
      tier_type = #{tierType,jdbcType=TINYINT},
      service_pay_rate = #{servicePayRate,jdbcType=DECIMAL},
      addon_pay_rate = #{addonPayRate,jdbcType=DECIMAL},
      hourly_commission_enable = #{hourlyCommissionEnable,jdbcType=BIT},
      hourly_pay = #{hourlyPay,jdbcType=DECIMAL},
      tips_commission_enable = #{tipsCommissionEnable,jdbcType=BIT},
      tips_pay_rate = #{tipsPayRate,jdbcType=DECIMAL},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      company_id = #{companyId,jdbcType=BIGINT},
      service_tier_rate_config = #{serviceTierRateConfig,jdbcType=LONGVARCHAR}
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.moego.svc.organization.entity.MoeStaffPayrollSetting">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update moe_staff_payroll_setting
    set business_id = #{businessId,jdbcType=INTEGER},
      staff_id = #{staffId,jdbcType=INTEGER},
      service_commission_enable = #{serviceCommissionEnable,jdbcType=BIT},
      service_commission_type = #{serviceCommissionType,jdbcType=TINYINT},
      tier_type = #{tierType,jdbcType=TINYINT},
      service_pay_rate = #{servicePayRate,jdbcType=DECIMAL},
      addon_pay_rate = #{addonPayRate,jdbcType=DECIMAL},
      hourly_commission_enable = #{hourlyCommissionEnable,jdbcType=BIT},
      hourly_pay = #{hourlyPay,jdbcType=DECIMAL},
      tips_commission_enable = #{tipsCommissionEnable,jdbcType=BIT},
      tips_pay_rate = #{tipsPayRate,jdbcType=DECIMAL},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      company_id = #{companyId,jdbcType=BIGINT}
    where id = #{id,jdbcType=INTEGER}
  </update>
</mapper>