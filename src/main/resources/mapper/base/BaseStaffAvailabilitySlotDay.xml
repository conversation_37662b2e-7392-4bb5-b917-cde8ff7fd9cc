<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.moego.svc.organization.mapper.base.BaseStaffAvailabilitySlotDay">
  <resultMap id="BaseResultMap" type="com.moego.svc.organization.entity.StaffAvailabilitySlotDay">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="company_id" jdbcType="BIGINT" property="companyId" />
    <result column="business_id" jdbcType="BIGINT" property="businessId" />
    <result column="staff_id" jdbcType="BIGINT" property="staffId" />
    <result column="override_date" jdbcType="DATE" property="overrideDate" />
    <result column="is_available" jdbcType="BIT" property="isAvailable" />
    <result column="schedule_type" jdbcType="INTEGER" property="scheduleType" />
    <result column="day_of_week" jdbcType="INTEGER" property="dayOfWeek" />
    <result column="start_time" jdbcType="INTEGER" property="startTime" />
    <result column="end_time" jdbcType="INTEGER" property="endTime" />
    <result column="capacity" jdbcType="INTEGER" property="capacity" />
    <result column="created_at" jdbcType="TIMESTAMP" property="createdAt" />
    <result column="updated_at" jdbcType="TIMESTAMP" property="updatedAt" />
  </resultMap>
  <resultMap extends="BaseResultMap" id="ResultMapWithBLOBs" type="com.moego.svc.organization.entity.StaffAvailabilitySlotDay">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <result column="limit_ids" jdbcType="LONGVARCHAR" property="limitIds" typeHandler="com.moego.svc.organization.mapper.typehander.JsonLongListTypeHandler" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    id, company_id, business_id, staff_id, override_date, is_available, schedule_type, 
    day_of_week, start_time, end_time, capacity, created_at, updated_at
  </sql>
  <sql id="Blob_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    limit_ids
  </sql>
  <select id="selectByExampleWithBLOBs" parameterType="com.moego.svc.organization.entity.StaffAvailabilitySlotDayExample" resultMap="ResultMapWithBLOBs">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from staff_availability_slot_day
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByExample" parameterType="com.moego.svc.organization.entity.StaffAvailabilitySlotDayExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from staff_availability_slot_day
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="ResultMapWithBLOBs">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select 
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from staff_availability_slot_day
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from staff_availability_slot_day
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.moego.svc.organization.entity.StaffAvailabilitySlotDayExample">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from staff_availability_slot_day
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.moego.svc.organization.entity.StaffAvailabilitySlotDay">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into staff_availability_slot_day (company_id, business_id, staff_id, 
      override_date, is_available, schedule_type, 
      day_of_week, start_time, end_time, 
      capacity, created_at, updated_at, 
      limit_ids
      )
    values (#{companyId,jdbcType=BIGINT}, #{businessId,jdbcType=BIGINT}, #{staffId,jdbcType=BIGINT}, 
      #{overrideDate,jdbcType=DATE}, #{isAvailable,jdbcType=BIT}, #{scheduleType,jdbcType=INTEGER}, 
      #{dayOfWeek,jdbcType=INTEGER}, #{startTime,jdbcType=INTEGER}, #{endTime,jdbcType=INTEGER}, 
      #{capacity,jdbcType=INTEGER}, #{createdAt,jdbcType=TIMESTAMP}, #{updatedAt,jdbcType=TIMESTAMP}, 
      #{limitIds,jdbcType=LONGVARCHAR,typeHandler=com.moego.svc.organization.mapper.typehander.JsonLongListTypeHandler}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.moego.svc.organization.entity.StaffAvailabilitySlotDay">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into staff_availability_slot_day
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="companyId != null">
        company_id,
      </if>
      <if test="businessId != null">
        business_id,
      </if>
      <if test="staffId != null">
        staff_id,
      </if>
      <if test="overrideDate != null">
        override_date,
      </if>
      <if test="isAvailable != null">
        is_available,
      </if>
      <if test="scheduleType != null">
        schedule_type,
      </if>
      <if test="dayOfWeek != null">
        day_of_week,
      </if>
      <if test="startTime != null">
        start_time,
      </if>
      <if test="endTime != null">
        end_time,
      </if>
      <if test="capacity != null">
        capacity,
      </if>
      <if test="createdAt != null">
        created_at,
      </if>
      <if test="updatedAt != null">
        updated_at,
      </if>
      <if test="limitIds != null">
        limit_ids,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="companyId != null">
        #{companyId,jdbcType=BIGINT},
      </if>
      <if test="businessId != null">
        #{businessId,jdbcType=BIGINT},
      </if>
      <if test="staffId != null">
        #{staffId,jdbcType=BIGINT},
      </if>
      <if test="overrideDate != null">
        #{overrideDate,jdbcType=DATE},
      </if>
      <if test="isAvailable != null">
        #{isAvailable,jdbcType=BIT},
      </if>
      <if test="scheduleType != null">
        #{scheduleType,jdbcType=INTEGER},
      </if>
      <if test="dayOfWeek != null">
        #{dayOfWeek,jdbcType=INTEGER},
      </if>
      <if test="startTime != null">
        #{startTime,jdbcType=INTEGER},
      </if>
      <if test="endTime != null">
        #{endTime,jdbcType=INTEGER},
      </if>
      <if test="capacity != null">
        #{capacity,jdbcType=INTEGER},
      </if>
      <if test="createdAt != null">
        #{createdAt,jdbcType=TIMESTAMP},
      </if>
      <if test="updatedAt != null">
        #{updatedAt,jdbcType=TIMESTAMP},
      </if>
      <if test="limitIds != null">
        #{limitIds,jdbcType=LONGVARCHAR,typeHandler=com.moego.svc.organization.mapper.typehander.JsonLongListTypeHandler},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.moego.svc.organization.entity.StaffAvailabilitySlotDayExample" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select count(*) from staff_availability_slot_day
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update staff_availability_slot_day
    <set>
      <if test="row.id != null">
        id = #{row.id,jdbcType=BIGINT},
      </if>
      <if test="row.companyId != null">
        company_id = #{row.companyId,jdbcType=BIGINT},
      </if>
      <if test="row.businessId != null">
        business_id = #{row.businessId,jdbcType=BIGINT},
      </if>
      <if test="row.staffId != null">
        staff_id = #{row.staffId,jdbcType=BIGINT},
      </if>
      <if test="row.overrideDate != null">
        override_date = #{row.overrideDate,jdbcType=DATE},
      </if>
      <if test="row.isAvailable != null">
        is_available = #{row.isAvailable,jdbcType=BIT},
      </if>
      <if test="row.scheduleType != null">
        schedule_type = #{row.scheduleType,jdbcType=INTEGER},
      </if>
      <if test="row.dayOfWeek != null">
        day_of_week = #{row.dayOfWeek,jdbcType=INTEGER},
      </if>
      <if test="row.startTime != null">
        start_time = #{row.startTime,jdbcType=INTEGER},
      </if>
      <if test="row.endTime != null">
        end_time = #{row.endTime,jdbcType=INTEGER},
      </if>
      <if test="row.capacity != null">
        capacity = #{row.capacity,jdbcType=INTEGER},
      </if>
      <if test="row.createdAt != null">
        created_at = #{row.createdAt,jdbcType=TIMESTAMP},
      </if>
      <if test="row.updatedAt != null">
        updated_at = #{row.updatedAt,jdbcType=TIMESTAMP},
      </if>
      <if test="row.limitIds != null">
        limit_ids = #{row.limitIds,jdbcType=LONGVARCHAR,typeHandler=com.moego.svc.organization.mapper.typehander.JsonLongListTypeHandler},
      </if>
    </set>
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExampleWithBLOBs" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update staff_availability_slot_day
    set id = #{row.id,jdbcType=BIGINT},
      company_id = #{row.companyId,jdbcType=BIGINT},
      business_id = #{row.businessId,jdbcType=BIGINT},
      staff_id = #{row.staffId,jdbcType=BIGINT},
      override_date = #{row.overrideDate,jdbcType=DATE},
      is_available = #{row.isAvailable,jdbcType=BIT},
      schedule_type = #{row.scheduleType,jdbcType=INTEGER},
      day_of_week = #{row.dayOfWeek,jdbcType=INTEGER},
      start_time = #{row.startTime,jdbcType=INTEGER},
      end_time = #{row.endTime,jdbcType=INTEGER},
      capacity = #{row.capacity,jdbcType=INTEGER},
      created_at = #{row.createdAt,jdbcType=TIMESTAMP},
      updated_at = #{row.updatedAt,jdbcType=TIMESTAMP},
      limit_ids = #{row.limitIds,jdbcType=LONGVARCHAR,typeHandler=com.moego.svc.organization.mapper.typehander.JsonLongListTypeHandler}
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update staff_availability_slot_day
    set id = #{row.id,jdbcType=BIGINT},
      company_id = #{row.companyId,jdbcType=BIGINT},
      business_id = #{row.businessId,jdbcType=BIGINT},
      staff_id = #{row.staffId,jdbcType=BIGINT},
      override_date = #{row.overrideDate,jdbcType=DATE},
      is_available = #{row.isAvailable,jdbcType=BIT},
      schedule_type = #{row.scheduleType,jdbcType=INTEGER},
      day_of_week = #{row.dayOfWeek,jdbcType=INTEGER},
      start_time = #{row.startTime,jdbcType=INTEGER},
      end_time = #{row.endTime,jdbcType=INTEGER},
      capacity = #{row.capacity,jdbcType=INTEGER},
      created_at = #{row.createdAt,jdbcType=TIMESTAMP},
      updated_at = #{row.updatedAt,jdbcType=TIMESTAMP}
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.moego.svc.organization.entity.StaffAvailabilitySlotDay">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update staff_availability_slot_day
    <set>
      <if test="companyId != null">
        company_id = #{companyId,jdbcType=BIGINT},
      </if>
      <if test="businessId != null">
        business_id = #{businessId,jdbcType=BIGINT},
      </if>
      <if test="staffId != null">
        staff_id = #{staffId,jdbcType=BIGINT},
      </if>
      <if test="overrideDate != null">
        override_date = #{overrideDate,jdbcType=DATE},
      </if>
      <if test="isAvailable != null">
        is_available = #{isAvailable,jdbcType=BIT},
      </if>
      <if test="scheduleType != null">
        schedule_type = #{scheduleType,jdbcType=INTEGER},
      </if>
      <if test="dayOfWeek != null">
        day_of_week = #{dayOfWeek,jdbcType=INTEGER},
      </if>
      <if test="startTime != null">
        start_time = #{startTime,jdbcType=INTEGER},
      </if>
      <if test="endTime != null">
        end_time = #{endTime,jdbcType=INTEGER},
      </if>
      <if test="capacity != null">
        capacity = #{capacity,jdbcType=INTEGER},
      </if>
      <if test="createdAt != null">
        created_at = #{createdAt,jdbcType=TIMESTAMP},
      </if>
      <if test="updatedAt != null">
        updated_at = #{updatedAt,jdbcType=TIMESTAMP},
      </if>
      <if test="limitIds != null">
        limit_ids = #{limitIds,jdbcType=LONGVARCHAR,typeHandler=com.moego.svc.organization.mapper.typehander.JsonLongListTypeHandler},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKeyWithBLOBs" parameterType="com.moego.svc.organization.entity.StaffAvailabilitySlotDay">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update staff_availability_slot_day
    set company_id = #{companyId,jdbcType=BIGINT},
      business_id = #{businessId,jdbcType=BIGINT},
      staff_id = #{staffId,jdbcType=BIGINT},
      override_date = #{overrideDate,jdbcType=DATE},
      is_available = #{isAvailable,jdbcType=BIT},
      schedule_type = #{scheduleType,jdbcType=INTEGER},
      day_of_week = #{dayOfWeek,jdbcType=INTEGER},
      start_time = #{startTime,jdbcType=INTEGER},
      end_time = #{endTime,jdbcType=INTEGER},
      capacity = #{capacity,jdbcType=INTEGER},
      created_at = #{createdAt,jdbcType=TIMESTAMP},
      updated_at = #{updatedAt,jdbcType=TIMESTAMP},
      limit_ids = #{limitIds,jdbcType=LONGVARCHAR,typeHandler=com.moego.svc.organization.mapper.typehander.JsonLongListTypeHandler}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.moego.svc.organization.entity.StaffAvailabilitySlotDay">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update staff_availability_slot_day
    set company_id = #{companyId,jdbcType=BIGINT},
      business_id = #{businessId,jdbcType=BIGINT},
      staff_id = #{staffId,jdbcType=BIGINT},
      override_date = #{overrideDate,jdbcType=DATE},
      is_available = #{isAvailable,jdbcType=BIT},
      schedule_type = #{scheduleType,jdbcType=INTEGER},
      day_of_week = #{dayOfWeek,jdbcType=INTEGER},
      start_time = #{startTime,jdbcType=INTEGER},
      end_time = #{endTime,jdbcType=INTEGER},
      capacity = #{capacity,jdbcType=INTEGER},
      created_at = #{createdAt,jdbcType=TIMESTAMP},
      updated_at = #{updatedAt,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>