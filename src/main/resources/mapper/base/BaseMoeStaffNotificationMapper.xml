<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.moego.svc.organization.mapper.base.BaseMoeStaffNotificationMapper">
  <resultMap id="BaseResultMap" type="com.moego.svc.organization.entity.MoeStaffNotification">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="staff_id" jdbcType="INTEGER" property="staffId" />
    <result column="booking_created" jdbcType="TINYINT" property="bookingCreated" />
    <result column="booking_cancelled" jdbcType="TINYINT" property="bookingCancelled" />
    <result column="booking_rescheduled" jdbcType="TINYINT" property="bookingRescheduled" />
    <result column="new_booking" jdbcType="TINYINT" property="newBooking" />
    <result column="new_intake_form" jdbcType="TINYINT" property="newIntakeForm" />
    <result column="agreement_signed" jdbcType="TINYINT" property="agreementSigned" />
    <result column="invoice_paid" jdbcType="TINYINT" property="invoicePaid" />
    <result column="review_submitted" jdbcType="TINYINT" property="reviewSubmitted" />
    <result column="push_calendar_switch" jdbcType="TINYINT" property="pushCalendarSwitch" />
    <result column="before_mins" jdbcType="INTEGER" property="beforeMins" />
    <result column="create_time" jdbcType="BIGINT" property="createTime" />
    <result column="update_time" jdbcType="BIGINT" property="updateTime" />
    <result column="new_abandoned_bookings" jdbcType="TINYINT" property="newAbandonedBookings" />
    <result column="company_id" jdbcType="BIGINT" property="companyId" />
    <result column="business_id" jdbcType="BIGINT" property="businessId" />
    <result column="assigned_task" jdbcType="TINYINT" property="assignedTask" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    id, staff_id, booking_created, booking_cancelled, booking_rescheduled, new_booking, 
    new_intake_form, agreement_signed, invoice_paid, review_submitted, push_calendar_switch, 
    before_mins, create_time, update_time, new_abandoned_bookings, company_id, business_id, 
    assigned_task
  </sql>
  <select id="selectByExample" parameterType="com.moego.svc.organization.entity.MoeStaffNotificationExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from moe_staff_notification
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select 
    <include refid="Base_Column_List" />
    from moe_staff_notification
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from moe_staff_notification
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <delete id="deleteByExample" parameterType="com.moego.svc.organization.entity.MoeStaffNotificationExample">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from moe_staff_notification
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.moego.svc.organization.entity.MoeStaffNotification">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into moe_staff_notification (staff_id, booking_created, booking_cancelled, 
      booking_rescheduled, new_booking, new_intake_form, 
      agreement_signed, invoice_paid, review_submitted, 
      push_calendar_switch, before_mins, create_time, 
      update_time, new_abandoned_bookings, company_id, 
      business_id, assigned_task)
    values (#{staffId,jdbcType=INTEGER}, #{bookingCreated,jdbcType=TINYINT}, #{bookingCancelled,jdbcType=TINYINT}, 
      #{bookingRescheduled,jdbcType=TINYINT}, #{newBooking,jdbcType=TINYINT}, #{newIntakeForm,jdbcType=TINYINT}, 
      #{agreementSigned,jdbcType=TINYINT}, #{invoicePaid,jdbcType=TINYINT}, #{reviewSubmitted,jdbcType=TINYINT}, 
      #{pushCalendarSwitch,jdbcType=TINYINT}, #{beforeMins,jdbcType=INTEGER}, #{createTime,jdbcType=BIGINT}, 
      #{updateTime,jdbcType=BIGINT}, #{newAbandonedBookings,jdbcType=TINYINT}, #{companyId,jdbcType=BIGINT}, 
      #{businessId,jdbcType=BIGINT}, #{assignedTask,jdbcType=TINYINT})
  </insert>
  <insert id="insertSelective" parameterType="com.moego.svc.organization.entity.MoeStaffNotification">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into moe_staff_notification
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="staffId != null">
        staff_id,
      </if>
      <if test="bookingCreated != null">
        booking_created,
      </if>
      <if test="bookingCancelled != null">
        booking_cancelled,
      </if>
      <if test="bookingRescheduled != null">
        booking_rescheduled,
      </if>
      <if test="newBooking != null">
        new_booking,
      </if>
      <if test="newIntakeForm != null">
        new_intake_form,
      </if>
      <if test="agreementSigned != null">
        agreement_signed,
      </if>
      <if test="invoicePaid != null">
        invoice_paid,
      </if>
      <if test="reviewSubmitted != null">
        review_submitted,
      </if>
      <if test="pushCalendarSwitch != null">
        push_calendar_switch,
      </if>
      <if test="beforeMins != null">
        before_mins,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="newAbandonedBookings != null">
        new_abandoned_bookings,
      </if>
      <if test="companyId != null">
        company_id,
      </if>
      <if test="businessId != null">
        business_id,
      </if>
      <if test="assignedTask != null">
        assigned_task,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="staffId != null">
        #{staffId,jdbcType=INTEGER},
      </if>
      <if test="bookingCreated != null">
        #{bookingCreated,jdbcType=TINYINT},
      </if>
      <if test="bookingCancelled != null">
        #{bookingCancelled,jdbcType=TINYINT},
      </if>
      <if test="bookingRescheduled != null">
        #{bookingRescheduled,jdbcType=TINYINT},
      </if>
      <if test="newBooking != null">
        #{newBooking,jdbcType=TINYINT},
      </if>
      <if test="newIntakeForm != null">
        #{newIntakeForm,jdbcType=TINYINT},
      </if>
      <if test="agreementSigned != null">
        #{agreementSigned,jdbcType=TINYINT},
      </if>
      <if test="invoicePaid != null">
        #{invoicePaid,jdbcType=TINYINT},
      </if>
      <if test="reviewSubmitted != null">
        #{reviewSubmitted,jdbcType=TINYINT},
      </if>
      <if test="pushCalendarSwitch != null">
        #{pushCalendarSwitch,jdbcType=TINYINT},
      </if>
      <if test="beforeMins != null">
        #{beforeMins,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=BIGINT},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=BIGINT},
      </if>
      <if test="newAbandonedBookings != null">
        #{newAbandonedBookings,jdbcType=TINYINT},
      </if>
      <if test="companyId != null">
        #{companyId,jdbcType=BIGINT},
      </if>
      <if test="businessId != null">
        #{businessId,jdbcType=BIGINT},
      </if>
      <if test="assignedTask != null">
        #{assignedTask,jdbcType=TINYINT},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.moego.svc.organization.entity.MoeStaffNotificationExample" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select count(*) from moe_staff_notification
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update moe_staff_notification
    <set>
      <if test="row.id != null">
        id = #{row.id,jdbcType=INTEGER},
      </if>
      <if test="row.staffId != null">
        staff_id = #{row.staffId,jdbcType=INTEGER},
      </if>
      <if test="row.bookingCreated != null">
        booking_created = #{row.bookingCreated,jdbcType=TINYINT},
      </if>
      <if test="row.bookingCancelled != null">
        booking_cancelled = #{row.bookingCancelled,jdbcType=TINYINT},
      </if>
      <if test="row.bookingRescheduled != null">
        booking_rescheduled = #{row.bookingRescheduled,jdbcType=TINYINT},
      </if>
      <if test="row.newBooking != null">
        new_booking = #{row.newBooking,jdbcType=TINYINT},
      </if>
      <if test="row.newIntakeForm != null">
        new_intake_form = #{row.newIntakeForm,jdbcType=TINYINT},
      </if>
      <if test="row.agreementSigned != null">
        agreement_signed = #{row.agreementSigned,jdbcType=TINYINT},
      </if>
      <if test="row.invoicePaid != null">
        invoice_paid = #{row.invoicePaid,jdbcType=TINYINT},
      </if>
      <if test="row.reviewSubmitted != null">
        review_submitted = #{row.reviewSubmitted,jdbcType=TINYINT},
      </if>
      <if test="row.pushCalendarSwitch != null">
        push_calendar_switch = #{row.pushCalendarSwitch,jdbcType=TINYINT},
      </if>
      <if test="row.beforeMins != null">
        before_mins = #{row.beforeMins,jdbcType=INTEGER},
      </if>
      <if test="row.createTime != null">
        create_time = #{row.createTime,jdbcType=BIGINT},
      </if>
      <if test="row.updateTime != null">
        update_time = #{row.updateTime,jdbcType=BIGINT},
      </if>
      <if test="row.newAbandonedBookings != null">
        new_abandoned_bookings = #{row.newAbandonedBookings,jdbcType=TINYINT},
      </if>
      <if test="row.companyId != null">
        company_id = #{row.companyId,jdbcType=BIGINT},
      </if>
      <if test="row.businessId != null">
        business_id = #{row.businessId,jdbcType=BIGINT},
      </if>
      <if test="row.assignedTask != null">
        assigned_task = #{row.assignedTask,jdbcType=TINYINT},
      </if>
    </set>
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update moe_staff_notification
    set id = #{row.id,jdbcType=INTEGER},
      staff_id = #{row.staffId,jdbcType=INTEGER},
      booking_created = #{row.bookingCreated,jdbcType=TINYINT},
      booking_cancelled = #{row.bookingCancelled,jdbcType=TINYINT},
      booking_rescheduled = #{row.bookingRescheduled,jdbcType=TINYINT},
      new_booking = #{row.newBooking,jdbcType=TINYINT},
      new_intake_form = #{row.newIntakeForm,jdbcType=TINYINT},
      agreement_signed = #{row.agreementSigned,jdbcType=TINYINT},
      invoice_paid = #{row.invoicePaid,jdbcType=TINYINT},
      review_submitted = #{row.reviewSubmitted,jdbcType=TINYINT},
      push_calendar_switch = #{row.pushCalendarSwitch,jdbcType=TINYINT},
      before_mins = #{row.beforeMins,jdbcType=INTEGER},
      create_time = #{row.createTime,jdbcType=BIGINT},
      update_time = #{row.updateTime,jdbcType=BIGINT},
      new_abandoned_bookings = #{row.newAbandonedBookings,jdbcType=TINYINT},
      company_id = #{row.companyId,jdbcType=BIGINT},
      business_id = #{row.businessId,jdbcType=BIGINT},
      assigned_task = #{row.assignedTask,jdbcType=TINYINT}
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.moego.svc.organization.entity.MoeStaffNotification">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update moe_staff_notification
    <set>
      <if test="staffId != null">
        staff_id = #{staffId,jdbcType=INTEGER},
      </if>
      <if test="bookingCreated != null">
        booking_created = #{bookingCreated,jdbcType=TINYINT},
      </if>
      <if test="bookingCancelled != null">
        booking_cancelled = #{bookingCancelled,jdbcType=TINYINT},
      </if>
      <if test="bookingRescheduled != null">
        booking_rescheduled = #{bookingRescheduled,jdbcType=TINYINT},
      </if>
      <if test="newBooking != null">
        new_booking = #{newBooking,jdbcType=TINYINT},
      </if>
      <if test="newIntakeForm != null">
        new_intake_form = #{newIntakeForm,jdbcType=TINYINT},
      </if>
      <if test="agreementSigned != null">
        agreement_signed = #{agreementSigned,jdbcType=TINYINT},
      </if>
      <if test="invoicePaid != null">
        invoice_paid = #{invoicePaid,jdbcType=TINYINT},
      </if>
      <if test="reviewSubmitted != null">
        review_submitted = #{reviewSubmitted,jdbcType=TINYINT},
      </if>
      <if test="pushCalendarSwitch != null">
        push_calendar_switch = #{pushCalendarSwitch,jdbcType=TINYINT},
      </if>
      <if test="beforeMins != null">
        before_mins = #{beforeMins,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=BIGINT},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=BIGINT},
      </if>
      <if test="newAbandonedBookings != null">
        new_abandoned_bookings = #{newAbandonedBookings,jdbcType=TINYINT},
      </if>
      <if test="companyId != null">
        company_id = #{companyId,jdbcType=BIGINT},
      </if>
      <if test="businessId != null">
        business_id = #{businessId,jdbcType=BIGINT},
      </if>
      <if test="assignedTask != null">
        assigned_task = #{assignedTask,jdbcType=TINYINT},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.moego.svc.organization.entity.MoeStaffNotification">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update moe_staff_notification
    set staff_id = #{staffId,jdbcType=INTEGER},
      booking_created = #{bookingCreated,jdbcType=TINYINT},
      booking_cancelled = #{bookingCancelled,jdbcType=TINYINT},
      booking_rescheduled = #{bookingRescheduled,jdbcType=TINYINT},
      new_booking = #{newBooking,jdbcType=TINYINT},
      new_intake_form = #{newIntakeForm,jdbcType=TINYINT},
      agreement_signed = #{agreementSigned,jdbcType=TINYINT},
      invoice_paid = #{invoicePaid,jdbcType=TINYINT},
      review_submitted = #{reviewSubmitted,jdbcType=TINYINT},
      push_calendar_switch = #{pushCalendarSwitch,jdbcType=TINYINT},
      before_mins = #{beforeMins,jdbcType=INTEGER},
      create_time = #{createTime,jdbcType=BIGINT},
      update_time = #{updateTime,jdbcType=BIGINT},
      new_abandoned_bookings = #{newAbandonedBookings,jdbcType=TINYINT},
      company_id = #{companyId,jdbcType=BIGINT},
      business_id = #{businessId,jdbcType=BIGINT},
      assigned_task = #{assignedTask,jdbcType=TINYINT}
    where id = #{id,jdbcType=INTEGER}
  </update>
</mapper>