<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.moego.svc.organization.mapper.base.BaseMoeClockInOutSettingMapper">
  <resultMap id="BaseResultMap" type="com.moego.svc.organization.entity.MoeClockInOutSetting">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="company_id" jdbcType="BIGINT" property="companyId" />
    <result column="enable_clock_in_out" jdbcType="BIT" property="enableClockInOut" />
    <result column="clock_in_out_notify" jdbcType="BIT" property="clockInOutNotify" />
    <result column="created_at" jdbcType="TIMESTAMP" property="createdAt" />
    <result column="updated_at" jdbcType="TIMESTAMP" property="updatedAt" />
    <result column="updated_by" jdbcType="BIGINT" property="updatedBy" />
    <result column="enable_clock_in_out_overnight" jdbcType="BIT" property="enableClockInOutOvernight" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    id, company_id, enable_clock_in_out, clock_in_out_notify, created_at, updated_at, 
    updated_by, enable_clock_in_out_overnight
  </sql>
  <select id="selectByExample" parameterType="com.moego.svc.organization.entity.MoeClockInOutSettingExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from moe_clock_in_out_setting
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select 
    <include refid="Base_Column_List" />
    from moe_clock_in_out_setting
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from moe_clock_in_out_setting
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.moego.svc.organization.entity.MoeClockInOutSettingExample">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from moe_clock_in_out_setting
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.moego.svc.organization.entity.MoeClockInOutSetting">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into moe_clock_in_out_setting (company_id, enable_clock_in_out, clock_in_out_notify, 
      created_at, updated_at, updated_by, 
      enable_clock_in_out_overnight)
    values (#{companyId,jdbcType=BIGINT}, #{enableClockInOut,jdbcType=BIT}, #{clockInOutNotify,jdbcType=BIT}, 
      #{createdAt,jdbcType=TIMESTAMP}, #{updatedAt,jdbcType=TIMESTAMP}, #{updatedBy,jdbcType=BIGINT}, 
      #{enableClockInOutOvernight,jdbcType=BIT})
  </insert>
  <insert id="insertSelective" parameterType="com.moego.svc.organization.entity.MoeClockInOutSetting">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into moe_clock_in_out_setting
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="companyId != null">
        company_id,
      </if>
      <if test="enableClockInOut != null">
        enable_clock_in_out,
      </if>
      <if test="clockInOutNotify != null">
        clock_in_out_notify,
      </if>
      <if test="createdAt != null">
        created_at,
      </if>
      <if test="updatedAt != null">
        updated_at,
      </if>
      <if test="updatedBy != null">
        updated_by,
      </if>
      <if test="enableClockInOutOvernight != null">
        enable_clock_in_out_overnight,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="companyId != null">
        #{companyId,jdbcType=BIGINT},
      </if>
      <if test="enableClockInOut != null">
        #{enableClockInOut,jdbcType=BIT},
      </if>
      <if test="clockInOutNotify != null">
        #{clockInOutNotify,jdbcType=BIT},
      </if>
      <if test="createdAt != null">
        #{createdAt,jdbcType=TIMESTAMP},
      </if>
      <if test="updatedAt != null">
        #{updatedAt,jdbcType=TIMESTAMP},
      </if>
      <if test="updatedBy != null">
        #{updatedBy,jdbcType=BIGINT},
      </if>
      <if test="enableClockInOutOvernight != null">
        #{enableClockInOutOvernight,jdbcType=BIT},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.moego.svc.organization.entity.MoeClockInOutSettingExample" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select count(*) from moe_clock_in_out_setting
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update moe_clock_in_out_setting
    <set>
      <if test="row.id != null">
        id = #{row.id,jdbcType=BIGINT},
      </if>
      <if test="row.companyId != null">
        company_id = #{row.companyId,jdbcType=BIGINT},
      </if>
      <if test="row.enableClockInOut != null">
        enable_clock_in_out = #{row.enableClockInOut,jdbcType=BIT},
      </if>
      <if test="row.clockInOutNotify != null">
        clock_in_out_notify = #{row.clockInOutNotify,jdbcType=BIT},
      </if>
      <if test="row.createdAt != null">
        created_at = #{row.createdAt,jdbcType=TIMESTAMP},
      </if>
      <if test="row.updatedAt != null">
        updated_at = #{row.updatedAt,jdbcType=TIMESTAMP},
      </if>
      <if test="row.updatedBy != null">
        updated_by = #{row.updatedBy,jdbcType=BIGINT},
      </if>
      <if test="row.enableClockInOutOvernight != null">
        enable_clock_in_out_overnight = #{row.enableClockInOutOvernight,jdbcType=BIT},
      </if>
    </set>
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update moe_clock_in_out_setting
    set id = #{row.id,jdbcType=BIGINT},
      company_id = #{row.companyId,jdbcType=BIGINT},
      enable_clock_in_out = #{row.enableClockInOut,jdbcType=BIT},
      clock_in_out_notify = #{row.clockInOutNotify,jdbcType=BIT},
      created_at = #{row.createdAt,jdbcType=TIMESTAMP},
      updated_at = #{row.updatedAt,jdbcType=TIMESTAMP},
      updated_by = #{row.updatedBy,jdbcType=BIGINT},
      enable_clock_in_out_overnight = #{row.enableClockInOutOvernight,jdbcType=BIT}
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.moego.svc.organization.entity.MoeClockInOutSetting">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update moe_clock_in_out_setting
    <set>
      <if test="companyId != null">
        company_id = #{companyId,jdbcType=BIGINT},
      </if>
      <if test="enableClockInOut != null">
        enable_clock_in_out = #{enableClockInOut,jdbcType=BIT},
      </if>
      <if test="clockInOutNotify != null">
        clock_in_out_notify = #{clockInOutNotify,jdbcType=BIT},
      </if>
      <if test="createdAt != null">
        created_at = #{createdAt,jdbcType=TIMESTAMP},
      </if>
      <if test="updatedAt != null">
        updated_at = #{updatedAt,jdbcType=TIMESTAMP},
      </if>
      <if test="updatedBy != null">
        updated_by = #{updatedBy,jdbcType=BIGINT},
      </if>
      <if test="enableClockInOutOvernight != null">
        enable_clock_in_out_overnight = #{enableClockInOutOvernight,jdbcType=BIT},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.moego.svc.organization.entity.MoeClockInOutSetting">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update moe_clock_in_out_setting
    set company_id = #{companyId,jdbcType=BIGINT},
      enable_clock_in_out = #{enableClockInOut,jdbcType=BIT},
      clock_in_out_notify = #{clockInOutNotify,jdbcType=BIT},
      created_at = #{createdAt,jdbcType=TIMESTAMP},
      updated_at = #{updatedAt,jdbcType=TIMESTAMP},
      updated_by = #{updatedBy,jdbcType=BIGINT},
      enable_clock_in_out_overnight = #{enableClockInOutOvernight,jdbcType=BIT}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>