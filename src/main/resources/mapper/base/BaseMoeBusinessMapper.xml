<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.moego.svc.organization.mapper.base.BaseMoeBusinessMapper">
  <resultMap id="BaseResultMap" type="com.moego.svc.organization.entity.MoeBusiness">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="company_id" jdbcType="INTEGER" property="companyId" />
    <result column="business_name" jdbcType="VARCHAR" property="businessName" />
    <result column="phone_number" jdbcType="VARCHAR" property="phoneNumber" />
    <result column="avatar_path" jdbcType="VARCHAR" property="avatarPath" />
    <result column="website" jdbcType="VARCHAR" property="website" />
    <result column="address" jdbcType="VARCHAR" property="address" />
    <result column="address1" jdbcType="VARCHAR" property="address1" />
    <result column="address2" jdbcType="VARCHAR" property="address2" />
    <result column="address_city" jdbcType="VARCHAR" property="addressCity" />
    <result column="address_state" jdbcType="VARCHAR" property="addressState" />
    <result column="address_zipcode" jdbcType="VARCHAR" property="addressZipcode" />
    <result column="address_country" jdbcType="VARCHAR" property="addressCountry" />
    <result column="address_lat" jdbcType="VARCHAR" property="addressLat" />
    <result column="address_lng" jdbcType="VARCHAR" property="addressLng" />
    <result column="country" jdbcType="VARCHAR" property="country" />
    <result column="country_alpha2_code" jdbcType="CHAR" property="countryAlpha2Code" />
    <result column="country_code" jdbcType="VARCHAR" property="countryCode" />
    <result column="currency_symbol" jdbcType="VARCHAR" property="currencySymbol" />
    <result column="currency_code" jdbcType="VARCHAR" property="currencyCode" />
    <result column="time_format_type" jdbcType="TINYINT" property="timeFormatType" />
    <result column="unit_of_weight_type" jdbcType="TINYINT" property="unitOfWeightType" />
    <result column="unit_of_distance_type" jdbcType="TINYINT" property="unitOfDistanceType" />
    <result column="timezone_name" jdbcType="VARCHAR" property="timezoneName" />
    <result column="timezone_seconds" jdbcType="INTEGER" property="timezoneSeconds" />
    <result column="date_format_type" jdbcType="TINYINT" property="dateFormatType" />
    <result column="calendar_format_type" jdbcType="TINYINT" property="calendarFormatType" />
    <result column="number_format_type" jdbcType="TINYINT" property="numberFormatType" />
    <result column="book_online_name" jdbcType="VARCHAR" property="bookOnlineName" />
    <result column="app_type" jdbcType="TINYINT" property="appType" />
    <result column="primary_pay_type" jdbcType="TINYINT" property="primaryPayType" />
    <result column="source" jdbcType="TINYINT" property="source" />
    <result column="clock_in_out_enable" jdbcType="TINYINT" property="clockInOutEnable" />
    <result column="clock_in_out_notify" jdbcType="TINYINT" property="clockInOutNotify" />
    <result column="is_enable_access_code" jdbcType="TINYINT" property="isEnableAccessCode" />
    <result column="smart_schedule_max_dist" jdbcType="INTEGER" property="smartScheduleMaxDist" />
    <result column="smart_schedule_max_time" jdbcType="INTEGER" property="smartScheduleMaxTime" />
    <result column="service_area_enable" jdbcType="TINYINT" property="serviceAreaEnable" />
    <result column="create_time" jdbcType="BIGINT" property="createTime" />
    <result column="update_time" jdbcType="BIGINT" property="updateTime" />
    <result column="facebook" jdbcType="VARCHAR" property="facebook" />
    <result column="instagram" jdbcType="VARCHAR" property="instagram" />
    <result column="google" jdbcType="VARCHAR" property="google" />
    <result column="yelp" jdbcType="VARCHAR" property="yelp" />
    <result column="smart_schedule_start_lat" jdbcType="VARCHAR" property="smartScheduleStartLat" />
    <result column="smart_schedule_start_lng" jdbcType="VARCHAR" property="smartScheduleStartLng" />
    <result column="smart_schedule_end_lat" jdbcType="VARCHAR" property="smartScheduleEndLat" />
    <result column="smart_schedule_end_lng" jdbcType="VARCHAR" property="smartScheduleEndLng" />
    <result column="service_range_max_dist" jdbcType="INTEGER" property="serviceRangeMaxDist" />
    <result column="service_range_max_time" jdbcType="INTEGER" property="serviceRangeMaxTime" />
    <result column="smart_schedule_service_range" jdbcType="INTEGER" property="smartScheduleServiceRange" />
    <result column="smart_schedule_start_addr" jdbcType="VARCHAR" property="smartScheduleStartAddr" />
    <result column="smart_schedule_end_addr" jdbcType="VARCHAR" property="smartScheduleEndAddr" />
    <result column="service_range_center_addr" jdbcType="VARCHAR" property="serviceRangeCenterAddr" />
    <result column="service_range_center_lat" jdbcType="VARCHAR" property="serviceRangeCenterLat" />
    <result column="service_range_center_lng" jdbcType="VARCHAR" property="serviceRangeCenterLng" />
    <result column="source_from" jdbcType="TINYINT" property="sourceFrom" />
    <result column="message_send_by" jdbcType="TINYINT" property="messageSendBy" />
    <result column="send_daily" jdbcType="TINYINT" property="sendDaily" />
    <result column="business_mode" jdbcType="TINYINT" property="businessMode" />
    <result column="know_about_us" jdbcType="VARCHAR" property="knowAboutUs" />
    <result column="appt_per_week" jdbcType="TINYINT" property="apptPerWeek" />
    <result column="business_years" jdbcType="TINYINT" property="businessYears" />
    <result column="move_from" jdbcType="TINYINT" property="moveFrom" />
    <result column="retail_enable" jdbcType="TINYINT" property="retailEnable" />
    <result column="notification_sound_enable" jdbcType="TINYINT" property="notificationSoundEnable" />
    <result column="contact_email" jdbcType="VARCHAR" property="contactEmail" />
    <result column="invitation_code" jdbcType="VARCHAR" property="invitationCode" />
    <result column="tiktok" jdbcType="VARCHAR" property="tiktok" />
    <result column="staff_availability_type" jdbcType="INTEGER" property="staffAvailabilityType" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    id, company_id, business_name, phone_number, avatar_path, website, address, address1, 
    address2, address_city, address_state, address_zipcode, address_country, address_lat, 
    address_lng, country, country_alpha2_code, country_code, currency_symbol, currency_code, 
    time_format_type, unit_of_weight_type, unit_of_distance_type, timezone_name, timezone_seconds, 
    date_format_type, calendar_format_type, number_format_type, book_online_name, app_type, 
    primary_pay_type, source, clock_in_out_enable, clock_in_out_notify, is_enable_access_code, 
    smart_schedule_max_dist, smart_schedule_max_time, service_area_enable, create_time, 
    update_time, facebook, instagram, google, yelp, smart_schedule_start_lat, smart_schedule_start_lng, 
    smart_schedule_end_lat, smart_schedule_end_lng, service_range_max_dist, service_range_max_time, 
    smart_schedule_service_range, smart_schedule_start_addr, smart_schedule_end_addr, 
    service_range_center_addr, service_range_center_lat, service_range_center_lng, source_from, 
    message_send_by, send_daily, business_mode, know_about_us, appt_per_week, business_years, 
    move_from, retail_enable, notification_sound_enable, contact_email, invitation_code, 
    tiktok, staff_availability_type
  </sql>
  <select id="selectByExample" parameterType="com.moego.svc.organization.entity.MoeBusinessExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from moe_business
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select 
    <include refid="Base_Column_List" />
    from moe_business
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from moe_business
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <delete id="deleteByExample" parameterType="com.moego.svc.organization.entity.MoeBusinessExample">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from moe_business
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.moego.svc.organization.entity.MoeBusiness">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into moe_business (company_id, business_name, phone_number, 
      avatar_path, website, address, 
      address1, address2, address_city, 
      address_state, address_zipcode, address_country, 
      address_lat, address_lng, country, 
      country_alpha2_code, country_code, currency_symbol, 
      currency_code, time_format_type, unit_of_weight_type, 
      unit_of_distance_type, timezone_name, timezone_seconds, 
      date_format_type, calendar_format_type, number_format_type, 
      book_online_name, app_type, primary_pay_type, 
      source, clock_in_out_enable, clock_in_out_notify, 
      is_enable_access_code, smart_schedule_max_dist, 
      smart_schedule_max_time, service_area_enable, 
      create_time, update_time, facebook, 
      instagram, google, yelp, 
      smart_schedule_start_lat, smart_schedule_start_lng, 
      smart_schedule_end_lat, smart_schedule_end_lng, 
      service_range_max_dist, service_range_max_time, 
      smart_schedule_service_range, smart_schedule_start_addr, 
      smart_schedule_end_addr, service_range_center_addr, 
      service_range_center_lat, service_range_center_lng, 
      source_from, message_send_by, send_daily, 
      business_mode, know_about_us, appt_per_week, 
      business_years, move_from, retail_enable, 
      notification_sound_enable, contact_email, 
      invitation_code, tiktok, staff_availability_type
      )
    values (#{companyId,jdbcType=INTEGER}, #{businessName,jdbcType=VARCHAR}, #{phoneNumber,jdbcType=VARCHAR}, 
      #{avatarPath,jdbcType=VARCHAR}, #{website,jdbcType=VARCHAR}, #{address,jdbcType=VARCHAR}, 
      #{address1,jdbcType=VARCHAR}, #{address2,jdbcType=VARCHAR}, #{addressCity,jdbcType=VARCHAR}, 
      #{addressState,jdbcType=VARCHAR}, #{addressZipcode,jdbcType=VARCHAR}, #{addressCountry,jdbcType=VARCHAR}, 
      #{addressLat,jdbcType=VARCHAR}, #{addressLng,jdbcType=VARCHAR}, #{country,jdbcType=VARCHAR}, 
      #{countryAlpha2Code,jdbcType=CHAR}, #{countryCode,jdbcType=VARCHAR}, #{currencySymbol,jdbcType=VARCHAR}, 
      #{currencyCode,jdbcType=VARCHAR}, #{timeFormatType,jdbcType=TINYINT}, #{unitOfWeightType,jdbcType=TINYINT}, 
      #{unitOfDistanceType,jdbcType=TINYINT}, #{timezoneName,jdbcType=VARCHAR}, #{timezoneSeconds,jdbcType=INTEGER}, 
      #{dateFormatType,jdbcType=TINYINT}, #{calendarFormatType,jdbcType=TINYINT}, #{numberFormatType,jdbcType=TINYINT}, 
      #{bookOnlineName,jdbcType=VARCHAR}, #{appType,jdbcType=TINYINT}, #{primaryPayType,jdbcType=TINYINT}, 
      #{source,jdbcType=TINYINT}, #{clockInOutEnable,jdbcType=TINYINT}, #{clockInOutNotify,jdbcType=TINYINT}, 
      #{isEnableAccessCode,jdbcType=TINYINT}, #{smartScheduleMaxDist,jdbcType=INTEGER}, 
      #{smartScheduleMaxTime,jdbcType=INTEGER}, #{serviceAreaEnable,jdbcType=TINYINT}, 
      #{createTime,jdbcType=BIGINT}, #{updateTime,jdbcType=BIGINT}, #{facebook,jdbcType=VARCHAR}, 
      #{instagram,jdbcType=VARCHAR}, #{google,jdbcType=VARCHAR}, #{yelp,jdbcType=VARCHAR}, 
      #{smartScheduleStartLat,jdbcType=VARCHAR}, #{smartScheduleStartLng,jdbcType=VARCHAR}, 
      #{smartScheduleEndLat,jdbcType=VARCHAR}, #{smartScheduleEndLng,jdbcType=VARCHAR}, 
      #{serviceRangeMaxDist,jdbcType=INTEGER}, #{serviceRangeMaxTime,jdbcType=INTEGER}, 
      #{smartScheduleServiceRange,jdbcType=INTEGER}, #{smartScheduleStartAddr,jdbcType=VARCHAR}, 
      #{smartScheduleEndAddr,jdbcType=VARCHAR}, #{serviceRangeCenterAddr,jdbcType=VARCHAR}, 
      #{serviceRangeCenterLat,jdbcType=VARCHAR}, #{serviceRangeCenterLng,jdbcType=VARCHAR}, 
      #{sourceFrom,jdbcType=TINYINT}, #{messageSendBy,jdbcType=TINYINT}, #{sendDaily,jdbcType=TINYINT}, 
      #{businessMode,jdbcType=TINYINT}, #{knowAboutUs,jdbcType=VARCHAR}, #{apptPerWeek,jdbcType=TINYINT}, 
      #{businessYears,jdbcType=TINYINT}, #{moveFrom,jdbcType=TINYINT}, #{retailEnable,jdbcType=TINYINT}, 
      #{notificationSoundEnable,jdbcType=TINYINT}, #{contactEmail,jdbcType=VARCHAR}, 
      #{invitationCode,jdbcType=VARCHAR}, #{tiktok,jdbcType=VARCHAR}, #{staffAvailabilityType,jdbcType=INTEGER}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.moego.svc.organization.entity.MoeBusiness">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into moe_business
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="companyId != null">
        company_id,
      </if>
      <if test="businessName != null">
        business_name,
      </if>
      <if test="phoneNumber != null">
        phone_number,
      </if>
      <if test="avatarPath != null">
        avatar_path,
      </if>
      <if test="website != null">
        website,
      </if>
      <if test="address != null">
        address,
      </if>
      <if test="address1 != null">
        address1,
      </if>
      <if test="address2 != null">
        address2,
      </if>
      <if test="addressCity != null">
        address_city,
      </if>
      <if test="addressState != null">
        address_state,
      </if>
      <if test="addressZipcode != null">
        address_zipcode,
      </if>
      <if test="addressCountry != null">
        address_country,
      </if>
      <if test="addressLat != null">
        address_lat,
      </if>
      <if test="addressLng != null">
        address_lng,
      </if>
      <if test="country != null">
        country,
      </if>
      <if test="countryAlpha2Code != null">
        country_alpha2_code,
      </if>
      <if test="countryCode != null">
        country_code,
      </if>
      <if test="currencySymbol != null">
        currency_symbol,
      </if>
      <if test="currencyCode != null">
        currency_code,
      </if>
      <if test="timeFormatType != null">
        time_format_type,
      </if>
      <if test="unitOfWeightType != null">
        unit_of_weight_type,
      </if>
      <if test="unitOfDistanceType != null">
        unit_of_distance_type,
      </if>
      <if test="timezoneName != null">
        timezone_name,
      </if>
      <if test="timezoneSeconds != null">
        timezone_seconds,
      </if>
      <if test="dateFormatType != null">
        date_format_type,
      </if>
      <if test="calendarFormatType != null">
        calendar_format_type,
      </if>
      <if test="numberFormatType != null">
        number_format_type,
      </if>
      <if test="bookOnlineName != null">
        book_online_name,
      </if>
      <if test="appType != null">
        app_type,
      </if>
      <if test="primaryPayType != null">
        primary_pay_type,
      </if>
      <if test="source != null">
        source,
      </if>
      <if test="clockInOutEnable != null">
        clock_in_out_enable,
      </if>
      <if test="clockInOutNotify != null">
        clock_in_out_notify,
      </if>
      <if test="isEnableAccessCode != null">
        is_enable_access_code,
      </if>
      <if test="smartScheduleMaxDist != null">
        smart_schedule_max_dist,
      </if>
      <if test="smartScheduleMaxTime != null">
        smart_schedule_max_time,
      </if>
      <if test="serviceAreaEnable != null">
        service_area_enable,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="facebook != null">
        facebook,
      </if>
      <if test="instagram != null">
        instagram,
      </if>
      <if test="google != null">
        google,
      </if>
      <if test="yelp != null">
        yelp,
      </if>
      <if test="smartScheduleStartLat != null">
        smart_schedule_start_lat,
      </if>
      <if test="smartScheduleStartLng != null">
        smart_schedule_start_lng,
      </if>
      <if test="smartScheduleEndLat != null">
        smart_schedule_end_lat,
      </if>
      <if test="smartScheduleEndLng != null">
        smart_schedule_end_lng,
      </if>
      <if test="serviceRangeMaxDist != null">
        service_range_max_dist,
      </if>
      <if test="serviceRangeMaxTime != null">
        service_range_max_time,
      </if>
      <if test="smartScheduleServiceRange != null">
        smart_schedule_service_range,
      </if>
      <if test="smartScheduleStartAddr != null">
        smart_schedule_start_addr,
      </if>
      <if test="smartScheduleEndAddr != null">
        smart_schedule_end_addr,
      </if>
      <if test="serviceRangeCenterAddr != null">
        service_range_center_addr,
      </if>
      <if test="serviceRangeCenterLat != null">
        service_range_center_lat,
      </if>
      <if test="serviceRangeCenterLng != null">
        service_range_center_lng,
      </if>
      <if test="sourceFrom != null">
        source_from,
      </if>
      <if test="messageSendBy != null">
        message_send_by,
      </if>
      <if test="sendDaily != null">
        send_daily,
      </if>
      <if test="businessMode != null">
        business_mode,
      </if>
      <if test="knowAboutUs != null">
        know_about_us,
      </if>
      <if test="apptPerWeek != null">
        appt_per_week,
      </if>
      <if test="businessYears != null">
        business_years,
      </if>
      <if test="moveFrom != null">
        move_from,
      </if>
      <if test="retailEnable != null">
        retail_enable,
      </if>
      <if test="notificationSoundEnable != null">
        notification_sound_enable,
      </if>
      <if test="contactEmail != null">
        contact_email,
      </if>
      <if test="invitationCode != null">
        invitation_code,
      </if>
      <if test="tiktok != null">
        tiktok,
      </if>
      <if test="staffAvailabilityType != null">
        staff_availability_type,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="companyId != null">
        #{companyId,jdbcType=INTEGER},
      </if>
      <if test="businessName != null">
        #{businessName,jdbcType=VARCHAR},
      </if>
      <if test="phoneNumber != null">
        #{phoneNumber,jdbcType=VARCHAR},
      </if>
      <if test="avatarPath != null">
        #{avatarPath,jdbcType=VARCHAR},
      </if>
      <if test="website != null">
        #{website,jdbcType=VARCHAR},
      </if>
      <if test="address != null">
        #{address,jdbcType=VARCHAR},
      </if>
      <if test="address1 != null">
        #{address1,jdbcType=VARCHAR},
      </if>
      <if test="address2 != null">
        #{address2,jdbcType=VARCHAR},
      </if>
      <if test="addressCity != null">
        #{addressCity,jdbcType=VARCHAR},
      </if>
      <if test="addressState != null">
        #{addressState,jdbcType=VARCHAR},
      </if>
      <if test="addressZipcode != null">
        #{addressZipcode,jdbcType=VARCHAR},
      </if>
      <if test="addressCountry != null">
        #{addressCountry,jdbcType=VARCHAR},
      </if>
      <if test="addressLat != null">
        #{addressLat,jdbcType=VARCHAR},
      </if>
      <if test="addressLng != null">
        #{addressLng,jdbcType=VARCHAR},
      </if>
      <if test="country != null">
        #{country,jdbcType=VARCHAR},
      </if>
      <if test="countryAlpha2Code != null">
        #{countryAlpha2Code,jdbcType=CHAR},
      </if>
      <if test="countryCode != null">
        #{countryCode,jdbcType=VARCHAR},
      </if>
      <if test="currencySymbol != null">
        #{currencySymbol,jdbcType=VARCHAR},
      </if>
      <if test="currencyCode != null">
        #{currencyCode,jdbcType=VARCHAR},
      </if>
      <if test="timeFormatType != null">
        #{timeFormatType,jdbcType=TINYINT},
      </if>
      <if test="unitOfWeightType != null">
        #{unitOfWeightType,jdbcType=TINYINT},
      </if>
      <if test="unitOfDistanceType != null">
        #{unitOfDistanceType,jdbcType=TINYINT},
      </if>
      <if test="timezoneName != null">
        #{timezoneName,jdbcType=VARCHAR},
      </if>
      <if test="timezoneSeconds != null">
        #{timezoneSeconds,jdbcType=INTEGER},
      </if>
      <if test="dateFormatType != null">
        #{dateFormatType,jdbcType=TINYINT},
      </if>
      <if test="calendarFormatType != null">
        #{calendarFormatType,jdbcType=TINYINT},
      </if>
      <if test="numberFormatType != null">
        #{numberFormatType,jdbcType=TINYINT},
      </if>
      <if test="bookOnlineName != null">
        #{bookOnlineName,jdbcType=VARCHAR},
      </if>
      <if test="appType != null">
        #{appType,jdbcType=TINYINT},
      </if>
      <if test="primaryPayType != null">
        #{primaryPayType,jdbcType=TINYINT},
      </if>
      <if test="source != null">
        #{source,jdbcType=TINYINT},
      </if>
      <if test="clockInOutEnable != null">
        #{clockInOutEnable,jdbcType=TINYINT},
      </if>
      <if test="clockInOutNotify != null">
        #{clockInOutNotify,jdbcType=TINYINT},
      </if>
      <if test="isEnableAccessCode != null">
        #{isEnableAccessCode,jdbcType=TINYINT},
      </if>
      <if test="smartScheduleMaxDist != null">
        #{smartScheduleMaxDist,jdbcType=INTEGER},
      </if>
      <if test="smartScheduleMaxTime != null">
        #{smartScheduleMaxTime,jdbcType=INTEGER},
      </if>
      <if test="serviceAreaEnable != null">
        #{serviceAreaEnable,jdbcType=TINYINT},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=BIGINT},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=BIGINT},
      </if>
      <if test="facebook != null">
        #{facebook,jdbcType=VARCHAR},
      </if>
      <if test="instagram != null">
        #{instagram,jdbcType=VARCHAR},
      </if>
      <if test="google != null">
        #{google,jdbcType=VARCHAR},
      </if>
      <if test="yelp != null">
        #{yelp,jdbcType=VARCHAR},
      </if>
      <if test="smartScheduleStartLat != null">
        #{smartScheduleStartLat,jdbcType=VARCHAR},
      </if>
      <if test="smartScheduleStartLng != null">
        #{smartScheduleStartLng,jdbcType=VARCHAR},
      </if>
      <if test="smartScheduleEndLat != null">
        #{smartScheduleEndLat,jdbcType=VARCHAR},
      </if>
      <if test="smartScheduleEndLng != null">
        #{smartScheduleEndLng,jdbcType=VARCHAR},
      </if>
      <if test="serviceRangeMaxDist != null">
        #{serviceRangeMaxDist,jdbcType=INTEGER},
      </if>
      <if test="serviceRangeMaxTime != null">
        #{serviceRangeMaxTime,jdbcType=INTEGER},
      </if>
      <if test="smartScheduleServiceRange != null">
        #{smartScheduleServiceRange,jdbcType=INTEGER},
      </if>
      <if test="smartScheduleStartAddr != null">
        #{smartScheduleStartAddr,jdbcType=VARCHAR},
      </if>
      <if test="smartScheduleEndAddr != null">
        #{smartScheduleEndAddr,jdbcType=VARCHAR},
      </if>
      <if test="serviceRangeCenterAddr != null">
        #{serviceRangeCenterAddr,jdbcType=VARCHAR},
      </if>
      <if test="serviceRangeCenterLat != null">
        #{serviceRangeCenterLat,jdbcType=VARCHAR},
      </if>
      <if test="serviceRangeCenterLng != null">
        #{serviceRangeCenterLng,jdbcType=VARCHAR},
      </if>
      <if test="sourceFrom != null">
        #{sourceFrom,jdbcType=TINYINT},
      </if>
      <if test="messageSendBy != null">
        #{messageSendBy,jdbcType=TINYINT},
      </if>
      <if test="sendDaily != null">
        #{sendDaily,jdbcType=TINYINT},
      </if>
      <if test="businessMode != null">
        #{businessMode,jdbcType=TINYINT},
      </if>
      <if test="knowAboutUs != null">
        #{knowAboutUs,jdbcType=VARCHAR},
      </if>
      <if test="apptPerWeek != null">
        #{apptPerWeek,jdbcType=TINYINT},
      </if>
      <if test="businessYears != null">
        #{businessYears,jdbcType=TINYINT},
      </if>
      <if test="moveFrom != null">
        #{moveFrom,jdbcType=TINYINT},
      </if>
      <if test="retailEnable != null">
        #{retailEnable,jdbcType=TINYINT},
      </if>
      <if test="notificationSoundEnable != null">
        #{notificationSoundEnable,jdbcType=TINYINT},
      </if>
      <if test="contactEmail != null">
        #{contactEmail,jdbcType=VARCHAR},
      </if>
      <if test="invitationCode != null">
        #{invitationCode,jdbcType=VARCHAR},
      </if>
      <if test="tiktok != null">
        #{tiktok,jdbcType=VARCHAR},
      </if>
      <if test="staffAvailabilityType != null">
        #{staffAvailabilityType,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.moego.svc.organization.entity.MoeBusinessExample" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select count(*) from moe_business
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update moe_business
    <set>
      <if test="row.id != null">
        id = #{row.id,jdbcType=INTEGER},
      </if>
      <if test="row.companyId != null">
        company_id = #{row.companyId,jdbcType=INTEGER},
      </if>
      <if test="row.businessName != null">
        business_name = #{row.businessName,jdbcType=VARCHAR},
      </if>
      <if test="row.phoneNumber != null">
        phone_number = #{row.phoneNumber,jdbcType=VARCHAR},
      </if>
      <if test="row.avatarPath != null">
        avatar_path = #{row.avatarPath,jdbcType=VARCHAR},
      </if>
      <if test="row.website != null">
        website = #{row.website,jdbcType=VARCHAR},
      </if>
      <if test="row.address != null">
        address = #{row.address,jdbcType=VARCHAR},
      </if>
      <if test="row.address1 != null">
        address1 = #{row.address1,jdbcType=VARCHAR},
      </if>
      <if test="row.address2 != null">
        address2 = #{row.address2,jdbcType=VARCHAR},
      </if>
      <if test="row.addressCity != null">
        address_city = #{row.addressCity,jdbcType=VARCHAR},
      </if>
      <if test="row.addressState != null">
        address_state = #{row.addressState,jdbcType=VARCHAR},
      </if>
      <if test="row.addressZipcode != null">
        address_zipcode = #{row.addressZipcode,jdbcType=VARCHAR},
      </if>
      <if test="row.addressCountry != null">
        address_country = #{row.addressCountry,jdbcType=VARCHAR},
      </if>
      <if test="row.addressLat != null">
        address_lat = #{row.addressLat,jdbcType=VARCHAR},
      </if>
      <if test="row.addressLng != null">
        address_lng = #{row.addressLng,jdbcType=VARCHAR},
      </if>
      <if test="row.country != null">
        country = #{row.country,jdbcType=VARCHAR},
      </if>
      <if test="row.countryAlpha2Code != null">
        country_alpha2_code = #{row.countryAlpha2Code,jdbcType=CHAR},
      </if>
      <if test="row.countryCode != null">
        country_code = #{row.countryCode,jdbcType=VARCHAR},
      </if>
      <if test="row.currencySymbol != null">
        currency_symbol = #{row.currencySymbol,jdbcType=VARCHAR},
      </if>
      <if test="row.currencyCode != null">
        currency_code = #{row.currencyCode,jdbcType=VARCHAR},
      </if>
      <if test="row.timeFormatType != null">
        time_format_type = #{row.timeFormatType,jdbcType=TINYINT},
      </if>
      <if test="row.unitOfWeightType != null">
        unit_of_weight_type = #{row.unitOfWeightType,jdbcType=TINYINT},
      </if>
      <if test="row.unitOfDistanceType != null">
        unit_of_distance_type = #{row.unitOfDistanceType,jdbcType=TINYINT},
      </if>
      <if test="row.timezoneName != null">
        timezone_name = #{row.timezoneName,jdbcType=VARCHAR},
      </if>
      <if test="row.timezoneSeconds != null">
        timezone_seconds = #{row.timezoneSeconds,jdbcType=INTEGER},
      </if>
      <if test="row.dateFormatType != null">
        date_format_type = #{row.dateFormatType,jdbcType=TINYINT},
      </if>
      <if test="row.calendarFormatType != null">
        calendar_format_type = #{row.calendarFormatType,jdbcType=TINYINT},
      </if>
      <if test="row.numberFormatType != null">
        number_format_type = #{row.numberFormatType,jdbcType=TINYINT},
      </if>
      <if test="row.bookOnlineName != null">
        book_online_name = #{row.bookOnlineName,jdbcType=VARCHAR},
      </if>
      <if test="row.appType != null">
        app_type = #{row.appType,jdbcType=TINYINT},
      </if>
      <if test="row.primaryPayType != null">
        primary_pay_type = #{row.primaryPayType,jdbcType=TINYINT},
      </if>
      <if test="row.source != null">
        source = #{row.source,jdbcType=TINYINT},
      </if>
      <if test="row.clockInOutEnable != null">
        clock_in_out_enable = #{row.clockInOutEnable,jdbcType=TINYINT},
      </if>
      <if test="row.clockInOutNotify != null">
        clock_in_out_notify = #{row.clockInOutNotify,jdbcType=TINYINT},
      </if>
      <if test="row.isEnableAccessCode != null">
        is_enable_access_code = #{row.isEnableAccessCode,jdbcType=TINYINT},
      </if>
      <if test="row.smartScheduleMaxDist != null">
        smart_schedule_max_dist = #{row.smartScheduleMaxDist,jdbcType=INTEGER},
      </if>
      <if test="row.smartScheduleMaxTime != null">
        smart_schedule_max_time = #{row.smartScheduleMaxTime,jdbcType=INTEGER},
      </if>
      <if test="row.serviceAreaEnable != null">
        service_area_enable = #{row.serviceAreaEnable,jdbcType=TINYINT},
      </if>
      <if test="row.createTime != null">
        create_time = #{row.createTime,jdbcType=BIGINT},
      </if>
      <if test="row.updateTime != null">
        update_time = #{row.updateTime,jdbcType=BIGINT},
      </if>
      <if test="row.facebook != null">
        facebook = #{row.facebook,jdbcType=VARCHAR},
      </if>
      <if test="row.instagram != null">
        instagram = #{row.instagram,jdbcType=VARCHAR},
      </if>
      <if test="row.google != null">
        google = #{row.google,jdbcType=VARCHAR},
      </if>
      <if test="row.yelp != null">
        yelp = #{row.yelp,jdbcType=VARCHAR},
      </if>
      <if test="row.smartScheduleStartLat != null">
        smart_schedule_start_lat = #{row.smartScheduleStartLat,jdbcType=VARCHAR},
      </if>
      <if test="row.smartScheduleStartLng != null">
        smart_schedule_start_lng = #{row.smartScheduleStartLng,jdbcType=VARCHAR},
      </if>
      <if test="row.smartScheduleEndLat != null">
        smart_schedule_end_lat = #{row.smartScheduleEndLat,jdbcType=VARCHAR},
      </if>
      <if test="row.smartScheduleEndLng != null">
        smart_schedule_end_lng = #{row.smartScheduleEndLng,jdbcType=VARCHAR},
      </if>
      <if test="row.serviceRangeMaxDist != null">
        service_range_max_dist = #{row.serviceRangeMaxDist,jdbcType=INTEGER},
      </if>
      <if test="row.serviceRangeMaxTime != null">
        service_range_max_time = #{row.serviceRangeMaxTime,jdbcType=INTEGER},
      </if>
      <if test="row.smartScheduleServiceRange != null">
        smart_schedule_service_range = #{row.smartScheduleServiceRange,jdbcType=INTEGER},
      </if>
      <if test="row.smartScheduleStartAddr != null">
        smart_schedule_start_addr = #{row.smartScheduleStartAddr,jdbcType=VARCHAR},
      </if>
      <if test="row.smartScheduleEndAddr != null">
        smart_schedule_end_addr = #{row.smartScheduleEndAddr,jdbcType=VARCHAR},
      </if>
      <if test="row.serviceRangeCenterAddr != null">
        service_range_center_addr = #{row.serviceRangeCenterAddr,jdbcType=VARCHAR},
      </if>
      <if test="row.serviceRangeCenterLat != null">
        service_range_center_lat = #{row.serviceRangeCenterLat,jdbcType=VARCHAR},
      </if>
      <if test="row.serviceRangeCenterLng != null">
        service_range_center_lng = #{row.serviceRangeCenterLng,jdbcType=VARCHAR},
      </if>
      <if test="row.sourceFrom != null">
        source_from = #{row.sourceFrom,jdbcType=TINYINT},
      </if>
      <if test="row.messageSendBy != null">
        message_send_by = #{row.messageSendBy,jdbcType=TINYINT},
      </if>
      <if test="row.sendDaily != null">
        send_daily = #{row.sendDaily,jdbcType=TINYINT},
      </if>
      <if test="row.businessMode != null">
        business_mode = #{row.businessMode,jdbcType=TINYINT},
      </if>
      <if test="row.knowAboutUs != null">
        know_about_us = #{row.knowAboutUs,jdbcType=VARCHAR},
      </if>
      <if test="row.apptPerWeek != null">
        appt_per_week = #{row.apptPerWeek,jdbcType=TINYINT},
      </if>
      <if test="row.businessYears != null">
        business_years = #{row.businessYears,jdbcType=TINYINT},
      </if>
      <if test="row.moveFrom != null">
        move_from = #{row.moveFrom,jdbcType=TINYINT},
      </if>
      <if test="row.retailEnable != null">
        retail_enable = #{row.retailEnable,jdbcType=TINYINT},
      </if>
      <if test="row.notificationSoundEnable != null">
        notification_sound_enable = #{row.notificationSoundEnable,jdbcType=TINYINT},
      </if>
      <if test="row.contactEmail != null">
        contact_email = #{row.contactEmail,jdbcType=VARCHAR},
      </if>
      <if test="row.invitationCode != null">
        invitation_code = #{row.invitationCode,jdbcType=VARCHAR},
      </if>
      <if test="row.tiktok != null">
        tiktok = #{row.tiktok,jdbcType=VARCHAR},
      </if>
      <if test="row.staffAvailabilityType != null">
        staff_availability_type = #{row.staffAvailabilityType,jdbcType=INTEGER},
      </if>
    </set>
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update moe_business
    set id = #{row.id,jdbcType=INTEGER},
      company_id = #{row.companyId,jdbcType=INTEGER},
      business_name = #{row.businessName,jdbcType=VARCHAR},
      phone_number = #{row.phoneNumber,jdbcType=VARCHAR},
      avatar_path = #{row.avatarPath,jdbcType=VARCHAR},
      website = #{row.website,jdbcType=VARCHAR},
      address = #{row.address,jdbcType=VARCHAR},
      address1 = #{row.address1,jdbcType=VARCHAR},
      address2 = #{row.address2,jdbcType=VARCHAR},
      address_city = #{row.addressCity,jdbcType=VARCHAR},
      address_state = #{row.addressState,jdbcType=VARCHAR},
      address_zipcode = #{row.addressZipcode,jdbcType=VARCHAR},
      address_country = #{row.addressCountry,jdbcType=VARCHAR},
      address_lat = #{row.addressLat,jdbcType=VARCHAR},
      address_lng = #{row.addressLng,jdbcType=VARCHAR},
      country = #{row.country,jdbcType=VARCHAR},
      country_alpha2_code = #{row.countryAlpha2Code,jdbcType=CHAR},
      country_code = #{row.countryCode,jdbcType=VARCHAR},
      currency_symbol = #{row.currencySymbol,jdbcType=VARCHAR},
      currency_code = #{row.currencyCode,jdbcType=VARCHAR},
      time_format_type = #{row.timeFormatType,jdbcType=TINYINT},
      unit_of_weight_type = #{row.unitOfWeightType,jdbcType=TINYINT},
      unit_of_distance_type = #{row.unitOfDistanceType,jdbcType=TINYINT},
      timezone_name = #{row.timezoneName,jdbcType=VARCHAR},
      timezone_seconds = #{row.timezoneSeconds,jdbcType=INTEGER},
      date_format_type = #{row.dateFormatType,jdbcType=TINYINT},
      calendar_format_type = #{row.calendarFormatType,jdbcType=TINYINT},
      number_format_type = #{row.numberFormatType,jdbcType=TINYINT},
      book_online_name = #{row.bookOnlineName,jdbcType=VARCHAR},
      app_type = #{row.appType,jdbcType=TINYINT},
      primary_pay_type = #{row.primaryPayType,jdbcType=TINYINT},
      source = #{row.source,jdbcType=TINYINT},
      clock_in_out_enable = #{row.clockInOutEnable,jdbcType=TINYINT},
      clock_in_out_notify = #{row.clockInOutNotify,jdbcType=TINYINT},
      is_enable_access_code = #{row.isEnableAccessCode,jdbcType=TINYINT},
      smart_schedule_max_dist = #{row.smartScheduleMaxDist,jdbcType=INTEGER},
      smart_schedule_max_time = #{row.smartScheduleMaxTime,jdbcType=INTEGER},
      service_area_enable = #{row.serviceAreaEnable,jdbcType=TINYINT},
      create_time = #{row.createTime,jdbcType=BIGINT},
      update_time = #{row.updateTime,jdbcType=BIGINT},
      facebook = #{row.facebook,jdbcType=VARCHAR},
      instagram = #{row.instagram,jdbcType=VARCHAR},
      google = #{row.google,jdbcType=VARCHAR},
      yelp = #{row.yelp,jdbcType=VARCHAR},
      smart_schedule_start_lat = #{row.smartScheduleStartLat,jdbcType=VARCHAR},
      smart_schedule_start_lng = #{row.smartScheduleStartLng,jdbcType=VARCHAR},
      smart_schedule_end_lat = #{row.smartScheduleEndLat,jdbcType=VARCHAR},
      smart_schedule_end_lng = #{row.smartScheduleEndLng,jdbcType=VARCHAR},
      service_range_max_dist = #{row.serviceRangeMaxDist,jdbcType=INTEGER},
      service_range_max_time = #{row.serviceRangeMaxTime,jdbcType=INTEGER},
      smart_schedule_service_range = #{row.smartScheduleServiceRange,jdbcType=INTEGER},
      smart_schedule_start_addr = #{row.smartScheduleStartAddr,jdbcType=VARCHAR},
      smart_schedule_end_addr = #{row.smartScheduleEndAddr,jdbcType=VARCHAR},
      service_range_center_addr = #{row.serviceRangeCenterAddr,jdbcType=VARCHAR},
      service_range_center_lat = #{row.serviceRangeCenterLat,jdbcType=VARCHAR},
      service_range_center_lng = #{row.serviceRangeCenterLng,jdbcType=VARCHAR},
      source_from = #{row.sourceFrom,jdbcType=TINYINT},
      message_send_by = #{row.messageSendBy,jdbcType=TINYINT},
      send_daily = #{row.sendDaily,jdbcType=TINYINT},
      business_mode = #{row.businessMode,jdbcType=TINYINT},
      know_about_us = #{row.knowAboutUs,jdbcType=VARCHAR},
      appt_per_week = #{row.apptPerWeek,jdbcType=TINYINT},
      business_years = #{row.businessYears,jdbcType=TINYINT},
      move_from = #{row.moveFrom,jdbcType=TINYINT},
      retail_enable = #{row.retailEnable,jdbcType=TINYINT},
      notification_sound_enable = #{row.notificationSoundEnable,jdbcType=TINYINT},
      contact_email = #{row.contactEmail,jdbcType=VARCHAR},
      invitation_code = #{row.invitationCode,jdbcType=VARCHAR},
      tiktok = #{row.tiktok,jdbcType=VARCHAR},
      staff_availability_type = #{row.staffAvailabilityType,jdbcType=INTEGER}
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.moego.svc.organization.entity.MoeBusiness">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update moe_business
    <set>
      <if test="companyId != null">
        company_id = #{companyId,jdbcType=INTEGER},
      </if>
      <if test="businessName != null">
        business_name = #{businessName,jdbcType=VARCHAR},
      </if>
      <if test="phoneNumber != null">
        phone_number = #{phoneNumber,jdbcType=VARCHAR},
      </if>
      <if test="avatarPath != null">
        avatar_path = #{avatarPath,jdbcType=VARCHAR},
      </if>
      <if test="website != null">
        website = #{website,jdbcType=VARCHAR},
      </if>
      <if test="address != null">
        address = #{address,jdbcType=VARCHAR},
      </if>
      <if test="address1 != null">
        address1 = #{address1,jdbcType=VARCHAR},
      </if>
      <if test="address2 != null">
        address2 = #{address2,jdbcType=VARCHAR},
      </if>
      <if test="addressCity != null">
        address_city = #{addressCity,jdbcType=VARCHAR},
      </if>
      <if test="addressState != null">
        address_state = #{addressState,jdbcType=VARCHAR},
      </if>
      <if test="addressZipcode != null">
        address_zipcode = #{addressZipcode,jdbcType=VARCHAR},
      </if>
      <if test="addressCountry != null">
        address_country = #{addressCountry,jdbcType=VARCHAR},
      </if>
      <if test="addressLat != null">
        address_lat = #{addressLat,jdbcType=VARCHAR},
      </if>
      <if test="addressLng != null">
        address_lng = #{addressLng,jdbcType=VARCHAR},
      </if>
      <if test="country != null">
        country = #{country,jdbcType=VARCHAR},
      </if>
      <if test="countryAlpha2Code != null">
        country_alpha2_code = #{countryAlpha2Code,jdbcType=CHAR},
      </if>
      <if test="countryCode != null">
        country_code = #{countryCode,jdbcType=VARCHAR},
      </if>
      <if test="currencySymbol != null">
        currency_symbol = #{currencySymbol,jdbcType=VARCHAR},
      </if>
      <if test="currencyCode != null">
        currency_code = #{currencyCode,jdbcType=VARCHAR},
      </if>
      <if test="timeFormatType != null">
        time_format_type = #{timeFormatType,jdbcType=TINYINT},
      </if>
      <if test="unitOfWeightType != null">
        unit_of_weight_type = #{unitOfWeightType,jdbcType=TINYINT},
      </if>
      <if test="unitOfDistanceType != null">
        unit_of_distance_type = #{unitOfDistanceType,jdbcType=TINYINT},
      </if>
      <if test="timezoneName != null">
        timezone_name = #{timezoneName,jdbcType=VARCHAR},
      </if>
      <if test="timezoneSeconds != null">
        timezone_seconds = #{timezoneSeconds,jdbcType=INTEGER},
      </if>
      <if test="dateFormatType != null">
        date_format_type = #{dateFormatType,jdbcType=TINYINT},
      </if>
      <if test="calendarFormatType != null">
        calendar_format_type = #{calendarFormatType,jdbcType=TINYINT},
      </if>
      <if test="numberFormatType != null">
        number_format_type = #{numberFormatType,jdbcType=TINYINT},
      </if>
      <if test="bookOnlineName != null">
        book_online_name = #{bookOnlineName,jdbcType=VARCHAR},
      </if>
      <if test="appType != null">
        app_type = #{appType,jdbcType=TINYINT},
      </if>
      <if test="primaryPayType != null">
        primary_pay_type = #{primaryPayType,jdbcType=TINYINT},
      </if>
      <if test="source != null">
        source = #{source,jdbcType=TINYINT},
      </if>
      <if test="clockInOutEnable != null">
        clock_in_out_enable = #{clockInOutEnable,jdbcType=TINYINT},
      </if>
      <if test="clockInOutNotify != null">
        clock_in_out_notify = #{clockInOutNotify,jdbcType=TINYINT},
      </if>
      <if test="isEnableAccessCode != null">
        is_enable_access_code = #{isEnableAccessCode,jdbcType=TINYINT},
      </if>
      <if test="smartScheduleMaxDist != null">
        smart_schedule_max_dist = #{smartScheduleMaxDist,jdbcType=INTEGER},
      </if>
      <if test="smartScheduleMaxTime != null">
        smart_schedule_max_time = #{smartScheduleMaxTime,jdbcType=INTEGER},
      </if>
      <if test="serviceAreaEnable != null">
        service_area_enable = #{serviceAreaEnable,jdbcType=TINYINT},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=BIGINT},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=BIGINT},
      </if>
      <if test="facebook != null">
        facebook = #{facebook,jdbcType=VARCHAR},
      </if>
      <if test="instagram != null">
        instagram = #{instagram,jdbcType=VARCHAR},
      </if>
      <if test="google != null">
        google = #{google,jdbcType=VARCHAR},
      </if>
      <if test="yelp != null">
        yelp = #{yelp,jdbcType=VARCHAR},
      </if>
      <if test="smartScheduleStartLat != null">
        smart_schedule_start_lat = #{smartScheduleStartLat,jdbcType=VARCHAR},
      </if>
      <if test="smartScheduleStartLng != null">
        smart_schedule_start_lng = #{smartScheduleStartLng,jdbcType=VARCHAR},
      </if>
      <if test="smartScheduleEndLat != null">
        smart_schedule_end_lat = #{smartScheduleEndLat,jdbcType=VARCHAR},
      </if>
      <if test="smartScheduleEndLng != null">
        smart_schedule_end_lng = #{smartScheduleEndLng,jdbcType=VARCHAR},
      </if>
      <if test="serviceRangeMaxDist != null">
        service_range_max_dist = #{serviceRangeMaxDist,jdbcType=INTEGER},
      </if>
      <if test="serviceRangeMaxTime != null">
        service_range_max_time = #{serviceRangeMaxTime,jdbcType=INTEGER},
      </if>
      <if test="smartScheduleServiceRange != null">
        smart_schedule_service_range = #{smartScheduleServiceRange,jdbcType=INTEGER},
      </if>
      <if test="smartScheduleStartAddr != null">
        smart_schedule_start_addr = #{smartScheduleStartAddr,jdbcType=VARCHAR},
      </if>
      <if test="smartScheduleEndAddr != null">
        smart_schedule_end_addr = #{smartScheduleEndAddr,jdbcType=VARCHAR},
      </if>
      <if test="serviceRangeCenterAddr != null">
        service_range_center_addr = #{serviceRangeCenterAddr,jdbcType=VARCHAR},
      </if>
      <if test="serviceRangeCenterLat != null">
        service_range_center_lat = #{serviceRangeCenterLat,jdbcType=VARCHAR},
      </if>
      <if test="serviceRangeCenterLng != null">
        service_range_center_lng = #{serviceRangeCenterLng,jdbcType=VARCHAR},
      </if>
      <if test="sourceFrom != null">
        source_from = #{sourceFrom,jdbcType=TINYINT},
      </if>
      <if test="messageSendBy != null">
        message_send_by = #{messageSendBy,jdbcType=TINYINT},
      </if>
      <if test="sendDaily != null">
        send_daily = #{sendDaily,jdbcType=TINYINT},
      </if>
      <if test="businessMode != null">
        business_mode = #{businessMode,jdbcType=TINYINT},
      </if>
      <if test="knowAboutUs != null">
        know_about_us = #{knowAboutUs,jdbcType=VARCHAR},
      </if>
      <if test="apptPerWeek != null">
        appt_per_week = #{apptPerWeek,jdbcType=TINYINT},
      </if>
      <if test="businessYears != null">
        business_years = #{businessYears,jdbcType=TINYINT},
      </if>
      <if test="moveFrom != null">
        move_from = #{moveFrom,jdbcType=TINYINT},
      </if>
      <if test="retailEnable != null">
        retail_enable = #{retailEnable,jdbcType=TINYINT},
      </if>
      <if test="notificationSoundEnable != null">
        notification_sound_enable = #{notificationSoundEnable,jdbcType=TINYINT},
      </if>
      <if test="contactEmail != null">
        contact_email = #{contactEmail,jdbcType=VARCHAR},
      </if>
      <if test="invitationCode != null">
        invitation_code = #{invitationCode,jdbcType=VARCHAR},
      </if>
      <if test="tiktok != null">
        tiktok = #{tiktok,jdbcType=VARCHAR},
      </if>
      <if test="staffAvailabilityType != null">
        staff_availability_type = #{staffAvailabilityType,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.moego.svc.organization.entity.MoeBusiness">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update moe_business
    set company_id = #{companyId,jdbcType=INTEGER},
      business_name = #{businessName,jdbcType=VARCHAR},
      phone_number = #{phoneNumber,jdbcType=VARCHAR},
      avatar_path = #{avatarPath,jdbcType=VARCHAR},
      website = #{website,jdbcType=VARCHAR},
      address = #{address,jdbcType=VARCHAR},
      address1 = #{address1,jdbcType=VARCHAR},
      address2 = #{address2,jdbcType=VARCHAR},
      address_city = #{addressCity,jdbcType=VARCHAR},
      address_state = #{addressState,jdbcType=VARCHAR},
      address_zipcode = #{addressZipcode,jdbcType=VARCHAR},
      address_country = #{addressCountry,jdbcType=VARCHAR},
      address_lat = #{addressLat,jdbcType=VARCHAR},
      address_lng = #{addressLng,jdbcType=VARCHAR},
      country = #{country,jdbcType=VARCHAR},
      country_alpha2_code = #{countryAlpha2Code,jdbcType=CHAR},
      country_code = #{countryCode,jdbcType=VARCHAR},
      currency_symbol = #{currencySymbol,jdbcType=VARCHAR},
      currency_code = #{currencyCode,jdbcType=VARCHAR},
      time_format_type = #{timeFormatType,jdbcType=TINYINT},
      unit_of_weight_type = #{unitOfWeightType,jdbcType=TINYINT},
      unit_of_distance_type = #{unitOfDistanceType,jdbcType=TINYINT},
      timezone_name = #{timezoneName,jdbcType=VARCHAR},
      timezone_seconds = #{timezoneSeconds,jdbcType=INTEGER},
      date_format_type = #{dateFormatType,jdbcType=TINYINT},
      calendar_format_type = #{calendarFormatType,jdbcType=TINYINT},
      number_format_type = #{numberFormatType,jdbcType=TINYINT},
      book_online_name = #{bookOnlineName,jdbcType=VARCHAR},
      app_type = #{appType,jdbcType=TINYINT},
      primary_pay_type = #{primaryPayType,jdbcType=TINYINT},
      source = #{source,jdbcType=TINYINT},
      clock_in_out_enable = #{clockInOutEnable,jdbcType=TINYINT},
      clock_in_out_notify = #{clockInOutNotify,jdbcType=TINYINT},
      is_enable_access_code = #{isEnableAccessCode,jdbcType=TINYINT},
      smart_schedule_max_dist = #{smartScheduleMaxDist,jdbcType=INTEGER},
      smart_schedule_max_time = #{smartScheduleMaxTime,jdbcType=INTEGER},
      service_area_enable = #{serviceAreaEnable,jdbcType=TINYINT},
      create_time = #{createTime,jdbcType=BIGINT},
      update_time = #{updateTime,jdbcType=BIGINT},
      facebook = #{facebook,jdbcType=VARCHAR},
      instagram = #{instagram,jdbcType=VARCHAR},
      google = #{google,jdbcType=VARCHAR},
      yelp = #{yelp,jdbcType=VARCHAR},
      smart_schedule_start_lat = #{smartScheduleStartLat,jdbcType=VARCHAR},
      smart_schedule_start_lng = #{smartScheduleStartLng,jdbcType=VARCHAR},
      smart_schedule_end_lat = #{smartScheduleEndLat,jdbcType=VARCHAR},
      smart_schedule_end_lng = #{smartScheduleEndLng,jdbcType=VARCHAR},
      service_range_max_dist = #{serviceRangeMaxDist,jdbcType=INTEGER},
      service_range_max_time = #{serviceRangeMaxTime,jdbcType=INTEGER},
      smart_schedule_service_range = #{smartScheduleServiceRange,jdbcType=INTEGER},
      smart_schedule_start_addr = #{smartScheduleStartAddr,jdbcType=VARCHAR},
      smart_schedule_end_addr = #{smartScheduleEndAddr,jdbcType=VARCHAR},
      service_range_center_addr = #{serviceRangeCenterAddr,jdbcType=VARCHAR},
      service_range_center_lat = #{serviceRangeCenterLat,jdbcType=VARCHAR},
      service_range_center_lng = #{serviceRangeCenterLng,jdbcType=VARCHAR},
      source_from = #{sourceFrom,jdbcType=TINYINT},
      message_send_by = #{messageSendBy,jdbcType=TINYINT},
      send_daily = #{sendDaily,jdbcType=TINYINT},
      business_mode = #{businessMode,jdbcType=TINYINT},
      know_about_us = #{knowAboutUs,jdbcType=VARCHAR},
      appt_per_week = #{apptPerWeek,jdbcType=TINYINT},
      business_years = #{businessYears,jdbcType=TINYINT},
      move_from = #{moveFrom,jdbcType=TINYINT},
      retail_enable = #{retailEnable,jdbcType=TINYINT},
      notification_sound_enable = #{notificationSoundEnable,jdbcType=TINYINT},
      contact_email = #{contactEmail,jdbcType=VARCHAR},
      invitation_code = #{invitationCode,jdbcType=VARCHAR},
      tiktok = #{tiktok,jdbcType=VARCHAR},
      staff_availability_type = #{staffAvailabilityType,jdbcType=INTEGER}
    where id = #{id,jdbcType=INTEGER}
  </update>
</mapper>