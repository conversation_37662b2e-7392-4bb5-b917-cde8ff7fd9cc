<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.moego.svc.organization.mapper.base.BaseMoeCompanyMapper">
  <resultMap id="BaseResultMap" type="com.moego.svc.organization.entity.MoeCompany">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="account_id" jdbcType="INTEGER" property="accountId" />
    <result column="country" jdbcType="VARCHAR" property="country" />
    <result column="create_time" jdbcType="BIGINT" property="createTime" />
    <result column="update_time" jdbcType="BIGINT" property="updateTime" />
    <result column="location_num" jdbcType="INTEGER" property="locationNum" />
    <result column="staff_num" jdbcType="INTEGER" property="staffNum" />
    <result column="vans_num" jdbcType="INTEGER" property="vansNum" />
    <result column="level" jdbcType="INTEGER" property="level" />
    <result column="is_new_pricing" jdbcType="INTEGER" property="isNewPricing" />
    <result column="enable_square" jdbcType="TINYINT" property="enableSquare" />
    <result column="enable_stripe_reader" jdbcType="TINYINT" property="enableStripeReader" />
    <result column="enterprise_id" jdbcType="INTEGER" property="enterpriseId" />
    <result column="currency_code" jdbcType="VARCHAR" property="currencyCode" />
    <result column="currency_symbol" jdbcType="VARCHAR" property="currencySymbol" />
    <result column="date_format_type" jdbcType="TINYINT" property="dateFormatType" />
    <result column="time_format_type" jdbcType="TINYINT" property="timeFormatType" />
    <result column="unit_of_weight_type" jdbcType="TINYINT" property="unitOfWeightType" />
    <result column="unit_of_distance_type" jdbcType="TINYINT" property="unitOfDistanceType" />
    <result column="notification_sound_enable" jdbcType="TINYINT" property="notificationSoundEnable" />
    <result column="country_alpha2_code" jdbcType="CHAR" property="countryAlpha2Code" />
    <result column="timezone_name" jdbcType="VARCHAR" property="timezoneName" />
    <result column="timezone_seconds" jdbcType="INTEGER" property="timezoneSeconds" />
    <result column="source" jdbcType="INTEGER" property="source" />
    <result column="know_about_us" jdbcType="VARCHAR" property="knowAboutUs" />
    <result column="company_type" jdbcType="TINYINT" property="companyType" />
    <result column="theme_color" jdbcType="VARCHAR" property="themeColor" />
    <result column="logo_path" jdbcType="VARCHAR" property="logoPath" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    id, name, account_id, country, create_time, update_time, location_num, staff_num, 
    vans_num, level, is_new_pricing, enable_square, enable_stripe_reader, enterprise_id, 
    currency_code, currency_symbol, date_format_type, time_format_type, unit_of_weight_type, 
    unit_of_distance_type, notification_sound_enable, country_alpha2_code, timezone_name, 
    timezone_seconds, source, know_about_us, company_type, theme_color, logo_path
  </sql>
  <select id="selectByExample" parameterType="com.moego.svc.organization.entity.MoeCompanyExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from moe_company
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select 
    <include refid="Base_Column_List" />
    from moe_company
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from moe_company
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <delete id="deleteByExample" parameterType="com.moego.svc.organization.entity.MoeCompanyExample">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from moe_company
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.moego.svc.organization.entity.MoeCompany">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into moe_company (name, account_id, country, 
      create_time, update_time, location_num, 
      staff_num, vans_num, level, 
      is_new_pricing, enable_square, enable_stripe_reader, 
      enterprise_id, currency_code, currency_symbol, 
      date_format_type, time_format_type, unit_of_weight_type, 
      unit_of_distance_type, notification_sound_enable, 
      country_alpha2_code, timezone_name, timezone_seconds, 
      source, know_about_us, company_type, 
      theme_color, logo_path)
    values (#{name,jdbcType=VARCHAR}, #{accountId,jdbcType=INTEGER}, #{country,jdbcType=VARCHAR}, 
      #{createTime,jdbcType=BIGINT}, #{updateTime,jdbcType=BIGINT}, #{locationNum,jdbcType=INTEGER}, 
      #{staffNum,jdbcType=INTEGER}, #{vansNum,jdbcType=INTEGER}, #{level,jdbcType=INTEGER}, 
      #{isNewPricing,jdbcType=INTEGER}, #{enableSquare,jdbcType=TINYINT}, #{enableStripeReader,jdbcType=TINYINT}, 
      #{enterpriseId,jdbcType=INTEGER}, #{currencyCode,jdbcType=VARCHAR}, #{currencySymbol,jdbcType=VARCHAR}, 
      #{dateFormatType,jdbcType=TINYINT}, #{timeFormatType,jdbcType=TINYINT}, #{unitOfWeightType,jdbcType=TINYINT}, 
      #{unitOfDistanceType,jdbcType=TINYINT}, #{notificationSoundEnable,jdbcType=TINYINT}, 
      #{countryAlpha2Code,jdbcType=CHAR}, #{timezoneName,jdbcType=VARCHAR}, #{timezoneSeconds,jdbcType=INTEGER}, 
      #{source,jdbcType=INTEGER}, #{knowAboutUs,jdbcType=VARCHAR}, #{companyType,jdbcType=TINYINT}, 
      #{themeColor,jdbcType=VARCHAR}, #{logoPath,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.moego.svc.organization.entity.MoeCompany">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into moe_company
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="name != null">
        name,
      </if>
      <if test="accountId != null">
        account_id,
      </if>
      <if test="country != null">
        country,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="locationNum != null">
        location_num,
      </if>
      <if test="staffNum != null">
        staff_num,
      </if>
      <if test="vansNum != null">
        vans_num,
      </if>
      <if test="level != null">
        level,
      </if>
      <if test="isNewPricing != null">
        is_new_pricing,
      </if>
      <if test="enableSquare != null">
        enable_square,
      </if>
      <if test="enableStripeReader != null">
        enable_stripe_reader,
      </if>
      <if test="enterpriseId != null">
        enterprise_id,
      </if>
      <if test="currencyCode != null">
        currency_code,
      </if>
      <if test="currencySymbol != null">
        currency_symbol,
      </if>
      <if test="dateFormatType != null">
        date_format_type,
      </if>
      <if test="timeFormatType != null">
        time_format_type,
      </if>
      <if test="unitOfWeightType != null">
        unit_of_weight_type,
      </if>
      <if test="unitOfDistanceType != null">
        unit_of_distance_type,
      </if>
      <if test="notificationSoundEnable != null">
        notification_sound_enable,
      </if>
      <if test="countryAlpha2Code != null">
        country_alpha2_code,
      </if>
      <if test="timezoneName != null">
        timezone_name,
      </if>
      <if test="timezoneSeconds != null">
        timezone_seconds,
      </if>
      <if test="source != null">
        source,
      </if>
      <if test="knowAboutUs != null">
        know_about_us,
      </if>
      <if test="companyType != null">
        company_type,
      </if>
      <if test="themeColor != null">
        theme_color,
      </if>
      <if test="logoPath != null">
        logo_path,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="name != null">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="accountId != null">
        #{accountId,jdbcType=INTEGER},
      </if>
      <if test="country != null">
        #{country,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=BIGINT},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=BIGINT},
      </if>
      <if test="locationNum != null">
        #{locationNum,jdbcType=INTEGER},
      </if>
      <if test="staffNum != null">
        #{staffNum,jdbcType=INTEGER},
      </if>
      <if test="vansNum != null">
        #{vansNum,jdbcType=INTEGER},
      </if>
      <if test="level != null">
        #{level,jdbcType=INTEGER},
      </if>
      <if test="isNewPricing != null">
        #{isNewPricing,jdbcType=INTEGER},
      </if>
      <if test="enableSquare != null">
        #{enableSquare,jdbcType=TINYINT},
      </if>
      <if test="enableStripeReader != null">
        #{enableStripeReader,jdbcType=TINYINT},
      </if>
      <if test="enterpriseId != null">
        #{enterpriseId,jdbcType=INTEGER},
      </if>
      <if test="currencyCode != null">
        #{currencyCode,jdbcType=VARCHAR},
      </if>
      <if test="currencySymbol != null">
        #{currencySymbol,jdbcType=VARCHAR},
      </if>
      <if test="dateFormatType != null">
        #{dateFormatType,jdbcType=TINYINT},
      </if>
      <if test="timeFormatType != null">
        #{timeFormatType,jdbcType=TINYINT},
      </if>
      <if test="unitOfWeightType != null">
        #{unitOfWeightType,jdbcType=TINYINT},
      </if>
      <if test="unitOfDistanceType != null">
        #{unitOfDistanceType,jdbcType=TINYINT},
      </if>
      <if test="notificationSoundEnable != null">
        #{notificationSoundEnable,jdbcType=TINYINT},
      </if>
      <if test="countryAlpha2Code != null">
        #{countryAlpha2Code,jdbcType=CHAR},
      </if>
      <if test="timezoneName != null">
        #{timezoneName,jdbcType=VARCHAR},
      </if>
      <if test="timezoneSeconds != null">
        #{timezoneSeconds,jdbcType=INTEGER},
      </if>
      <if test="source != null">
        #{source,jdbcType=INTEGER},
      </if>
      <if test="knowAboutUs != null">
        #{knowAboutUs,jdbcType=VARCHAR},
      </if>
      <if test="companyType != null">
        #{companyType,jdbcType=TINYINT},
      </if>
      <if test="themeColor != null">
        #{themeColor,jdbcType=VARCHAR},
      </if>
      <if test="logoPath != null">
        #{logoPath,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.moego.svc.organization.entity.MoeCompanyExample" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select count(*) from moe_company
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update moe_company
    <set>
      <if test="row.id != null">
        id = #{row.id,jdbcType=INTEGER},
      </if>
      <if test="row.name != null">
        name = #{row.name,jdbcType=VARCHAR},
      </if>
      <if test="row.accountId != null">
        account_id = #{row.accountId,jdbcType=INTEGER},
      </if>
      <if test="row.country != null">
        country = #{row.country,jdbcType=VARCHAR},
      </if>
      <if test="row.createTime != null">
        create_time = #{row.createTime,jdbcType=BIGINT},
      </if>
      <if test="row.updateTime != null">
        update_time = #{row.updateTime,jdbcType=BIGINT},
      </if>
      <if test="row.locationNum != null">
        location_num = #{row.locationNum,jdbcType=INTEGER},
      </if>
      <if test="row.staffNum != null">
        staff_num = #{row.staffNum,jdbcType=INTEGER},
      </if>
      <if test="row.vansNum != null">
        vans_num = #{row.vansNum,jdbcType=INTEGER},
      </if>
      <if test="row.level != null">
        level = #{row.level,jdbcType=INTEGER},
      </if>
      <if test="row.isNewPricing != null">
        is_new_pricing = #{row.isNewPricing,jdbcType=INTEGER},
      </if>
      <if test="row.enableSquare != null">
        enable_square = #{row.enableSquare,jdbcType=TINYINT},
      </if>
      <if test="row.enableStripeReader != null">
        enable_stripe_reader = #{row.enableStripeReader,jdbcType=TINYINT},
      </if>
      <if test="row.enterpriseId != null">
        enterprise_id = #{row.enterpriseId,jdbcType=INTEGER},
      </if>
      <if test="row.currencyCode != null">
        currency_code = #{row.currencyCode,jdbcType=VARCHAR},
      </if>
      <if test="row.currencySymbol != null">
        currency_symbol = #{row.currencySymbol,jdbcType=VARCHAR},
      </if>
      <if test="row.dateFormatType != null">
        date_format_type = #{row.dateFormatType,jdbcType=TINYINT},
      </if>
      <if test="row.timeFormatType != null">
        time_format_type = #{row.timeFormatType,jdbcType=TINYINT},
      </if>
      <if test="row.unitOfWeightType != null">
        unit_of_weight_type = #{row.unitOfWeightType,jdbcType=TINYINT},
      </if>
      <if test="row.unitOfDistanceType != null">
        unit_of_distance_type = #{row.unitOfDistanceType,jdbcType=TINYINT},
      </if>
      <if test="row.notificationSoundEnable != null">
        notification_sound_enable = #{row.notificationSoundEnable,jdbcType=TINYINT},
      </if>
      <if test="row.countryAlpha2Code != null">
        country_alpha2_code = #{row.countryAlpha2Code,jdbcType=CHAR},
      </if>
      <if test="row.timezoneName != null">
        timezone_name = #{row.timezoneName,jdbcType=VARCHAR},
      </if>
      <if test="row.timezoneSeconds != null">
        timezone_seconds = #{row.timezoneSeconds,jdbcType=INTEGER},
      </if>
      <if test="row.source != null">
        source = #{row.source,jdbcType=INTEGER},
      </if>
      <if test="row.knowAboutUs != null">
        know_about_us = #{row.knowAboutUs,jdbcType=VARCHAR},
      </if>
      <if test="row.companyType != null">
        company_type = #{row.companyType,jdbcType=TINYINT},
      </if>
      <if test="row.themeColor != null">
        theme_color = #{row.themeColor,jdbcType=VARCHAR},
      </if>
      <if test="row.logoPath != null">
        logo_path = #{row.logoPath,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update moe_company
    set id = #{row.id,jdbcType=INTEGER},
      name = #{row.name,jdbcType=VARCHAR},
      account_id = #{row.accountId,jdbcType=INTEGER},
      country = #{row.country,jdbcType=VARCHAR},
      create_time = #{row.createTime,jdbcType=BIGINT},
      update_time = #{row.updateTime,jdbcType=BIGINT},
      location_num = #{row.locationNum,jdbcType=INTEGER},
      staff_num = #{row.staffNum,jdbcType=INTEGER},
      vans_num = #{row.vansNum,jdbcType=INTEGER},
      level = #{row.level,jdbcType=INTEGER},
      is_new_pricing = #{row.isNewPricing,jdbcType=INTEGER},
      enable_square = #{row.enableSquare,jdbcType=TINYINT},
      enable_stripe_reader = #{row.enableStripeReader,jdbcType=TINYINT},
      enterprise_id = #{row.enterpriseId,jdbcType=INTEGER},
      currency_code = #{row.currencyCode,jdbcType=VARCHAR},
      currency_symbol = #{row.currencySymbol,jdbcType=VARCHAR},
      date_format_type = #{row.dateFormatType,jdbcType=TINYINT},
      time_format_type = #{row.timeFormatType,jdbcType=TINYINT},
      unit_of_weight_type = #{row.unitOfWeightType,jdbcType=TINYINT},
      unit_of_distance_type = #{row.unitOfDistanceType,jdbcType=TINYINT},
      notification_sound_enable = #{row.notificationSoundEnable,jdbcType=TINYINT},
      country_alpha2_code = #{row.countryAlpha2Code,jdbcType=CHAR},
      timezone_name = #{row.timezoneName,jdbcType=VARCHAR},
      timezone_seconds = #{row.timezoneSeconds,jdbcType=INTEGER},
      source = #{row.source,jdbcType=INTEGER},
      know_about_us = #{row.knowAboutUs,jdbcType=VARCHAR},
      company_type = #{row.companyType,jdbcType=TINYINT},
      theme_color = #{row.themeColor,jdbcType=VARCHAR},
      logo_path = #{row.logoPath,jdbcType=VARCHAR}
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.moego.svc.organization.entity.MoeCompany">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update moe_company
    <set>
      <if test="name != null">
        name = #{name,jdbcType=VARCHAR},
      </if>
      <if test="accountId != null">
        account_id = #{accountId,jdbcType=INTEGER},
      </if>
      <if test="country != null">
        country = #{country,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=BIGINT},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=BIGINT},
      </if>
      <if test="locationNum != null">
        location_num = #{locationNum,jdbcType=INTEGER},
      </if>
      <if test="staffNum != null">
        staff_num = #{staffNum,jdbcType=INTEGER},
      </if>
      <if test="vansNum != null">
        vans_num = #{vansNum,jdbcType=INTEGER},
      </if>
      <if test="level != null">
        level = #{level,jdbcType=INTEGER},
      </if>
      <if test="isNewPricing != null">
        is_new_pricing = #{isNewPricing,jdbcType=INTEGER},
      </if>
      <if test="enableSquare != null">
        enable_square = #{enableSquare,jdbcType=TINYINT},
      </if>
      <if test="enableStripeReader != null">
        enable_stripe_reader = #{enableStripeReader,jdbcType=TINYINT},
      </if>
      <if test="enterpriseId != null">
        enterprise_id = #{enterpriseId,jdbcType=INTEGER},
      </if>
      <if test="currencyCode != null">
        currency_code = #{currencyCode,jdbcType=VARCHAR},
      </if>
      <if test="currencySymbol != null">
        currency_symbol = #{currencySymbol,jdbcType=VARCHAR},
      </if>
      <if test="dateFormatType != null">
        date_format_type = #{dateFormatType,jdbcType=TINYINT},
      </if>
      <if test="timeFormatType != null">
        time_format_type = #{timeFormatType,jdbcType=TINYINT},
      </if>
      <if test="unitOfWeightType != null">
        unit_of_weight_type = #{unitOfWeightType,jdbcType=TINYINT},
      </if>
      <if test="unitOfDistanceType != null">
        unit_of_distance_type = #{unitOfDistanceType,jdbcType=TINYINT},
      </if>
      <if test="notificationSoundEnable != null">
        notification_sound_enable = #{notificationSoundEnable,jdbcType=TINYINT},
      </if>
      <if test="countryAlpha2Code != null">
        country_alpha2_code = #{countryAlpha2Code,jdbcType=CHAR},
      </if>
      <if test="timezoneName != null">
        timezone_name = #{timezoneName,jdbcType=VARCHAR},
      </if>
      <if test="timezoneSeconds != null">
        timezone_seconds = #{timezoneSeconds,jdbcType=INTEGER},
      </if>
      <if test="source != null">
        source = #{source,jdbcType=INTEGER},
      </if>
      <if test="knowAboutUs != null">
        know_about_us = #{knowAboutUs,jdbcType=VARCHAR},
      </if>
      <if test="companyType != null">
        company_type = #{companyType,jdbcType=TINYINT},
      </if>
      <if test="themeColor != null">
        theme_color = #{themeColor,jdbcType=VARCHAR},
      </if>
      <if test="logoPath != null">
        logo_path = #{logoPath,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.moego.svc.organization.entity.MoeCompany">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update moe_company
    set name = #{name,jdbcType=VARCHAR},
      account_id = #{accountId,jdbcType=INTEGER},
      country = #{country,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=BIGINT},
      update_time = #{updateTime,jdbcType=BIGINT},
      location_num = #{locationNum,jdbcType=INTEGER},
      staff_num = #{staffNum,jdbcType=INTEGER},
      vans_num = #{vansNum,jdbcType=INTEGER},
      level = #{level,jdbcType=INTEGER},
      is_new_pricing = #{isNewPricing,jdbcType=INTEGER},
      enable_square = #{enableSquare,jdbcType=TINYINT},
      enable_stripe_reader = #{enableStripeReader,jdbcType=TINYINT},
      enterprise_id = #{enterpriseId,jdbcType=INTEGER},
      currency_code = #{currencyCode,jdbcType=VARCHAR},
      currency_symbol = #{currencySymbol,jdbcType=VARCHAR},
      date_format_type = #{dateFormatType,jdbcType=TINYINT},
      time_format_type = #{timeFormatType,jdbcType=TINYINT},
      unit_of_weight_type = #{unitOfWeightType,jdbcType=TINYINT},
      unit_of_distance_type = #{unitOfDistanceType,jdbcType=TINYINT},
      notification_sound_enable = #{notificationSoundEnable,jdbcType=TINYINT},
      country_alpha2_code = #{countryAlpha2Code,jdbcType=CHAR},
      timezone_name = #{timezoneName,jdbcType=VARCHAR},
      timezone_seconds = #{timezoneSeconds,jdbcType=INTEGER},
      source = #{source,jdbcType=INTEGER},
      know_about_us = #{knowAboutUs,jdbcType=VARCHAR},
      company_type = #{companyType,jdbcType=TINYINT},
      theme_color = #{themeColor,jdbcType=VARCHAR},
      logo_path = #{logoPath,jdbcType=VARCHAR}
    where id = #{id,jdbcType=INTEGER}
  </update>
</mapper>