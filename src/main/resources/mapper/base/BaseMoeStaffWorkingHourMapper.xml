<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.moego.svc.organization.mapper.base.BaseMoeStaffWorkingHourMapper">
  <resultMap id="BaseResultMap" type="com.moego.svc.organization.entity.MoeStaffWorkingHour">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="business_id" jdbcType="INTEGER" property="businessId" />
    <result column="staff_id" jdbcType="INTEGER" property="staffId" />
    <result column="schedule_type" jdbcType="TINYINT" property="scheduleType" />
    <result column="start_date" jdbcType="VARCHAR" property="startDate" />
    <result column="end_date" jdbcType="VARCHAR" property="endDate" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="company_id" jdbcType="BIGINT" property="companyId" />
  </resultMap>
  <resultMap extends="BaseResultMap" id="ResultMapWithBLOBs" type="com.moego.svc.organization.entity.MoeStaffWorkingHour">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <result column="first_week" jdbcType="LONGVARCHAR" property="firstWeek" />
    <result column="second_week" jdbcType="LONGVARCHAR" property="secondWeek" />
    <result column="third_week" jdbcType="LONGVARCHAR" property="thirdWeek" />
    <result column="forth_week" jdbcType="LONGVARCHAR" property="forthWeek" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    id, business_id, staff_id, schedule_type, start_date, end_date, create_time, update_time, 
    company_id
  </sql>
  <sql id="Blob_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    first_week, second_week, third_week, forth_week
  </sql>
  <select id="selectByExampleWithBLOBs" parameterType="com.moego.svc.organization.entity.MoeStaffWorkingHourExample" resultMap="ResultMapWithBLOBs">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from moe_staff_working_hour
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByExample" parameterType="com.moego.svc.organization.entity.MoeStaffWorkingHourExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from moe_staff_working_hour
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="ResultMapWithBLOBs">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select 
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from moe_staff_working_hour
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from moe_staff_working_hour
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <delete id="deleteByExample" parameterType="com.moego.svc.organization.entity.MoeStaffWorkingHourExample">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from moe_staff_working_hour
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.moego.svc.organization.entity.MoeStaffWorkingHour">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into moe_staff_working_hour (business_id, staff_id, schedule_type, 
      start_date, end_date, create_time, 
      update_time, company_id, first_week, 
      second_week, third_week, forth_week
      )
    values (#{businessId,jdbcType=INTEGER}, #{staffId,jdbcType=INTEGER}, #{scheduleType,jdbcType=TINYINT}, 
      #{startDate,jdbcType=VARCHAR}, #{endDate,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, 
      #{updateTime,jdbcType=TIMESTAMP}, #{companyId,jdbcType=BIGINT}, #{firstWeek,jdbcType=LONGVARCHAR}, 
      #{secondWeek,jdbcType=LONGVARCHAR}, #{thirdWeek,jdbcType=LONGVARCHAR}, #{forthWeek,jdbcType=LONGVARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.moego.svc.organization.entity.MoeStaffWorkingHour">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into moe_staff_working_hour
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="businessId != null">
        business_id,
      </if>
      <if test="staffId != null">
        staff_id,
      </if>
      <if test="scheduleType != null">
        schedule_type,
      </if>
      <if test="startDate != null">
        start_date,
      </if>
      <if test="endDate != null">
        end_date,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="companyId != null">
        company_id,
      </if>
      <if test="firstWeek != null">
        first_week,
      </if>
      <if test="secondWeek != null">
        second_week,
      </if>
      <if test="thirdWeek != null">
        third_week,
      </if>
      <if test="forthWeek != null">
        forth_week,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="businessId != null">
        #{businessId,jdbcType=INTEGER},
      </if>
      <if test="staffId != null">
        #{staffId,jdbcType=INTEGER},
      </if>
      <if test="scheduleType != null">
        #{scheduleType,jdbcType=TINYINT},
      </if>
      <if test="startDate != null">
        #{startDate,jdbcType=VARCHAR},
      </if>
      <if test="endDate != null">
        #{endDate,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="companyId != null">
        #{companyId,jdbcType=BIGINT},
      </if>
      <if test="firstWeek != null">
        #{firstWeek,jdbcType=LONGVARCHAR},
      </if>
      <if test="secondWeek != null">
        #{secondWeek,jdbcType=LONGVARCHAR},
      </if>
      <if test="thirdWeek != null">
        #{thirdWeek,jdbcType=LONGVARCHAR},
      </if>
      <if test="forthWeek != null">
        #{forthWeek,jdbcType=LONGVARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.moego.svc.organization.entity.MoeStaffWorkingHourExample" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select count(*) from moe_staff_working_hour
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update moe_staff_working_hour
    <set>
      <if test="row.id != null">
        id = #{row.id,jdbcType=INTEGER},
      </if>
      <if test="row.businessId != null">
        business_id = #{row.businessId,jdbcType=INTEGER},
      </if>
      <if test="row.staffId != null">
        staff_id = #{row.staffId,jdbcType=INTEGER},
      </if>
      <if test="row.scheduleType != null">
        schedule_type = #{row.scheduleType,jdbcType=TINYINT},
      </if>
      <if test="row.startDate != null">
        start_date = #{row.startDate,jdbcType=VARCHAR},
      </if>
      <if test="row.endDate != null">
        end_date = #{row.endDate,jdbcType=VARCHAR},
      </if>
      <if test="row.createTime != null">
        create_time = #{row.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="row.updateTime != null">
        update_time = #{row.updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="row.companyId != null">
        company_id = #{row.companyId,jdbcType=BIGINT},
      </if>
      <if test="row.firstWeek != null">
        first_week = #{row.firstWeek,jdbcType=LONGVARCHAR},
      </if>
      <if test="row.secondWeek != null">
        second_week = #{row.secondWeek,jdbcType=LONGVARCHAR},
      </if>
      <if test="row.thirdWeek != null">
        third_week = #{row.thirdWeek,jdbcType=LONGVARCHAR},
      </if>
      <if test="row.forthWeek != null">
        forth_week = #{row.forthWeek,jdbcType=LONGVARCHAR},
      </if>
    </set>
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExampleWithBLOBs" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update moe_staff_working_hour
    set id = #{row.id,jdbcType=INTEGER},
      business_id = #{row.businessId,jdbcType=INTEGER},
      staff_id = #{row.staffId,jdbcType=INTEGER},
      schedule_type = #{row.scheduleType,jdbcType=TINYINT},
      start_date = #{row.startDate,jdbcType=VARCHAR},
      end_date = #{row.endDate,jdbcType=VARCHAR},
      create_time = #{row.createTime,jdbcType=TIMESTAMP},
      update_time = #{row.updateTime,jdbcType=TIMESTAMP},
      company_id = #{row.companyId,jdbcType=BIGINT},
      first_week = #{row.firstWeek,jdbcType=LONGVARCHAR},
      second_week = #{row.secondWeek,jdbcType=LONGVARCHAR},
      third_week = #{row.thirdWeek,jdbcType=LONGVARCHAR},
      forth_week = #{row.forthWeek,jdbcType=LONGVARCHAR}
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update moe_staff_working_hour
    set id = #{row.id,jdbcType=INTEGER},
      business_id = #{row.businessId,jdbcType=INTEGER},
      staff_id = #{row.staffId,jdbcType=INTEGER},
      schedule_type = #{row.scheduleType,jdbcType=TINYINT},
      start_date = #{row.startDate,jdbcType=VARCHAR},
      end_date = #{row.endDate,jdbcType=VARCHAR},
      create_time = #{row.createTime,jdbcType=TIMESTAMP},
      update_time = #{row.updateTime,jdbcType=TIMESTAMP},
      company_id = #{row.companyId,jdbcType=BIGINT}
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.moego.svc.organization.entity.MoeStaffWorkingHour">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update moe_staff_working_hour
    <set>
      <if test="businessId != null">
        business_id = #{businessId,jdbcType=INTEGER},
      </if>
      <if test="staffId != null">
        staff_id = #{staffId,jdbcType=INTEGER},
      </if>
      <if test="scheduleType != null">
        schedule_type = #{scheduleType,jdbcType=TINYINT},
      </if>
      <if test="startDate != null">
        start_date = #{startDate,jdbcType=VARCHAR},
      </if>
      <if test="endDate != null">
        end_date = #{endDate,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="companyId != null">
        company_id = #{companyId,jdbcType=BIGINT},
      </if>
      <if test="firstWeek != null">
        first_week = #{firstWeek,jdbcType=LONGVARCHAR},
      </if>
      <if test="secondWeek != null">
        second_week = #{secondWeek,jdbcType=LONGVARCHAR},
      </if>
      <if test="thirdWeek != null">
        third_week = #{thirdWeek,jdbcType=LONGVARCHAR},
      </if>
      <if test="forthWeek != null">
        forth_week = #{forthWeek,jdbcType=LONGVARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKeyWithBLOBs" parameterType="com.moego.svc.organization.entity.MoeStaffWorkingHour">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update moe_staff_working_hour
    set business_id = #{businessId,jdbcType=INTEGER},
      staff_id = #{staffId,jdbcType=INTEGER},
      schedule_type = #{scheduleType,jdbcType=TINYINT},
      start_date = #{startDate,jdbcType=VARCHAR},
      end_date = #{endDate,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      company_id = #{companyId,jdbcType=BIGINT},
      first_week = #{firstWeek,jdbcType=LONGVARCHAR},
      second_week = #{secondWeek,jdbcType=LONGVARCHAR},
      third_week = #{thirdWeek,jdbcType=LONGVARCHAR},
      forth_week = #{forthWeek,jdbcType=LONGVARCHAR}
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.moego.svc.organization.entity.MoeStaffWorkingHour">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update moe_staff_working_hour
    set business_id = #{businessId,jdbcType=INTEGER},
      staff_id = #{staffId,jdbcType=INTEGER},
      schedule_type = #{scheduleType,jdbcType=TINYINT},
      start_date = #{startDate,jdbcType=VARCHAR},
      end_date = #{endDate,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      company_id = #{companyId,jdbcType=BIGINT}
    where id = #{id,jdbcType=INTEGER}
  </update>
</mapper>