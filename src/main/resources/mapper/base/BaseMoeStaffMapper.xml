<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.moego.svc.organization.mapper.base.BaseMoeStaffMapper">
  <resultMap id="BaseResultMap" type="com.moego.svc.organization.entity.MoeStaff">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="business_id" jdbcType="INTEGER" property="businessId" />
    <result column="account_id" jdbcType="INTEGER" property="accountId" />
    <result column="role_id" jdbcType="INTEGER" property="roleId" />
    <result column="avatar_path" jdbcType="VARCHAR" property="avatarPath" />
    <result column="first_name" jdbcType="VARCHAR" property="firstName" />
    <result column="last_name" jdbcType="VARCHAR" property="lastName" />
    <result column="employee_category" jdbcType="TINYINT" property="employeeCategory" />
    <result column="phone_number" jdbcType="VARCHAR" property="phoneNumber" />
    <result column="hire_date" jdbcType="BIGINT" property="hireDate" />
    <result column="fire_date" jdbcType="BIGINT" property="fireDate" />
    <result column="allow_login" jdbcType="TINYINT" property="allowLogin" />
    <result column="group_leader_id" jdbcType="INTEGER" property="groupLeaderId" />
    <result column="note" jdbcType="VARCHAR" property="note" />
    <result column="inactive" jdbcType="TINYINT" property="inactive" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="create_by_id" jdbcType="INTEGER" property="createById" />
    <result column="sort" jdbcType="INTEGER" property="sort" />
    <result column="book_online_available" jdbcType="TINYINT" property="bookOnlineAvailable" />
    <result column="create_time" jdbcType="BIGINT" property="createTime" />
    <result column="update_time" jdbcType="BIGINT" property="updateTime" />
    <result column="show_on_calendar" jdbcType="TINYINT" property="showOnCalendar" />
    <result column="show_calendar_staff_all" jdbcType="TINYINT" property="showCalendarStaffAll" />
    <result column="access_code" jdbcType="VARCHAR" property="accessCode" />
    <result column="token" jdbcType="VARCHAR" property="token" />
    <result column="invite_code" jdbcType="VARCHAR" property="inviteCode" />
    <result column="account_last_visited_at" jdbcType="BIGINT" property="accountLastVisitedAt" />
    <result column="account_sort" jdbcType="INTEGER" property="accountSort" />
    <result column="company_id" jdbcType="INTEGER" property="companyId" />
    <result column="enterprise_id" jdbcType="INTEGER" property="enterpriseId" />
    <result column="working_in_all_locations" jdbcType="BIT" property="workingInAllLocations" />
    <result column="color_code" jdbcType="VARCHAR" property="colorCode" />
    <result column="last_visit_business_id" jdbcType="INTEGER" property="lastVisitBusinessId" />
    <result column="access_all_working_locations_staff" jdbcType="TINYINT" property="accessAllWorkingLocationsStaff" />
    <result column="profile_email" jdbcType="VARCHAR" property="profileEmail" />
    <result column="require_access_code" jdbcType="BIT" property="requireAccessCode" />
    <result column="is_shown_on_all_calendar" jdbcType="BIT" property="isShownOnAllCalendar" />
    <result column="source" jdbcType="INTEGER" property="source" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    id, business_id, account_id, role_id, avatar_path, first_name, last_name, employee_category, 
    phone_number, hire_date, fire_date, allow_login, group_leader_id, note, inactive, 
    status, create_by_id, sort, book_online_available, create_time, update_time, show_on_calendar, 
    show_calendar_staff_all, access_code, token, invite_code, account_last_visited_at, 
    account_sort, company_id, enterprise_id, working_in_all_locations, color_code, last_visit_business_id, 
    access_all_working_locations_staff, profile_email, require_access_code, is_shown_on_all_calendar, 
    source
  </sql>
  <select id="selectByExample" parameterType="com.moego.svc.organization.entity.MoeStaffExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from moe_staff
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select 
    <include refid="Base_Column_List" />
    from moe_staff
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from moe_staff
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <delete id="deleteByExample" parameterType="com.moego.svc.organization.entity.MoeStaffExample">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from moe_staff
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.moego.svc.organization.entity.MoeStaff">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into moe_staff (business_id, account_id, role_id, 
      avatar_path, first_name, last_name, 
      employee_category, phone_number, hire_date, 
      fire_date, allow_login, group_leader_id, 
      note, inactive, status, 
      create_by_id, sort, book_online_available, 
      create_time, update_time, show_on_calendar, 
      show_calendar_staff_all, access_code, token, 
      invite_code, account_last_visited_at, account_sort, 
      company_id, enterprise_id, working_in_all_locations, 
      color_code, last_visit_business_id, access_all_working_locations_staff, 
      profile_email, require_access_code, is_shown_on_all_calendar, 
      source)
    values (#{businessId,jdbcType=INTEGER}, #{accountId,jdbcType=INTEGER}, #{roleId,jdbcType=INTEGER}, 
      #{avatarPath,jdbcType=VARCHAR}, #{firstName,jdbcType=VARCHAR}, #{lastName,jdbcType=VARCHAR}, 
      #{employeeCategory,jdbcType=TINYINT}, #{phoneNumber,jdbcType=VARCHAR}, #{hireDate,jdbcType=BIGINT}, 
      #{fireDate,jdbcType=BIGINT}, #{allowLogin,jdbcType=TINYINT}, #{groupLeaderId,jdbcType=INTEGER}, 
      #{note,jdbcType=VARCHAR}, #{inactive,jdbcType=TINYINT}, #{status,jdbcType=TINYINT}, 
      #{createById,jdbcType=INTEGER}, #{sort,jdbcType=INTEGER}, #{bookOnlineAvailable,jdbcType=TINYINT}, 
      #{createTime,jdbcType=BIGINT}, #{updateTime,jdbcType=BIGINT}, #{showOnCalendar,jdbcType=TINYINT}, 
      #{showCalendarStaffAll,jdbcType=TINYINT}, #{accessCode,jdbcType=VARCHAR}, #{token,jdbcType=VARCHAR}, 
      #{inviteCode,jdbcType=VARCHAR}, #{accountLastVisitedAt,jdbcType=BIGINT}, #{accountSort,jdbcType=INTEGER}, 
      #{companyId,jdbcType=INTEGER}, #{enterpriseId,jdbcType=INTEGER}, #{workingInAllLocations,jdbcType=BIT}, 
      #{colorCode,jdbcType=VARCHAR}, #{lastVisitBusinessId,jdbcType=INTEGER}, #{accessAllWorkingLocationsStaff,jdbcType=TINYINT}, 
      #{profileEmail,jdbcType=VARCHAR}, #{requireAccessCode,jdbcType=BIT}, #{isShownOnAllCalendar,jdbcType=BIT}, 
      #{source,jdbcType=INTEGER})
  </insert>
  <insert id="insertSelective" parameterType="com.moego.svc.organization.entity.MoeStaff">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into moe_staff
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="businessId != null">
        business_id,
      </if>
      <if test="accountId != null">
        account_id,
      </if>
      <if test="roleId != null">
        role_id,
      </if>
      <if test="avatarPath != null">
        avatar_path,
      </if>
      <if test="firstName != null">
        first_name,
      </if>
      <if test="lastName != null">
        last_name,
      </if>
      <if test="employeeCategory != null">
        employee_category,
      </if>
      <if test="phoneNumber != null">
        phone_number,
      </if>
      <if test="hireDate != null">
        hire_date,
      </if>
      <if test="fireDate != null">
        fire_date,
      </if>
      <if test="allowLogin != null">
        allow_login,
      </if>
      <if test="groupLeaderId != null">
        group_leader_id,
      </if>
      <if test="note != null">
        note,
      </if>
      <if test="inactive != null">
        inactive,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="createById != null">
        create_by_id,
      </if>
      <if test="sort != null">
        sort,
      </if>
      <if test="bookOnlineAvailable != null">
        book_online_available,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="showOnCalendar != null">
        show_on_calendar,
      </if>
      <if test="showCalendarStaffAll != null">
        show_calendar_staff_all,
      </if>
      <if test="accessCode != null">
        access_code,
      </if>
      <if test="token != null">
        token,
      </if>
      <if test="inviteCode != null">
        invite_code,
      </if>
      <if test="accountLastVisitedAt != null">
        account_last_visited_at,
      </if>
      <if test="accountSort != null">
        account_sort,
      </if>
      <if test="companyId != null">
        company_id,
      </if>
      <if test="enterpriseId != null">
        enterprise_id,
      </if>
      <if test="workingInAllLocations != null">
        working_in_all_locations,
      </if>
      <if test="colorCode != null">
        color_code,
      </if>
      <if test="lastVisitBusinessId != null">
        last_visit_business_id,
      </if>
      <if test="accessAllWorkingLocationsStaff != null">
        access_all_working_locations_staff,
      </if>
      <if test="profileEmail != null">
        profile_email,
      </if>
      <if test="requireAccessCode != null">
        require_access_code,
      </if>
      <if test="isShownOnAllCalendar != null">
        is_shown_on_all_calendar,
      </if>
      <if test="source != null">
        source,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="businessId != null">
        #{businessId,jdbcType=INTEGER},
      </if>
      <if test="accountId != null">
        #{accountId,jdbcType=INTEGER},
      </if>
      <if test="roleId != null">
        #{roleId,jdbcType=INTEGER},
      </if>
      <if test="avatarPath != null">
        #{avatarPath,jdbcType=VARCHAR},
      </if>
      <if test="firstName != null">
        #{firstName,jdbcType=VARCHAR},
      </if>
      <if test="lastName != null">
        #{lastName,jdbcType=VARCHAR},
      </if>
      <if test="employeeCategory != null">
        #{employeeCategory,jdbcType=TINYINT},
      </if>
      <if test="phoneNumber != null">
        #{phoneNumber,jdbcType=VARCHAR},
      </if>
      <if test="hireDate != null">
        #{hireDate,jdbcType=BIGINT},
      </if>
      <if test="fireDate != null">
        #{fireDate,jdbcType=BIGINT},
      </if>
      <if test="allowLogin != null">
        #{allowLogin,jdbcType=TINYINT},
      </if>
      <if test="groupLeaderId != null">
        #{groupLeaderId,jdbcType=INTEGER},
      </if>
      <if test="note != null">
        #{note,jdbcType=VARCHAR},
      </if>
      <if test="inactive != null">
        #{inactive,jdbcType=TINYINT},
      </if>
      <if test="status != null">
        #{status,jdbcType=TINYINT},
      </if>
      <if test="createById != null">
        #{createById,jdbcType=INTEGER},
      </if>
      <if test="sort != null">
        #{sort,jdbcType=INTEGER},
      </if>
      <if test="bookOnlineAvailable != null">
        #{bookOnlineAvailable,jdbcType=TINYINT},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=BIGINT},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=BIGINT},
      </if>
      <if test="showOnCalendar != null">
        #{showOnCalendar,jdbcType=TINYINT},
      </if>
      <if test="showCalendarStaffAll != null">
        #{showCalendarStaffAll,jdbcType=TINYINT},
      </if>
      <if test="accessCode != null">
        #{accessCode,jdbcType=VARCHAR},
      </if>
      <if test="token != null">
        #{token,jdbcType=VARCHAR},
      </if>
      <if test="inviteCode != null">
        #{inviteCode,jdbcType=VARCHAR},
      </if>
      <if test="accountLastVisitedAt != null">
        #{accountLastVisitedAt,jdbcType=BIGINT},
      </if>
      <if test="accountSort != null">
        #{accountSort,jdbcType=INTEGER},
      </if>
      <if test="companyId != null">
        #{companyId,jdbcType=INTEGER},
      </if>
      <if test="enterpriseId != null">
        #{enterpriseId,jdbcType=INTEGER},
      </if>
      <if test="workingInAllLocations != null">
        #{workingInAllLocations,jdbcType=BIT},
      </if>
      <if test="colorCode != null">
        #{colorCode,jdbcType=VARCHAR},
      </if>
      <if test="lastVisitBusinessId != null">
        #{lastVisitBusinessId,jdbcType=INTEGER},
      </if>
      <if test="accessAllWorkingLocationsStaff != null">
        #{accessAllWorkingLocationsStaff,jdbcType=TINYINT},
      </if>
      <if test="profileEmail != null">
        #{profileEmail,jdbcType=VARCHAR},
      </if>
      <if test="requireAccessCode != null">
        #{requireAccessCode,jdbcType=BIT},
      </if>
      <if test="isShownOnAllCalendar != null">
        #{isShownOnAllCalendar,jdbcType=BIT},
      </if>
      <if test="source != null">
        #{source,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.moego.svc.organization.entity.MoeStaffExample" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select count(*) from moe_staff
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update moe_staff
    <set>
      <if test="row.id != null">
        id = #{row.id,jdbcType=INTEGER},
      </if>
      <if test="row.businessId != null">
        business_id = #{row.businessId,jdbcType=INTEGER},
      </if>
      <if test="row.accountId != null">
        account_id = #{row.accountId,jdbcType=INTEGER},
      </if>
      <if test="row.roleId != null">
        role_id = #{row.roleId,jdbcType=INTEGER},
      </if>
      <if test="row.avatarPath != null">
        avatar_path = #{row.avatarPath,jdbcType=VARCHAR},
      </if>
      <if test="row.firstName != null">
        first_name = #{row.firstName,jdbcType=VARCHAR},
      </if>
      <if test="row.lastName != null">
        last_name = #{row.lastName,jdbcType=VARCHAR},
      </if>
      <if test="row.employeeCategory != null">
        employee_category = #{row.employeeCategory,jdbcType=TINYINT},
      </if>
      <if test="row.phoneNumber != null">
        phone_number = #{row.phoneNumber,jdbcType=VARCHAR},
      </if>
      <if test="row.hireDate != null">
        hire_date = #{row.hireDate,jdbcType=BIGINT},
      </if>
      <if test="row.fireDate != null">
        fire_date = #{row.fireDate,jdbcType=BIGINT},
      </if>
      <if test="row.allowLogin != null">
        allow_login = #{row.allowLogin,jdbcType=TINYINT},
      </if>
      <if test="row.groupLeaderId != null">
        group_leader_id = #{row.groupLeaderId,jdbcType=INTEGER},
      </if>
      <if test="row.note != null">
        note = #{row.note,jdbcType=VARCHAR},
      </if>
      <if test="row.inactive != null">
        inactive = #{row.inactive,jdbcType=TINYINT},
      </if>
      <if test="row.status != null">
        status = #{row.status,jdbcType=TINYINT},
      </if>
      <if test="row.createById != null">
        create_by_id = #{row.createById,jdbcType=INTEGER},
      </if>
      <if test="row.sort != null">
        sort = #{row.sort,jdbcType=INTEGER},
      </if>
      <if test="row.bookOnlineAvailable != null">
        book_online_available = #{row.bookOnlineAvailable,jdbcType=TINYINT},
      </if>
      <if test="row.createTime != null">
        create_time = #{row.createTime,jdbcType=BIGINT},
      </if>
      <if test="row.updateTime != null">
        update_time = #{row.updateTime,jdbcType=BIGINT},
      </if>
      <if test="row.showOnCalendar != null">
        show_on_calendar = #{row.showOnCalendar,jdbcType=TINYINT},
      </if>
      <if test="row.showCalendarStaffAll != null">
        show_calendar_staff_all = #{row.showCalendarStaffAll,jdbcType=TINYINT},
      </if>
      <if test="row.accessCode != null">
        access_code = #{row.accessCode,jdbcType=VARCHAR},
      </if>
      <if test="row.token != null">
        token = #{row.token,jdbcType=VARCHAR},
      </if>
      <if test="row.inviteCode != null">
        invite_code = #{row.inviteCode,jdbcType=VARCHAR},
      </if>
      <if test="row.accountLastVisitedAt != null">
        account_last_visited_at = #{row.accountLastVisitedAt,jdbcType=BIGINT},
      </if>
      <if test="row.accountSort != null">
        account_sort = #{row.accountSort,jdbcType=INTEGER},
      </if>
      <if test="row.companyId != null">
        company_id = #{row.companyId,jdbcType=INTEGER},
      </if>
      <if test="row.enterpriseId != null">
        enterprise_id = #{row.enterpriseId,jdbcType=INTEGER},
      </if>
      <if test="row.workingInAllLocations != null">
        working_in_all_locations = #{row.workingInAllLocations,jdbcType=BIT},
      </if>
      <if test="row.colorCode != null">
        color_code = #{row.colorCode,jdbcType=VARCHAR},
      </if>
      <if test="row.lastVisitBusinessId != null">
        last_visit_business_id = #{row.lastVisitBusinessId,jdbcType=INTEGER},
      </if>
      <if test="row.accessAllWorkingLocationsStaff != null">
        access_all_working_locations_staff = #{row.accessAllWorkingLocationsStaff,jdbcType=TINYINT},
      </if>
      <if test="row.profileEmail != null">
        profile_email = #{row.profileEmail,jdbcType=VARCHAR},
      </if>
      <if test="row.requireAccessCode != null">
        require_access_code = #{row.requireAccessCode,jdbcType=BIT},
      </if>
      <if test="row.isShownOnAllCalendar != null">
        is_shown_on_all_calendar = #{row.isShownOnAllCalendar,jdbcType=BIT},
      </if>
      <if test="row.source != null">
        source = #{row.source,jdbcType=INTEGER},
      </if>
    </set>
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update moe_staff
    set id = #{row.id,jdbcType=INTEGER},
      business_id = #{row.businessId,jdbcType=INTEGER},
      account_id = #{row.accountId,jdbcType=INTEGER},
      role_id = #{row.roleId,jdbcType=INTEGER},
      avatar_path = #{row.avatarPath,jdbcType=VARCHAR},
      first_name = #{row.firstName,jdbcType=VARCHAR},
      last_name = #{row.lastName,jdbcType=VARCHAR},
      employee_category = #{row.employeeCategory,jdbcType=TINYINT},
      phone_number = #{row.phoneNumber,jdbcType=VARCHAR},
      hire_date = #{row.hireDate,jdbcType=BIGINT},
      fire_date = #{row.fireDate,jdbcType=BIGINT},
      allow_login = #{row.allowLogin,jdbcType=TINYINT},
      group_leader_id = #{row.groupLeaderId,jdbcType=INTEGER},
      note = #{row.note,jdbcType=VARCHAR},
      inactive = #{row.inactive,jdbcType=TINYINT},
      status = #{row.status,jdbcType=TINYINT},
      create_by_id = #{row.createById,jdbcType=INTEGER},
      sort = #{row.sort,jdbcType=INTEGER},
      book_online_available = #{row.bookOnlineAvailable,jdbcType=TINYINT},
      create_time = #{row.createTime,jdbcType=BIGINT},
      update_time = #{row.updateTime,jdbcType=BIGINT},
      show_on_calendar = #{row.showOnCalendar,jdbcType=TINYINT},
      show_calendar_staff_all = #{row.showCalendarStaffAll,jdbcType=TINYINT},
      access_code = #{row.accessCode,jdbcType=VARCHAR},
      token = #{row.token,jdbcType=VARCHAR},
      invite_code = #{row.inviteCode,jdbcType=VARCHAR},
      account_last_visited_at = #{row.accountLastVisitedAt,jdbcType=BIGINT},
      account_sort = #{row.accountSort,jdbcType=INTEGER},
      company_id = #{row.companyId,jdbcType=INTEGER},
      enterprise_id = #{row.enterpriseId,jdbcType=INTEGER},
      working_in_all_locations = #{row.workingInAllLocations,jdbcType=BIT},
      color_code = #{row.colorCode,jdbcType=VARCHAR},
      last_visit_business_id = #{row.lastVisitBusinessId,jdbcType=INTEGER},
      access_all_working_locations_staff = #{row.accessAllWorkingLocationsStaff,jdbcType=TINYINT},
      profile_email = #{row.profileEmail,jdbcType=VARCHAR},
      require_access_code = #{row.requireAccessCode,jdbcType=BIT},
      is_shown_on_all_calendar = #{row.isShownOnAllCalendar,jdbcType=BIT},
      source = #{row.source,jdbcType=INTEGER}
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.moego.svc.organization.entity.MoeStaff">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update moe_staff
    <set>
      <if test="businessId != null">
        business_id = #{businessId,jdbcType=INTEGER},
      </if>
      <if test="accountId != null">
        account_id = #{accountId,jdbcType=INTEGER},
      </if>
      <if test="roleId != null">
        role_id = #{roleId,jdbcType=INTEGER},
      </if>
      <if test="avatarPath != null">
        avatar_path = #{avatarPath,jdbcType=VARCHAR},
      </if>
      <if test="firstName != null">
        first_name = #{firstName,jdbcType=VARCHAR},
      </if>
      <if test="lastName != null">
        last_name = #{lastName,jdbcType=VARCHAR},
      </if>
      <if test="employeeCategory != null">
        employee_category = #{employeeCategory,jdbcType=TINYINT},
      </if>
      <if test="phoneNumber != null">
        phone_number = #{phoneNumber,jdbcType=VARCHAR},
      </if>
      <if test="hireDate != null">
        hire_date = #{hireDate,jdbcType=BIGINT},
      </if>
      <if test="fireDate != null">
        fire_date = #{fireDate,jdbcType=BIGINT},
      </if>
      <if test="allowLogin != null">
        allow_login = #{allowLogin,jdbcType=TINYINT},
      </if>
      <if test="groupLeaderId != null">
        group_leader_id = #{groupLeaderId,jdbcType=INTEGER},
      </if>
      <if test="note != null">
        note = #{note,jdbcType=VARCHAR},
      </if>
      <if test="inactive != null">
        inactive = #{inactive,jdbcType=TINYINT},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=TINYINT},
      </if>
      <if test="createById != null">
        create_by_id = #{createById,jdbcType=INTEGER},
      </if>
      <if test="sort != null">
        sort = #{sort,jdbcType=INTEGER},
      </if>
      <if test="bookOnlineAvailable != null">
        book_online_available = #{bookOnlineAvailable,jdbcType=TINYINT},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=BIGINT},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=BIGINT},
      </if>
      <if test="showOnCalendar != null">
        show_on_calendar = #{showOnCalendar,jdbcType=TINYINT},
      </if>
      <if test="showCalendarStaffAll != null">
        show_calendar_staff_all = #{showCalendarStaffAll,jdbcType=TINYINT},
      </if>
      <if test="accessCode != null">
        access_code = #{accessCode,jdbcType=VARCHAR},
      </if>
      <if test="token != null">
        token = #{token,jdbcType=VARCHAR},
      </if>
      <if test="inviteCode != null">
        invite_code = #{inviteCode,jdbcType=VARCHAR},
      </if>
      <if test="accountLastVisitedAt != null">
        account_last_visited_at = #{accountLastVisitedAt,jdbcType=BIGINT},
      </if>
      <if test="accountSort != null">
        account_sort = #{accountSort,jdbcType=INTEGER},
      </if>
      <if test="companyId != null">
        company_id = #{companyId,jdbcType=INTEGER},
      </if>
      <if test="enterpriseId != null">
        enterprise_id = #{enterpriseId,jdbcType=INTEGER},
      </if>
      <if test="workingInAllLocations != null">
        working_in_all_locations = #{workingInAllLocations,jdbcType=BIT},
      </if>
      <if test="colorCode != null">
        color_code = #{colorCode,jdbcType=VARCHAR},
      </if>
      <if test="lastVisitBusinessId != null">
        last_visit_business_id = #{lastVisitBusinessId,jdbcType=INTEGER},
      </if>
      <if test="accessAllWorkingLocationsStaff != null">
        access_all_working_locations_staff = #{accessAllWorkingLocationsStaff,jdbcType=TINYINT},
      </if>
      <if test="profileEmail != null">
        profile_email = #{profileEmail,jdbcType=VARCHAR},
      </if>
      <if test="requireAccessCode != null">
        require_access_code = #{requireAccessCode,jdbcType=BIT},
      </if>
      <if test="isShownOnAllCalendar != null">
        is_shown_on_all_calendar = #{isShownOnAllCalendar,jdbcType=BIT},
      </if>
      <if test="source != null">
        source = #{source,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.moego.svc.organization.entity.MoeStaff">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update moe_staff
    set business_id = #{businessId,jdbcType=INTEGER},
      account_id = #{accountId,jdbcType=INTEGER},
      role_id = #{roleId,jdbcType=INTEGER},
      avatar_path = #{avatarPath,jdbcType=VARCHAR},
      first_name = #{firstName,jdbcType=VARCHAR},
      last_name = #{lastName,jdbcType=VARCHAR},
      employee_category = #{employeeCategory,jdbcType=TINYINT},
      phone_number = #{phoneNumber,jdbcType=VARCHAR},
      hire_date = #{hireDate,jdbcType=BIGINT},
      fire_date = #{fireDate,jdbcType=BIGINT},
      allow_login = #{allowLogin,jdbcType=TINYINT},
      group_leader_id = #{groupLeaderId,jdbcType=INTEGER},
      note = #{note,jdbcType=VARCHAR},
      inactive = #{inactive,jdbcType=TINYINT},
      status = #{status,jdbcType=TINYINT},
      create_by_id = #{createById,jdbcType=INTEGER},
      sort = #{sort,jdbcType=INTEGER},
      book_online_available = #{bookOnlineAvailable,jdbcType=TINYINT},
      create_time = #{createTime,jdbcType=BIGINT},
      update_time = #{updateTime,jdbcType=BIGINT},
      show_on_calendar = #{showOnCalendar,jdbcType=TINYINT},
      show_calendar_staff_all = #{showCalendarStaffAll,jdbcType=TINYINT},
      access_code = #{accessCode,jdbcType=VARCHAR},
      token = #{token,jdbcType=VARCHAR},
      invite_code = #{inviteCode,jdbcType=VARCHAR},
      account_last_visited_at = #{accountLastVisitedAt,jdbcType=BIGINT},
      account_sort = #{accountSort,jdbcType=INTEGER},
      company_id = #{companyId,jdbcType=INTEGER},
      enterprise_id = #{enterpriseId,jdbcType=INTEGER},
      working_in_all_locations = #{workingInAllLocations,jdbcType=BIT},
      color_code = #{colorCode,jdbcType=VARCHAR},
      last_visit_business_id = #{lastVisitBusinessId,jdbcType=INTEGER},
      access_all_working_locations_staff = #{accessAllWorkingLocationsStaff,jdbcType=TINYINT},
      profile_email = #{profileEmail,jdbcType=VARCHAR},
      require_access_code = #{requireAccessCode,jdbcType=BIT},
      is_shown_on_all_calendar = #{isShownOnAllCalendar,jdbcType=BIT},
      source = #{source,jdbcType=INTEGER}
    where id = #{id,jdbcType=INTEGER}
  </update>
</mapper>