<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.moego.svc.organization.mapper.base.BaseMoeStaffWorkingLocationMapper">
  <resultMap id="BaseResultMap" type="com.moego.svc.organization.entity.MoeStaffWorkingLocation">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="staff_id" jdbcType="BIGINT" property="staffId" />
    <result column="company_id" jdbcType="BIGINT" property="companyId" />
    <result column="working_location_id" jdbcType="BIGINT" property="workingLocationId" />
    <result column="created_at" jdbcType="TIMESTAMP" property="createdAt" />
    <result column="created_by" jdbcType="BIGINT" property="createdBy" />
    <result column="deleted_at" jdbcType="TIMESTAMP" property="deletedAt" />
    <result column="deleted_by" jdbcType="BIGINT" property="deletedBy" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    id, staff_id, company_id, working_location_id, created_at, created_by, deleted_at, 
    deleted_by
  </sql>
  <select id="selectByExample" parameterType="com.moego.svc.organization.entity.MoeStaffWorkingLocationExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from moe_staff_working_location
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select 
    <include refid="Base_Column_List" />
    from moe_staff_working_location
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from moe_staff_working_location
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.moego.svc.organization.entity.MoeStaffWorkingLocationExample">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from moe_staff_working_location
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.moego.svc.organization.entity.MoeStaffWorkingLocation">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into moe_staff_working_location (staff_id, company_id, working_location_id, 
      created_at, created_by, deleted_at, 
      deleted_by)
    values (#{staffId,jdbcType=BIGINT}, #{companyId,jdbcType=BIGINT}, #{workingLocationId,jdbcType=BIGINT}, 
      #{createdAt,jdbcType=TIMESTAMP}, #{createdBy,jdbcType=BIGINT}, #{deletedAt,jdbcType=TIMESTAMP}, 
      #{deletedBy,jdbcType=BIGINT})
  </insert>
  <insert id="insertSelective" parameterType="com.moego.svc.organization.entity.MoeStaffWorkingLocation">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into moe_staff_working_location
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="staffId != null">
        staff_id,
      </if>
      <if test="companyId != null">
        company_id,
      </if>
      <if test="workingLocationId != null">
        working_location_id,
      </if>
      <if test="createdAt != null">
        created_at,
      </if>
      <if test="createdBy != null">
        created_by,
      </if>
      <if test="deletedAt != null">
        deleted_at,
      </if>
      <if test="deletedBy != null">
        deleted_by,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="staffId != null">
        #{staffId,jdbcType=BIGINT},
      </if>
      <if test="companyId != null">
        #{companyId,jdbcType=BIGINT},
      </if>
      <if test="workingLocationId != null">
        #{workingLocationId,jdbcType=BIGINT},
      </if>
      <if test="createdAt != null">
        #{createdAt,jdbcType=TIMESTAMP},
      </if>
      <if test="createdBy != null">
        #{createdBy,jdbcType=BIGINT},
      </if>
      <if test="deletedAt != null">
        #{deletedAt,jdbcType=TIMESTAMP},
      </if>
      <if test="deletedBy != null">
        #{deletedBy,jdbcType=BIGINT},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.moego.svc.organization.entity.MoeStaffWorkingLocationExample" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select count(*) from moe_staff_working_location
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update moe_staff_working_location
    <set>
      <if test="row.id != null">
        id = #{row.id,jdbcType=BIGINT},
      </if>
      <if test="row.staffId != null">
        staff_id = #{row.staffId,jdbcType=BIGINT},
      </if>
      <if test="row.companyId != null">
        company_id = #{row.companyId,jdbcType=BIGINT},
      </if>
      <if test="row.workingLocationId != null">
        working_location_id = #{row.workingLocationId,jdbcType=BIGINT},
      </if>
      <if test="row.createdAt != null">
        created_at = #{row.createdAt,jdbcType=TIMESTAMP},
      </if>
      <if test="row.createdBy != null">
        created_by = #{row.createdBy,jdbcType=BIGINT},
      </if>
      <if test="row.deletedAt != null">
        deleted_at = #{row.deletedAt,jdbcType=TIMESTAMP},
      </if>
      <if test="row.deletedBy != null">
        deleted_by = #{row.deletedBy,jdbcType=BIGINT},
      </if>
    </set>
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update moe_staff_working_location
    set id = #{row.id,jdbcType=BIGINT},
      staff_id = #{row.staffId,jdbcType=BIGINT},
      company_id = #{row.companyId,jdbcType=BIGINT},
      working_location_id = #{row.workingLocationId,jdbcType=BIGINT},
      created_at = #{row.createdAt,jdbcType=TIMESTAMP},
      created_by = #{row.createdBy,jdbcType=BIGINT},
      deleted_at = #{row.deletedAt,jdbcType=TIMESTAMP},
      deleted_by = #{row.deletedBy,jdbcType=BIGINT}
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.moego.svc.organization.entity.MoeStaffWorkingLocation">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update moe_staff_working_location
    <set>
      <if test="staffId != null">
        staff_id = #{staffId,jdbcType=BIGINT},
      </if>
      <if test="companyId != null">
        company_id = #{companyId,jdbcType=BIGINT},
      </if>
      <if test="workingLocationId != null">
        working_location_id = #{workingLocationId,jdbcType=BIGINT},
      </if>
      <if test="createdAt != null">
        created_at = #{createdAt,jdbcType=TIMESTAMP},
      </if>
      <if test="createdBy != null">
        created_by = #{createdBy,jdbcType=BIGINT},
      </if>
      <if test="deletedAt != null">
        deleted_at = #{deletedAt,jdbcType=TIMESTAMP},
      </if>
      <if test="deletedBy != null">
        deleted_by = #{deletedBy,jdbcType=BIGINT},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.moego.svc.organization.entity.MoeStaffWorkingLocation">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update moe_staff_working_location
    set staff_id = #{staffId,jdbcType=BIGINT},
      company_id = #{companyId,jdbcType=BIGINT},
      working_location_id = #{workingLocationId,jdbcType=BIGINT},
      created_at = #{createdAt,jdbcType=TIMESTAMP},
      created_by = #{createdBy,jdbcType=BIGINT},
      deleted_at = #{deletedAt,jdbcType=TIMESTAMP},
      deleted_by = #{deletedBy,jdbcType=BIGINT}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>