package com.moego.svc.organization.entity;

import java.time.LocalDateTime;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder(toBuilder = true)
@AllArgsConstructor(staticName = "")
@NoArgsConstructor(staticName = "")
public class StaffAvailabilityDayHour {
    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column staff_availability_day_hour.id
     *
     * @mbg.generated
     */
    private Long id;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column staff_availability_day_hour.day_type
     *
     * @mbg.generated
     */
    private Integer dayType;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column staff_availability_day_hour.day_id
     *
     * @mbg.generated
     */
    private Long dayId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column staff_availability_day_hour.start_time
     *
     * @mbg.generated
     */
    private Integer startTime;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column staff_availability_day_hour.end_time
     *
     * @mbg.generated
     */
    private Integer endTime;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column staff_availability_day_hour.capacity
     *
     * @mbg.generated
     */
    private Integer capacity;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column staff_availability_day_hour.created_at
     *
     * @mbg.generated
     */
    private LocalDateTime createdAt;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column staff_availability_day_hour.updated_at
     *
     * @mbg.generated
     */
    private LocalDateTime updatedAt;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column staff_availability_day_hour.limit_ids
     *
     * @mbg.generated
     */
    private List<Long> limitIds;
}
