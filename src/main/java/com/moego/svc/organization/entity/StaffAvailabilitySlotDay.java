package com.moego.svc.organization.entity;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder(toBuilder = true)
@AllArgsConstructor(staticName = "")
@NoArgsConstructor(staticName = "")
public class StaffAvailabilitySlotDay {
    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column staff_availability_slot_day.id
     *
     * @mbg.generated
     */
    private Long id;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column staff_availability_slot_day.company_id
     *
     * @mbg.generated
     */
    private Long companyId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column staff_availability_slot_day.business_id
     *
     * @mbg.generated
     */
    private Long businessId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column staff_availability_slot_day.staff_id
     *
     * @mbg.generated
     */
    private Long staffId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column staff_availability_slot_day.override_date
     *
     * @mbg.generated
     */
    private LocalDate overrideDate;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column staff_availability_slot_day.is_available
     *
     * @mbg.generated
     */
    private Boolean isAvailable;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column staff_availability_slot_day.schedule_type
     *
     * @mbg.generated
     */
    private Integer scheduleType;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column staff_availability_slot_day.day_of_week
     *
     * @mbg.generated
     */
    private Integer dayOfWeek;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column staff_availability_slot_day.start_time
     *
     * @mbg.generated
     */
    private Integer startTime;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column staff_availability_slot_day.end_time
     *
     * @mbg.generated
     */
    private Integer endTime;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column staff_availability_slot_day.capacity
     *
     * @mbg.generated
     */
    private Integer capacity;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column staff_availability_slot_day.created_at
     *
     * @mbg.generated
     */
    private LocalDateTime createdAt;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column staff_availability_slot_day.updated_at
     *
     * @mbg.generated
     */
    private LocalDateTime updatedAt;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column staff_availability_slot_day.limit_ids
     *
     * @mbg.generated
     */
    private List<Long> limitIds;
}
