package com.moego.svc.organization.entity;

import java.util.ArrayList;
import java.util.List;

public class MoeStaffNotificationExample {
    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table moe_staff_notification
     *
     * @mbg.generated
     */
    protected String orderByClause;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table moe_staff_notification
     *
     * @mbg.generated
     */
    protected boolean distinct;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table moe_staff_notification
     *
     * @mbg.generated
     */
    protected List<Criteria> oredCriteria;

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_staff_notification
     *
     * @mbg.generated
     */
    public MoeStaffNotificationExample() {
        oredCriteria = new ArrayList<>();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_staff_notification
     *
     * @mbg.generated
     */
    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_staff_notification
     *
     * @mbg.generated
     */
    public String getOrderByClause() {
        return orderByClause;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_staff_notification
     *
     * @mbg.generated
     */
    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_staff_notification
     *
     * @mbg.generated
     */
    public boolean isDistinct() {
        return distinct;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_staff_notification
     *
     * @mbg.generated
     */
    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_staff_notification
     *
     * @mbg.generated
     */
    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_staff_notification
     *
     * @mbg.generated
     */
    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_staff_notification
     *
     * @mbg.generated
     */
    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_staff_notification
     *
     * @mbg.generated
     */
    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_staff_notification
     *
     * @mbg.generated
     */
    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table moe_staff_notification
     *
     * @mbg.generated
     */
    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Integer value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Integer value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Integer value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Integer value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Integer value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Integer> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Integer> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Integer value1, Integer value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Integer value1, Integer value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andStaffIdIsNull() {
            addCriterion("staff_id is null");
            return (Criteria) this;
        }

        public Criteria andStaffIdIsNotNull() {
            addCriterion("staff_id is not null");
            return (Criteria) this;
        }

        public Criteria andStaffIdEqualTo(Integer value) {
            addCriterion("staff_id =", value, "staffId");
            return (Criteria) this;
        }

        public Criteria andStaffIdNotEqualTo(Integer value) {
            addCriterion("staff_id <>", value, "staffId");
            return (Criteria) this;
        }

        public Criteria andStaffIdGreaterThan(Integer value) {
            addCriterion("staff_id >", value, "staffId");
            return (Criteria) this;
        }

        public Criteria andStaffIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("staff_id >=", value, "staffId");
            return (Criteria) this;
        }

        public Criteria andStaffIdLessThan(Integer value) {
            addCriterion("staff_id <", value, "staffId");
            return (Criteria) this;
        }

        public Criteria andStaffIdLessThanOrEqualTo(Integer value) {
            addCriterion("staff_id <=", value, "staffId");
            return (Criteria) this;
        }

        public Criteria andStaffIdIn(List<Integer> values) {
            addCriterion("staff_id in", values, "staffId");
            return (Criteria) this;
        }

        public Criteria andStaffIdNotIn(List<Integer> values) {
            addCriterion("staff_id not in", values, "staffId");
            return (Criteria) this;
        }

        public Criteria andStaffIdBetween(Integer value1, Integer value2) {
            addCriterion("staff_id between", value1, value2, "staffId");
            return (Criteria) this;
        }

        public Criteria andStaffIdNotBetween(Integer value1, Integer value2) {
            addCriterion("staff_id not between", value1, value2, "staffId");
            return (Criteria) this;
        }

        public Criteria andBookingCreatedIsNull() {
            addCriterion("booking_created is null");
            return (Criteria) this;
        }

        public Criteria andBookingCreatedIsNotNull() {
            addCriterion("booking_created is not null");
            return (Criteria) this;
        }

        public Criteria andBookingCreatedEqualTo(Byte value) {
            addCriterion("booking_created =", value, "bookingCreated");
            return (Criteria) this;
        }

        public Criteria andBookingCreatedNotEqualTo(Byte value) {
            addCriterion("booking_created <>", value, "bookingCreated");
            return (Criteria) this;
        }

        public Criteria andBookingCreatedGreaterThan(Byte value) {
            addCriterion("booking_created >", value, "bookingCreated");
            return (Criteria) this;
        }

        public Criteria andBookingCreatedGreaterThanOrEqualTo(Byte value) {
            addCriterion("booking_created >=", value, "bookingCreated");
            return (Criteria) this;
        }

        public Criteria andBookingCreatedLessThan(Byte value) {
            addCriterion("booking_created <", value, "bookingCreated");
            return (Criteria) this;
        }

        public Criteria andBookingCreatedLessThanOrEqualTo(Byte value) {
            addCriterion("booking_created <=", value, "bookingCreated");
            return (Criteria) this;
        }

        public Criteria andBookingCreatedIn(List<Byte> values) {
            addCriterion("booking_created in", values, "bookingCreated");
            return (Criteria) this;
        }

        public Criteria andBookingCreatedNotIn(List<Byte> values) {
            addCriterion("booking_created not in", values, "bookingCreated");
            return (Criteria) this;
        }

        public Criteria andBookingCreatedBetween(Byte value1, Byte value2) {
            addCriterion("booking_created between", value1, value2, "bookingCreated");
            return (Criteria) this;
        }

        public Criteria andBookingCreatedNotBetween(Byte value1, Byte value2) {
            addCriterion("booking_created not between", value1, value2, "bookingCreated");
            return (Criteria) this;
        }

        public Criteria andBookingCancelledIsNull() {
            addCriterion("booking_cancelled is null");
            return (Criteria) this;
        }

        public Criteria andBookingCancelledIsNotNull() {
            addCriterion("booking_cancelled is not null");
            return (Criteria) this;
        }

        public Criteria andBookingCancelledEqualTo(Byte value) {
            addCriterion("booking_cancelled =", value, "bookingCancelled");
            return (Criteria) this;
        }

        public Criteria andBookingCancelledNotEqualTo(Byte value) {
            addCriterion("booking_cancelled <>", value, "bookingCancelled");
            return (Criteria) this;
        }

        public Criteria andBookingCancelledGreaterThan(Byte value) {
            addCriterion("booking_cancelled >", value, "bookingCancelled");
            return (Criteria) this;
        }

        public Criteria andBookingCancelledGreaterThanOrEqualTo(Byte value) {
            addCriterion("booking_cancelled >=", value, "bookingCancelled");
            return (Criteria) this;
        }

        public Criteria andBookingCancelledLessThan(Byte value) {
            addCriterion("booking_cancelled <", value, "bookingCancelled");
            return (Criteria) this;
        }

        public Criteria andBookingCancelledLessThanOrEqualTo(Byte value) {
            addCriterion("booking_cancelled <=", value, "bookingCancelled");
            return (Criteria) this;
        }

        public Criteria andBookingCancelledIn(List<Byte> values) {
            addCriterion("booking_cancelled in", values, "bookingCancelled");
            return (Criteria) this;
        }

        public Criteria andBookingCancelledNotIn(List<Byte> values) {
            addCriterion("booking_cancelled not in", values, "bookingCancelled");
            return (Criteria) this;
        }

        public Criteria andBookingCancelledBetween(Byte value1, Byte value2) {
            addCriterion("booking_cancelled between", value1, value2, "bookingCancelled");
            return (Criteria) this;
        }

        public Criteria andBookingCancelledNotBetween(Byte value1, Byte value2) {
            addCriterion("booking_cancelled not between", value1, value2, "bookingCancelled");
            return (Criteria) this;
        }

        public Criteria andBookingRescheduledIsNull() {
            addCriterion("booking_rescheduled is null");
            return (Criteria) this;
        }

        public Criteria andBookingRescheduledIsNotNull() {
            addCriterion("booking_rescheduled is not null");
            return (Criteria) this;
        }

        public Criteria andBookingRescheduledEqualTo(Byte value) {
            addCriterion("booking_rescheduled =", value, "bookingRescheduled");
            return (Criteria) this;
        }

        public Criteria andBookingRescheduledNotEqualTo(Byte value) {
            addCriterion("booking_rescheduled <>", value, "bookingRescheduled");
            return (Criteria) this;
        }

        public Criteria andBookingRescheduledGreaterThan(Byte value) {
            addCriterion("booking_rescheduled >", value, "bookingRescheduled");
            return (Criteria) this;
        }

        public Criteria andBookingRescheduledGreaterThanOrEqualTo(Byte value) {
            addCriterion("booking_rescheduled >=", value, "bookingRescheduled");
            return (Criteria) this;
        }

        public Criteria andBookingRescheduledLessThan(Byte value) {
            addCriterion("booking_rescheduled <", value, "bookingRescheduled");
            return (Criteria) this;
        }

        public Criteria andBookingRescheduledLessThanOrEqualTo(Byte value) {
            addCriterion("booking_rescheduled <=", value, "bookingRescheduled");
            return (Criteria) this;
        }

        public Criteria andBookingRescheduledIn(List<Byte> values) {
            addCriterion("booking_rescheduled in", values, "bookingRescheduled");
            return (Criteria) this;
        }

        public Criteria andBookingRescheduledNotIn(List<Byte> values) {
            addCriterion("booking_rescheduled not in", values, "bookingRescheduled");
            return (Criteria) this;
        }

        public Criteria andBookingRescheduledBetween(Byte value1, Byte value2) {
            addCriterion("booking_rescheduled between", value1, value2, "bookingRescheduled");
            return (Criteria) this;
        }

        public Criteria andBookingRescheduledNotBetween(Byte value1, Byte value2) {
            addCriterion("booking_rescheduled not between", value1, value2, "bookingRescheduled");
            return (Criteria) this;
        }

        public Criteria andNewBookingIsNull() {
            addCriterion("new_booking is null");
            return (Criteria) this;
        }

        public Criteria andNewBookingIsNotNull() {
            addCriterion("new_booking is not null");
            return (Criteria) this;
        }

        public Criteria andNewBookingEqualTo(Byte value) {
            addCriterion("new_booking =", value, "newBooking");
            return (Criteria) this;
        }

        public Criteria andNewBookingNotEqualTo(Byte value) {
            addCriterion("new_booking <>", value, "newBooking");
            return (Criteria) this;
        }

        public Criteria andNewBookingGreaterThan(Byte value) {
            addCriterion("new_booking >", value, "newBooking");
            return (Criteria) this;
        }

        public Criteria andNewBookingGreaterThanOrEqualTo(Byte value) {
            addCriterion("new_booking >=", value, "newBooking");
            return (Criteria) this;
        }

        public Criteria andNewBookingLessThan(Byte value) {
            addCriterion("new_booking <", value, "newBooking");
            return (Criteria) this;
        }

        public Criteria andNewBookingLessThanOrEqualTo(Byte value) {
            addCriterion("new_booking <=", value, "newBooking");
            return (Criteria) this;
        }

        public Criteria andNewBookingIn(List<Byte> values) {
            addCriterion("new_booking in", values, "newBooking");
            return (Criteria) this;
        }

        public Criteria andNewBookingNotIn(List<Byte> values) {
            addCriterion("new_booking not in", values, "newBooking");
            return (Criteria) this;
        }

        public Criteria andNewBookingBetween(Byte value1, Byte value2) {
            addCriterion("new_booking between", value1, value2, "newBooking");
            return (Criteria) this;
        }

        public Criteria andNewBookingNotBetween(Byte value1, Byte value2) {
            addCriterion("new_booking not between", value1, value2, "newBooking");
            return (Criteria) this;
        }

        public Criteria andNewIntakeFormIsNull() {
            addCriterion("new_intake_form is null");
            return (Criteria) this;
        }

        public Criteria andNewIntakeFormIsNotNull() {
            addCriterion("new_intake_form is not null");
            return (Criteria) this;
        }

        public Criteria andNewIntakeFormEqualTo(Byte value) {
            addCriterion("new_intake_form =", value, "newIntakeForm");
            return (Criteria) this;
        }

        public Criteria andNewIntakeFormNotEqualTo(Byte value) {
            addCriterion("new_intake_form <>", value, "newIntakeForm");
            return (Criteria) this;
        }

        public Criteria andNewIntakeFormGreaterThan(Byte value) {
            addCriterion("new_intake_form >", value, "newIntakeForm");
            return (Criteria) this;
        }

        public Criteria andNewIntakeFormGreaterThanOrEqualTo(Byte value) {
            addCriterion("new_intake_form >=", value, "newIntakeForm");
            return (Criteria) this;
        }

        public Criteria andNewIntakeFormLessThan(Byte value) {
            addCriterion("new_intake_form <", value, "newIntakeForm");
            return (Criteria) this;
        }

        public Criteria andNewIntakeFormLessThanOrEqualTo(Byte value) {
            addCriterion("new_intake_form <=", value, "newIntakeForm");
            return (Criteria) this;
        }

        public Criteria andNewIntakeFormIn(List<Byte> values) {
            addCriterion("new_intake_form in", values, "newIntakeForm");
            return (Criteria) this;
        }

        public Criteria andNewIntakeFormNotIn(List<Byte> values) {
            addCriterion("new_intake_form not in", values, "newIntakeForm");
            return (Criteria) this;
        }

        public Criteria andNewIntakeFormBetween(Byte value1, Byte value2) {
            addCriterion("new_intake_form between", value1, value2, "newIntakeForm");
            return (Criteria) this;
        }

        public Criteria andNewIntakeFormNotBetween(Byte value1, Byte value2) {
            addCriterion("new_intake_form not between", value1, value2, "newIntakeForm");
            return (Criteria) this;
        }

        public Criteria andAgreementSignedIsNull() {
            addCriterion("agreement_signed is null");
            return (Criteria) this;
        }

        public Criteria andAgreementSignedIsNotNull() {
            addCriterion("agreement_signed is not null");
            return (Criteria) this;
        }

        public Criteria andAgreementSignedEqualTo(Byte value) {
            addCriterion("agreement_signed =", value, "agreementSigned");
            return (Criteria) this;
        }

        public Criteria andAgreementSignedNotEqualTo(Byte value) {
            addCriterion("agreement_signed <>", value, "agreementSigned");
            return (Criteria) this;
        }

        public Criteria andAgreementSignedGreaterThan(Byte value) {
            addCriterion("agreement_signed >", value, "agreementSigned");
            return (Criteria) this;
        }

        public Criteria andAgreementSignedGreaterThanOrEqualTo(Byte value) {
            addCriterion("agreement_signed >=", value, "agreementSigned");
            return (Criteria) this;
        }

        public Criteria andAgreementSignedLessThan(Byte value) {
            addCriterion("agreement_signed <", value, "agreementSigned");
            return (Criteria) this;
        }

        public Criteria andAgreementSignedLessThanOrEqualTo(Byte value) {
            addCriterion("agreement_signed <=", value, "agreementSigned");
            return (Criteria) this;
        }

        public Criteria andAgreementSignedIn(List<Byte> values) {
            addCriterion("agreement_signed in", values, "agreementSigned");
            return (Criteria) this;
        }

        public Criteria andAgreementSignedNotIn(List<Byte> values) {
            addCriterion("agreement_signed not in", values, "agreementSigned");
            return (Criteria) this;
        }

        public Criteria andAgreementSignedBetween(Byte value1, Byte value2) {
            addCriterion("agreement_signed between", value1, value2, "agreementSigned");
            return (Criteria) this;
        }

        public Criteria andAgreementSignedNotBetween(Byte value1, Byte value2) {
            addCriterion("agreement_signed not between", value1, value2, "agreementSigned");
            return (Criteria) this;
        }

        public Criteria andInvoicePaidIsNull() {
            addCriterion("invoice_paid is null");
            return (Criteria) this;
        }

        public Criteria andInvoicePaidIsNotNull() {
            addCriterion("invoice_paid is not null");
            return (Criteria) this;
        }

        public Criteria andInvoicePaidEqualTo(Byte value) {
            addCriterion("invoice_paid =", value, "invoicePaid");
            return (Criteria) this;
        }

        public Criteria andInvoicePaidNotEqualTo(Byte value) {
            addCriterion("invoice_paid <>", value, "invoicePaid");
            return (Criteria) this;
        }

        public Criteria andInvoicePaidGreaterThan(Byte value) {
            addCriterion("invoice_paid >", value, "invoicePaid");
            return (Criteria) this;
        }

        public Criteria andInvoicePaidGreaterThanOrEqualTo(Byte value) {
            addCriterion("invoice_paid >=", value, "invoicePaid");
            return (Criteria) this;
        }

        public Criteria andInvoicePaidLessThan(Byte value) {
            addCriterion("invoice_paid <", value, "invoicePaid");
            return (Criteria) this;
        }

        public Criteria andInvoicePaidLessThanOrEqualTo(Byte value) {
            addCriterion("invoice_paid <=", value, "invoicePaid");
            return (Criteria) this;
        }

        public Criteria andInvoicePaidIn(List<Byte> values) {
            addCriterion("invoice_paid in", values, "invoicePaid");
            return (Criteria) this;
        }

        public Criteria andInvoicePaidNotIn(List<Byte> values) {
            addCriterion("invoice_paid not in", values, "invoicePaid");
            return (Criteria) this;
        }

        public Criteria andInvoicePaidBetween(Byte value1, Byte value2) {
            addCriterion("invoice_paid between", value1, value2, "invoicePaid");
            return (Criteria) this;
        }

        public Criteria andInvoicePaidNotBetween(Byte value1, Byte value2) {
            addCriterion("invoice_paid not between", value1, value2, "invoicePaid");
            return (Criteria) this;
        }

        public Criteria andReviewSubmittedIsNull() {
            addCriterion("review_submitted is null");
            return (Criteria) this;
        }

        public Criteria andReviewSubmittedIsNotNull() {
            addCriterion("review_submitted is not null");
            return (Criteria) this;
        }

        public Criteria andReviewSubmittedEqualTo(Byte value) {
            addCriterion("review_submitted =", value, "reviewSubmitted");
            return (Criteria) this;
        }

        public Criteria andReviewSubmittedNotEqualTo(Byte value) {
            addCriterion("review_submitted <>", value, "reviewSubmitted");
            return (Criteria) this;
        }

        public Criteria andReviewSubmittedGreaterThan(Byte value) {
            addCriterion("review_submitted >", value, "reviewSubmitted");
            return (Criteria) this;
        }

        public Criteria andReviewSubmittedGreaterThanOrEqualTo(Byte value) {
            addCriterion("review_submitted >=", value, "reviewSubmitted");
            return (Criteria) this;
        }

        public Criteria andReviewSubmittedLessThan(Byte value) {
            addCriterion("review_submitted <", value, "reviewSubmitted");
            return (Criteria) this;
        }

        public Criteria andReviewSubmittedLessThanOrEqualTo(Byte value) {
            addCriterion("review_submitted <=", value, "reviewSubmitted");
            return (Criteria) this;
        }

        public Criteria andReviewSubmittedIn(List<Byte> values) {
            addCriterion("review_submitted in", values, "reviewSubmitted");
            return (Criteria) this;
        }

        public Criteria andReviewSubmittedNotIn(List<Byte> values) {
            addCriterion("review_submitted not in", values, "reviewSubmitted");
            return (Criteria) this;
        }

        public Criteria andReviewSubmittedBetween(Byte value1, Byte value2) {
            addCriterion("review_submitted between", value1, value2, "reviewSubmitted");
            return (Criteria) this;
        }

        public Criteria andReviewSubmittedNotBetween(Byte value1, Byte value2) {
            addCriterion("review_submitted not between", value1, value2, "reviewSubmitted");
            return (Criteria) this;
        }

        public Criteria andPushCalendarSwitchIsNull() {
            addCriterion("push_calendar_switch is null");
            return (Criteria) this;
        }

        public Criteria andPushCalendarSwitchIsNotNull() {
            addCriterion("push_calendar_switch is not null");
            return (Criteria) this;
        }

        public Criteria andPushCalendarSwitchEqualTo(Byte value) {
            addCriterion("push_calendar_switch =", value, "pushCalendarSwitch");
            return (Criteria) this;
        }

        public Criteria andPushCalendarSwitchNotEqualTo(Byte value) {
            addCriterion("push_calendar_switch <>", value, "pushCalendarSwitch");
            return (Criteria) this;
        }

        public Criteria andPushCalendarSwitchGreaterThan(Byte value) {
            addCriterion("push_calendar_switch >", value, "pushCalendarSwitch");
            return (Criteria) this;
        }

        public Criteria andPushCalendarSwitchGreaterThanOrEqualTo(Byte value) {
            addCriterion("push_calendar_switch >=", value, "pushCalendarSwitch");
            return (Criteria) this;
        }

        public Criteria andPushCalendarSwitchLessThan(Byte value) {
            addCriterion("push_calendar_switch <", value, "pushCalendarSwitch");
            return (Criteria) this;
        }

        public Criteria andPushCalendarSwitchLessThanOrEqualTo(Byte value) {
            addCriterion("push_calendar_switch <=", value, "pushCalendarSwitch");
            return (Criteria) this;
        }

        public Criteria andPushCalendarSwitchIn(List<Byte> values) {
            addCriterion("push_calendar_switch in", values, "pushCalendarSwitch");
            return (Criteria) this;
        }

        public Criteria andPushCalendarSwitchNotIn(List<Byte> values) {
            addCriterion("push_calendar_switch not in", values, "pushCalendarSwitch");
            return (Criteria) this;
        }

        public Criteria andPushCalendarSwitchBetween(Byte value1, Byte value2) {
            addCriterion("push_calendar_switch between", value1, value2, "pushCalendarSwitch");
            return (Criteria) this;
        }

        public Criteria andPushCalendarSwitchNotBetween(Byte value1, Byte value2) {
            addCriterion("push_calendar_switch not between", value1, value2, "pushCalendarSwitch");
            return (Criteria) this;
        }

        public Criteria andBeforeMinsIsNull() {
            addCriterion("before_mins is null");
            return (Criteria) this;
        }

        public Criteria andBeforeMinsIsNotNull() {
            addCriterion("before_mins is not null");
            return (Criteria) this;
        }

        public Criteria andBeforeMinsEqualTo(Integer value) {
            addCriterion("before_mins =", value, "beforeMins");
            return (Criteria) this;
        }

        public Criteria andBeforeMinsNotEqualTo(Integer value) {
            addCriterion("before_mins <>", value, "beforeMins");
            return (Criteria) this;
        }

        public Criteria andBeforeMinsGreaterThan(Integer value) {
            addCriterion("before_mins >", value, "beforeMins");
            return (Criteria) this;
        }

        public Criteria andBeforeMinsGreaterThanOrEqualTo(Integer value) {
            addCriterion("before_mins >=", value, "beforeMins");
            return (Criteria) this;
        }

        public Criteria andBeforeMinsLessThan(Integer value) {
            addCriterion("before_mins <", value, "beforeMins");
            return (Criteria) this;
        }

        public Criteria andBeforeMinsLessThanOrEqualTo(Integer value) {
            addCriterion("before_mins <=", value, "beforeMins");
            return (Criteria) this;
        }

        public Criteria andBeforeMinsIn(List<Integer> values) {
            addCriterion("before_mins in", values, "beforeMins");
            return (Criteria) this;
        }

        public Criteria andBeforeMinsNotIn(List<Integer> values) {
            addCriterion("before_mins not in", values, "beforeMins");
            return (Criteria) this;
        }

        public Criteria andBeforeMinsBetween(Integer value1, Integer value2) {
            addCriterion("before_mins between", value1, value2, "beforeMins");
            return (Criteria) this;
        }

        public Criteria andBeforeMinsNotBetween(Integer value1, Integer value2) {
            addCriterion("before_mins not between", value1, value2, "beforeMins");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNull() {
            addCriterion("create_time is null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNotNull() {
            addCriterion("create_time is not null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeEqualTo(Long value) {
            addCriterion("create_time =", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotEqualTo(Long value) {
            addCriterion("create_time <>", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThan(Long value) {
            addCriterion("create_time >", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThanOrEqualTo(Long value) {
            addCriterion("create_time >=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThan(Long value) {
            addCriterion("create_time <", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThanOrEqualTo(Long value) {
            addCriterion("create_time <=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIn(List<Long> values) {
            addCriterion("create_time in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotIn(List<Long> values) {
            addCriterion("create_time not in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeBetween(Long value1, Long value2) {
            addCriterion("create_time between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotBetween(Long value1, Long value2) {
            addCriterion("create_time not between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNull() {
            addCriterion("update_time is null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNotNull() {
            addCriterion("update_time is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeEqualTo(Long value) {
            addCriterion("update_time =", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotEqualTo(Long value) {
            addCriterion("update_time <>", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThan(Long value) {
            addCriterion("update_time >", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThanOrEqualTo(Long value) {
            addCriterion("update_time >=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThan(Long value) {
            addCriterion("update_time <", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThanOrEqualTo(Long value) {
            addCriterion("update_time <=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIn(List<Long> values) {
            addCriterion("update_time in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotIn(List<Long> values) {
            addCriterion("update_time not in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeBetween(Long value1, Long value2) {
            addCriterion("update_time between", value1, value2, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotBetween(Long value1, Long value2) {
            addCriterion("update_time not between", value1, value2, "updateTime");
            return (Criteria) this;
        }

        public Criteria andNewAbandonedBookingsIsNull() {
            addCriterion("new_abandoned_bookings is null");
            return (Criteria) this;
        }

        public Criteria andNewAbandonedBookingsIsNotNull() {
            addCriterion("new_abandoned_bookings is not null");
            return (Criteria) this;
        }

        public Criteria andNewAbandonedBookingsEqualTo(Byte value) {
            addCriterion("new_abandoned_bookings =", value, "newAbandonedBookings");
            return (Criteria) this;
        }

        public Criteria andNewAbandonedBookingsNotEqualTo(Byte value) {
            addCriterion("new_abandoned_bookings <>", value, "newAbandonedBookings");
            return (Criteria) this;
        }

        public Criteria andNewAbandonedBookingsGreaterThan(Byte value) {
            addCriterion("new_abandoned_bookings >", value, "newAbandonedBookings");
            return (Criteria) this;
        }

        public Criteria andNewAbandonedBookingsGreaterThanOrEqualTo(Byte value) {
            addCriterion("new_abandoned_bookings >=", value, "newAbandonedBookings");
            return (Criteria) this;
        }

        public Criteria andNewAbandonedBookingsLessThan(Byte value) {
            addCriterion("new_abandoned_bookings <", value, "newAbandonedBookings");
            return (Criteria) this;
        }

        public Criteria andNewAbandonedBookingsLessThanOrEqualTo(Byte value) {
            addCriterion("new_abandoned_bookings <=", value, "newAbandonedBookings");
            return (Criteria) this;
        }

        public Criteria andNewAbandonedBookingsIn(List<Byte> values) {
            addCriterion("new_abandoned_bookings in", values, "newAbandonedBookings");
            return (Criteria) this;
        }

        public Criteria andNewAbandonedBookingsNotIn(List<Byte> values) {
            addCriterion("new_abandoned_bookings not in", values, "newAbandonedBookings");
            return (Criteria) this;
        }

        public Criteria andNewAbandonedBookingsBetween(Byte value1, Byte value2) {
            addCriterion("new_abandoned_bookings between", value1, value2, "newAbandonedBookings");
            return (Criteria) this;
        }

        public Criteria andNewAbandonedBookingsNotBetween(Byte value1, Byte value2) {
            addCriterion("new_abandoned_bookings not between", value1, value2, "newAbandonedBookings");
            return (Criteria) this;
        }

        public Criteria andCompanyIdIsNull() {
            addCriterion("company_id is null");
            return (Criteria) this;
        }

        public Criteria andCompanyIdIsNotNull() {
            addCriterion("company_id is not null");
            return (Criteria) this;
        }

        public Criteria andCompanyIdEqualTo(Long value) {
            addCriterion("company_id =", value, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdNotEqualTo(Long value) {
            addCriterion("company_id <>", value, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdGreaterThan(Long value) {
            addCriterion("company_id >", value, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdGreaterThanOrEqualTo(Long value) {
            addCriterion("company_id >=", value, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdLessThan(Long value) {
            addCriterion("company_id <", value, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdLessThanOrEqualTo(Long value) {
            addCriterion("company_id <=", value, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdIn(List<Long> values) {
            addCriterion("company_id in", values, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdNotIn(List<Long> values) {
            addCriterion("company_id not in", values, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdBetween(Long value1, Long value2) {
            addCriterion("company_id between", value1, value2, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdNotBetween(Long value1, Long value2) {
            addCriterion("company_id not between", value1, value2, "companyId");
            return (Criteria) this;
        }

        public Criteria andBusinessIdIsNull() {
            addCriterion("business_id is null");
            return (Criteria) this;
        }

        public Criteria andBusinessIdIsNotNull() {
            addCriterion("business_id is not null");
            return (Criteria) this;
        }

        public Criteria andBusinessIdEqualTo(Long value) {
            addCriterion("business_id =", value, "businessId");
            return (Criteria) this;
        }

        public Criteria andBusinessIdNotEqualTo(Long value) {
            addCriterion("business_id <>", value, "businessId");
            return (Criteria) this;
        }

        public Criteria andBusinessIdGreaterThan(Long value) {
            addCriterion("business_id >", value, "businessId");
            return (Criteria) this;
        }

        public Criteria andBusinessIdGreaterThanOrEqualTo(Long value) {
            addCriterion("business_id >=", value, "businessId");
            return (Criteria) this;
        }

        public Criteria andBusinessIdLessThan(Long value) {
            addCriterion("business_id <", value, "businessId");
            return (Criteria) this;
        }

        public Criteria andBusinessIdLessThanOrEqualTo(Long value) {
            addCriterion("business_id <=", value, "businessId");
            return (Criteria) this;
        }

        public Criteria andBusinessIdIn(List<Long> values) {
            addCriterion("business_id in", values, "businessId");
            return (Criteria) this;
        }

        public Criteria andBusinessIdNotIn(List<Long> values) {
            addCriterion("business_id not in", values, "businessId");
            return (Criteria) this;
        }

        public Criteria andBusinessIdBetween(Long value1, Long value2) {
            addCriterion("business_id between", value1, value2, "businessId");
            return (Criteria) this;
        }

        public Criteria andBusinessIdNotBetween(Long value1, Long value2) {
            addCriterion("business_id not between", value1, value2, "businessId");
            return (Criteria) this;
        }

        public Criteria andAssignedTaskIsNull() {
            addCriterion("assigned_task is null");
            return (Criteria) this;
        }

        public Criteria andAssignedTaskIsNotNull() {
            addCriterion("assigned_task is not null");
            return (Criteria) this;
        }

        public Criteria andAssignedTaskEqualTo(Byte value) {
            addCriterion("assigned_task =", value, "assignedTask");
            return (Criteria) this;
        }

        public Criteria andAssignedTaskNotEqualTo(Byte value) {
            addCriterion("assigned_task <>", value, "assignedTask");
            return (Criteria) this;
        }

        public Criteria andAssignedTaskGreaterThan(Byte value) {
            addCriterion("assigned_task >", value, "assignedTask");
            return (Criteria) this;
        }

        public Criteria andAssignedTaskGreaterThanOrEqualTo(Byte value) {
            addCriterion("assigned_task >=", value, "assignedTask");
            return (Criteria) this;
        }

        public Criteria andAssignedTaskLessThan(Byte value) {
            addCriterion("assigned_task <", value, "assignedTask");
            return (Criteria) this;
        }

        public Criteria andAssignedTaskLessThanOrEqualTo(Byte value) {
            addCriterion("assigned_task <=", value, "assignedTask");
            return (Criteria) this;
        }

        public Criteria andAssignedTaskIn(List<Byte> values) {
            addCriterion("assigned_task in", values, "assignedTask");
            return (Criteria) this;
        }

        public Criteria andAssignedTaskNotIn(List<Byte> values) {
            addCriterion("assigned_task not in", values, "assignedTask");
            return (Criteria) this;
        }

        public Criteria andAssignedTaskBetween(Byte value1, Byte value2) {
            addCriterion("assigned_task between", value1, value2, "assignedTask");
            return (Criteria) this;
        }

        public Criteria andAssignedTaskNotBetween(Byte value1, Byte value2) {
            addCriterion("assigned_task not between", value1, value2, "assignedTask");
            return (Criteria) this;
        }
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table moe_staff_notification
     *
     * @mbg.generated do_not_delete_during_merge
     */
    public static class Criteria extends GeneratedCriteria {
        protected Criteria() {
            super();
        }
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table moe_staff_notification
     *
     * @mbg.generated
     */
    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}
