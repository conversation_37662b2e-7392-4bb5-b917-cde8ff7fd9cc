package com.moego.svc.organization.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder(toBuilder = true)
@AllArgsConstructor(staticName = "")
@NoArgsConstructor(staticName = "")
public class MoeBusinessTax {
    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_business_tax.id
     *
     * @mbg.generated
     */
    private Integer id;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_business_tax.business_id
     *
     * @mbg.generated
     */
    private Integer businessId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_business_tax.tax_name
     *
     * @mbg.generated
     */
    private String taxName;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_business_tax.tax_rate
     *
     * @mbg.generated
     */
    private Double taxRate;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_business_tax.status
     *
     * @mbg.generated
     */
    private Byte status;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_business_tax.create_time
     *
     * @mbg.generated
     */
    private Long createTime;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_business_tax.update_time
     *
     * @mbg.generated
     */
    private Long updateTime;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_business_tax.company_id
     *
     * @mbg.generated
     */
    private Long companyId;
}
