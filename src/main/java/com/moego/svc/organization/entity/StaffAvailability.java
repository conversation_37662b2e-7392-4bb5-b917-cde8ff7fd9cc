package com.moego.svc.organization.entity;

import java.time.LocalDate;
import java.time.LocalDateTime;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder(toBuilder = true)
@AllArgsConstructor(staticName = "")
@NoArgsConstructor(staticName = "")
public class StaffAvailability {
    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column staff_availability.id
     *
     * @mbg.generated
     */
    private Long id;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column staff_availability.company_id
     *
     * @mbg.generated
     */
    private Long companyId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column staff_availability.business_id
     *
     * @mbg.generated
     */
    private Long businessId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column staff_availability.staff_id
     *
     * @mbg.generated
     */
    private Long staffId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column staff_availability.slot_schedule_type
     *
     * @mbg.generated
     */
    private Integer slotScheduleType;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column staff_availability.slot_start_sunday
     *
     * @mbg.generated
     */
    private LocalDate slotStartSunday;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column staff_availability.time_schedule_type
     *
     * @mbg.generated
     */
    private Integer timeScheduleType;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column staff_availability.time_start_sunday
     *
     * @mbg.generated
     */
    private LocalDate timeStartSunday;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column staff_availability.created_at
     *
     * @mbg.generated
     */
    private LocalDateTime createdAt;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column staff_availability.updated_at
     *
     * @mbg.generated
     */
    private LocalDateTime updatedAt;
}
