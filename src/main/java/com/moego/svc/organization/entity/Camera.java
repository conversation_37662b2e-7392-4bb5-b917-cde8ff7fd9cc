package com.moego.svc.organization.entity;

import java.time.LocalDateTime;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder(toBuilder = true)
@AllArgsConstructor(staticName = "")
@NoArgsConstructor(staticName = "")
public class Camera {
    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column camera.id
     *
     * @mbg.generated
     */
    private Long id;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column camera.company_id
     *
     * @mbg.generated
     */
    private Long companyId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column camera.business_id
     *
     * @mbg.generated
     */
    private Long businessId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column camera.config_id
     *
     * @mbg.generated
     */
    private Long configId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column camera.type
     *
     * @mbg.generated
     */
    private Byte type;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column camera.origin_camera_id
     *
     * @mbg.generated
     */
    private String originCameraId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column camera.origin_camera_title
     *
     * @mbg.generated
     */
    private String originCameraTitle;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column camera.origin_status
     *
     * @mbg.generated
     */
    private Integer originStatus;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column camera.video_url
     *
     * @mbg.generated
     */
    private String videoUrl;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column camera.is_active
     *
     * @mbg.generated
     */
    private Boolean isActive;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column camera.visibility_type
     *
     * @mbg.generated
     */
    private Integer visibilityType;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column camera.created_at
     *
     * @mbg.generated
     */
    private LocalDateTime createdAt;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column camera.updated_at
     *
     * @mbg.generated
     */
    private LocalDateTime updatedAt;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column camera.auth
     *
     * @mbg.generated
     */
    private String auth;
}
