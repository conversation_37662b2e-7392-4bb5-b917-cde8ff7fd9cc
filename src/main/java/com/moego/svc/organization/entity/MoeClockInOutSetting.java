package com.moego.svc.organization.entity;

import java.time.LocalDateTime;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder(toBuilder = true)
@AllArgsConstructor(staticName = "")
@NoArgsConstructor(staticName = "")
public class MoeClockInOutSetting {
    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_clock_in_out_setting.id
     *
     * @mbg.generated
     */
    private Long id;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_clock_in_out_setting.company_id
     *
     * @mbg.generated
     */
    private Long companyId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_clock_in_out_setting.enable_clock_in_out
     *
     * @mbg.generated
     */
    private Boolean enableClockInOut;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_clock_in_out_setting.clock_in_out_notify
     *
     * @mbg.generated
     */
    private Boolean clockInOutNotify;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_clock_in_out_setting.created_at
     *
     * @mbg.generated
     */
    private LocalDateTime createdAt;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_clock_in_out_setting.updated_at
     *
     * @mbg.generated
     */
    private LocalDateTime updatedAt;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_clock_in_out_setting.updated_by
     *
     * @mbg.generated
     */
    private Long updatedBy;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_clock_in_out_setting.enable_clock_in_out_overnight
     *
     * @mbg.generated
     */
    private Boolean enableClockInOutOvernight;
}
