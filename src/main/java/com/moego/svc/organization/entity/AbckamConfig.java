package com.moego.svc.organization.entity;

import java.time.LocalDateTime;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder(toBuilder = true)
@AllArgsConstructor(staticName = "")
@NoArgsConstructor(staticName = "")
public class AbckamConfig {
    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column abckam_config.id
     *
     * @mbg.generated
     */
    private Long id;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column abckam_config.company_id
     *
     * @mbg.generated
     */
    private Long companyId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column abckam_config.abckam_id
     *
     * @mbg.generated
     */
    private String abckamId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column abckam_config.is_active
     *
     * @mbg.generated
     */
    private Boolean isActive;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column abckam_config.created_at
     *
     * @mbg.generated
     */
    private LocalDateTime createdAt;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column abckam_config.updated_at
     *
     * @mbg.generated
     */
    private LocalDateTime updatedAt;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column abckam_config.business_id
     *
     * @mbg.generated
     */
    private Long businessId;
}
