package com.moego.svc.organization.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder(toBuilder = true)
@AllArgsConstructor(staticName = "")
@NoArgsConstructor(staticName = "")
public class MoeBusinessPaymentMethod {
    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_business_payment_method.id
     *
     * @mbg.generated
     */
    private Integer id;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_business_payment_method.business_id
     *
     * @mbg.generated
     */
    private Integer businessId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_business_payment_method.name
     *
     * @mbg.generated
     */
    private String name;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_business_payment_method.sort
     *
     * @mbg.generated
     */
    private Integer sort;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_business_payment_method.method_id
     *
     * @mbg.generated
     */
    private Integer methodId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_business_payment_method.type
     *
     * @mbg.generated
     */
    private Byte type;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_business_payment_method.inactive
     *
     * @mbg.generated
     */
    private Byte inactive;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_business_payment_method.status
     *
     * @mbg.generated
     */
    private Byte status;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_business_payment_method.create_time
     *
     * @mbg.generated
     */
    private Long createTime;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_business_payment_method.update_time
     *
     * @mbg.generated
     */
    private Long updateTime;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_business_payment_method.company_id
     *
     * @mbg.generated
     */
    private Long companyId;
}
