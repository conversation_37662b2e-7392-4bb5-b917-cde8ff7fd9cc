package com.moego.svc.organization.entity;

import java.util.ArrayList;
import java.util.List;

public class MoeCompanyExample {
    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table moe_company
     *
     * @mbg.generated
     */
    protected String orderByClause;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table moe_company
     *
     * @mbg.generated
     */
    protected boolean distinct;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table moe_company
     *
     * @mbg.generated
     */
    protected List<Criteria> oredCriteria;

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_company
     *
     * @mbg.generated
     */
    public MoeCompanyExample() {
        oredCriteria = new ArrayList<>();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_company
     *
     * @mbg.generated
     */
    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_company
     *
     * @mbg.generated
     */
    public String getOrderByClause() {
        return orderByClause;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_company
     *
     * @mbg.generated
     */
    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_company
     *
     * @mbg.generated
     */
    public boolean isDistinct() {
        return distinct;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_company
     *
     * @mbg.generated
     */
    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_company
     *
     * @mbg.generated
     */
    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_company
     *
     * @mbg.generated
     */
    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_company
     *
     * @mbg.generated
     */
    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_company
     *
     * @mbg.generated
     */
    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_company
     *
     * @mbg.generated
     */
    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table moe_company
     *
     * @mbg.generated
     */
    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Integer value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Integer value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Integer value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Integer value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Integer value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Integer> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Integer> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Integer value1, Integer value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Integer value1, Integer value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andNameIsNull() {
            addCriterion("name is null");
            return (Criteria) this;
        }

        public Criteria andNameIsNotNull() {
            addCriterion("name is not null");
            return (Criteria) this;
        }

        public Criteria andNameEqualTo(String value) {
            addCriterion("name =", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameNotEqualTo(String value) {
            addCriterion("name <>", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameGreaterThan(String value) {
            addCriterion("name >", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameGreaterThanOrEqualTo(String value) {
            addCriterion("name >=", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameLessThan(String value) {
            addCriterion("name <", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameLessThanOrEqualTo(String value) {
            addCriterion("name <=", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameLike(String value) {
            addCriterion("name like", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameNotLike(String value) {
            addCriterion("name not like", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameIn(List<String> values) {
            addCriterion("name in", values, "name");
            return (Criteria) this;
        }

        public Criteria andNameNotIn(List<String> values) {
            addCriterion("name not in", values, "name");
            return (Criteria) this;
        }

        public Criteria andNameBetween(String value1, String value2) {
            addCriterion("name between", value1, value2, "name");
            return (Criteria) this;
        }

        public Criteria andNameNotBetween(String value1, String value2) {
            addCriterion("name not between", value1, value2, "name");
            return (Criteria) this;
        }

        public Criteria andAccountIdIsNull() {
            addCriterion("account_id is null");
            return (Criteria) this;
        }

        public Criteria andAccountIdIsNotNull() {
            addCriterion("account_id is not null");
            return (Criteria) this;
        }

        public Criteria andAccountIdEqualTo(Integer value) {
            addCriterion("account_id =", value, "accountId");
            return (Criteria) this;
        }

        public Criteria andAccountIdNotEqualTo(Integer value) {
            addCriterion("account_id <>", value, "accountId");
            return (Criteria) this;
        }

        public Criteria andAccountIdGreaterThan(Integer value) {
            addCriterion("account_id >", value, "accountId");
            return (Criteria) this;
        }

        public Criteria andAccountIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("account_id >=", value, "accountId");
            return (Criteria) this;
        }

        public Criteria andAccountIdLessThan(Integer value) {
            addCriterion("account_id <", value, "accountId");
            return (Criteria) this;
        }

        public Criteria andAccountIdLessThanOrEqualTo(Integer value) {
            addCriterion("account_id <=", value, "accountId");
            return (Criteria) this;
        }

        public Criteria andAccountIdIn(List<Integer> values) {
            addCriterion("account_id in", values, "accountId");
            return (Criteria) this;
        }

        public Criteria andAccountIdNotIn(List<Integer> values) {
            addCriterion("account_id not in", values, "accountId");
            return (Criteria) this;
        }

        public Criteria andAccountIdBetween(Integer value1, Integer value2) {
            addCriterion("account_id between", value1, value2, "accountId");
            return (Criteria) this;
        }

        public Criteria andAccountIdNotBetween(Integer value1, Integer value2) {
            addCriterion("account_id not between", value1, value2, "accountId");
            return (Criteria) this;
        }

        public Criteria andCountryIsNull() {
            addCriterion("country is null");
            return (Criteria) this;
        }

        public Criteria andCountryIsNotNull() {
            addCriterion("country is not null");
            return (Criteria) this;
        }

        public Criteria andCountryEqualTo(String value) {
            addCriterion("country =", value, "country");
            return (Criteria) this;
        }

        public Criteria andCountryNotEqualTo(String value) {
            addCriterion("country <>", value, "country");
            return (Criteria) this;
        }

        public Criteria andCountryGreaterThan(String value) {
            addCriterion("country >", value, "country");
            return (Criteria) this;
        }

        public Criteria andCountryGreaterThanOrEqualTo(String value) {
            addCriterion("country >=", value, "country");
            return (Criteria) this;
        }

        public Criteria andCountryLessThan(String value) {
            addCriterion("country <", value, "country");
            return (Criteria) this;
        }

        public Criteria andCountryLessThanOrEqualTo(String value) {
            addCriterion("country <=", value, "country");
            return (Criteria) this;
        }

        public Criteria andCountryLike(String value) {
            addCriterion("country like", value, "country");
            return (Criteria) this;
        }

        public Criteria andCountryNotLike(String value) {
            addCriterion("country not like", value, "country");
            return (Criteria) this;
        }

        public Criteria andCountryIn(List<String> values) {
            addCriterion("country in", values, "country");
            return (Criteria) this;
        }

        public Criteria andCountryNotIn(List<String> values) {
            addCriterion("country not in", values, "country");
            return (Criteria) this;
        }

        public Criteria andCountryBetween(String value1, String value2) {
            addCriterion("country between", value1, value2, "country");
            return (Criteria) this;
        }

        public Criteria andCountryNotBetween(String value1, String value2) {
            addCriterion("country not between", value1, value2, "country");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNull() {
            addCriterion("create_time is null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNotNull() {
            addCriterion("create_time is not null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeEqualTo(Long value) {
            addCriterion("create_time =", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotEqualTo(Long value) {
            addCriterion("create_time <>", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThan(Long value) {
            addCriterion("create_time >", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThanOrEqualTo(Long value) {
            addCriterion("create_time >=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThan(Long value) {
            addCriterion("create_time <", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThanOrEqualTo(Long value) {
            addCriterion("create_time <=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIn(List<Long> values) {
            addCriterion("create_time in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotIn(List<Long> values) {
            addCriterion("create_time not in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeBetween(Long value1, Long value2) {
            addCriterion("create_time between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotBetween(Long value1, Long value2) {
            addCriterion("create_time not between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNull() {
            addCriterion("update_time is null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNotNull() {
            addCriterion("update_time is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeEqualTo(Long value) {
            addCriterion("update_time =", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotEqualTo(Long value) {
            addCriterion("update_time <>", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThan(Long value) {
            addCriterion("update_time >", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThanOrEqualTo(Long value) {
            addCriterion("update_time >=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThan(Long value) {
            addCriterion("update_time <", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThanOrEqualTo(Long value) {
            addCriterion("update_time <=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIn(List<Long> values) {
            addCriterion("update_time in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotIn(List<Long> values) {
            addCriterion("update_time not in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeBetween(Long value1, Long value2) {
            addCriterion("update_time between", value1, value2, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotBetween(Long value1, Long value2) {
            addCriterion("update_time not between", value1, value2, "updateTime");
            return (Criteria) this;
        }

        public Criteria andLocationNumIsNull() {
            addCriterion("location_num is null");
            return (Criteria) this;
        }

        public Criteria andLocationNumIsNotNull() {
            addCriterion("location_num is not null");
            return (Criteria) this;
        }

        public Criteria andLocationNumEqualTo(Integer value) {
            addCriterion("location_num =", value, "locationNum");
            return (Criteria) this;
        }

        public Criteria andLocationNumNotEqualTo(Integer value) {
            addCriterion("location_num <>", value, "locationNum");
            return (Criteria) this;
        }

        public Criteria andLocationNumGreaterThan(Integer value) {
            addCriterion("location_num >", value, "locationNum");
            return (Criteria) this;
        }

        public Criteria andLocationNumGreaterThanOrEqualTo(Integer value) {
            addCriterion("location_num >=", value, "locationNum");
            return (Criteria) this;
        }

        public Criteria andLocationNumLessThan(Integer value) {
            addCriterion("location_num <", value, "locationNum");
            return (Criteria) this;
        }

        public Criteria andLocationNumLessThanOrEqualTo(Integer value) {
            addCriterion("location_num <=", value, "locationNum");
            return (Criteria) this;
        }

        public Criteria andLocationNumIn(List<Integer> values) {
            addCriterion("location_num in", values, "locationNum");
            return (Criteria) this;
        }

        public Criteria andLocationNumNotIn(List<Integer> values) {
            addCriterion("location_num not in", values, "locationNum");
            return (Criteria) this;
        }

        public Criteria andLocationNumBetween(Integer value1, Integer value2) {
            addCriterion("location_num between", value1, value2, "locationNum");
            return (Criteria) this;
        }

        public Criteria andLocationNumNotBetween(Integer value1, Integer value2) {
            addCriterion("location_num not between", value1, value2, "locationNum");
            return (Criteria) this;
        }

        public Criteria andStaffNumIsNull() {
            addCriterion("staff_num is null");
            return (Criteria) this;
        }

        public Criteria andStaffNumIsNotNull() {
            addCriterion("staff_num is not null");
            return (Criteria) this;
        }

        public Criteria andStaffNumEqualTo(Integer value) {
            addCriterion("staff_num =", value, "staffNum");
            return (Criteria) this;
        }

        public Criteria andStaffNumNotEqualTo(Integer value) {
            addCriterion("staff_num <>", value, "staffNum");
            return (Criteria) this;
        }

        public Criteria andStaffNumGreaterThan(Integer value) {
            addCriterion("staff_num >", value, "staffNum");
            return (Criteria) this;
        }

        public Criteria andStaffNumGreaterThanOrEqualTo(Integer value) {
            addCriterion("staff_num >=", value, "staffNum");
            return (Criteria) this;
        }

        public Criteria andStaffNumLessThan(Integer value) {
            addCriterion("staff_num <", value, "staffNum");
            return (Criteria) this;
        }

        public Criteria andStaffNumLessThanOrEqualTo(Integer value) {
            addCriterion("staff_num <=", value, "staffNum");
            return (Criteria) this;
        }

        public Criteria andStaffNumIn(List<Integer> values) {
            addCriterion("staff_num in", values, "staffNum");
            return (Criteria) this;
        }

        public Criteria andStaffNumNotIn(List<Integer> values) {
            addCriterion("staff_num not in", values, "staffNum");
            return (Criteria) this;
        }

        public Criteria andStaffNumBetween(Integer value1, Integer value2) {
            addCriterion("staff_num between", value1, value2, "staffNum");
            return (Criteria) this;
        }

        public Criteria andStaffNumNotBetween(Integer value1, Integer value2) {
            addCriterion("staff_num not between", value1, value2, "staffNum");
            return (Criteria) this;
        }

        public Criteria andVansNumIsNull() {
            addCriterion("vans_num is null");
            return (Criteria) this;
        }

        public Criteria andVansNumIsNotNull() {
            addCriterion("vans_num is not null");
            return (Criteria) this;
        }

        public Criteria andVansNumEqualTo(Integer value) {
            addCriterion("vans_num =", value, "vansNum");
            return (Criteria) this;
        }

        public Criteria andVansNumNotEqualTo(Integer value) {
            addCriterion("vans_num <>", value, "vansNum");
            return (Criteria) this;
        }

        public Criteria andVansNumGreaterThan(Integer value) {
            addCriterion("vans_num >", value, "vansNum");
            return (Criteria) this;
        }

        public Criteria andVansNumGreaterThanOrEqualTo(Integer value) {
            addCriterion("vans_num >=", value, "vansNum");
            return (Criteria) this;
        }

        public Criteria andVansNumLessThan(Integer value) {
            addCriterion("vans_num <", value, "vansNum");
            return (Criteria) this;
        }

        public Criteria andVansNumLessThanOrEqualTo(Integer value) {
            addCriterion("vans_num <=", value, "vansNum");
            return (Criteria) this;
        }

        public Criteria andVansNumIn(List<Integer> values) {
            addCriterion("vans_num in", values, "vansNum");
            return (Criteria) this;
        }

        public Criteria andVansNumNotIn(List<Integer> values) {
            addCriterion("vans_num not in", values, "vansNum");
            return (Criteria) this;
        }

        public Criteria andVansNumBetween(Integer value1, Integer value2) {
            addCriterion("vans_num between", value1, value2, "vansNum");
            return (Criteria) this;
        }

        public Criteria andVansNumNotBetween(Integer value1, Integer value2) {
            addCriterion("vans_num not between", value1, value2, "vansNum");
            return (Criteria) this;
        }

        public Criteria andLevelIsNull() {
            addCriterion("level is null");
            return (Criteria) this;
        }

        public Criteria andLevelIsNotNull() {
            addCriterion("level is not null");
            return (Criteria) this;
        }

        public Criteria andLevelEqualTo(Integer value) {
            addCriterion("level =", value, "level");
            return (Criteria) this;
        }

        public Criteria andLevelNotEqualTo(Integer value) {
            addCriterion("level <>", value, "level");
            return (Criteria) this;
        }

        public Criteria andLevelGreaterThan(Integer value) {
            addCriterion("level >", value, "level");
            return (Criteria) this;
        }

        public Criteria andLevelGreaterThanOrEqualTo(Integer value) {
            addCriterion("level >=", value, "level");
            return (Criteria) this;
        }

        public Criteria andLevelLessThan(Integer value) {
            addCriterion("level <", value, "level");
            return (Criteria) this;
        }

        public Criteria andLevelLessThanOrEqualTo(Integer value) {
            addCriterion("level <=", value, "level");
            return (Criteria) this;
        }

        public Criteria andLevelIn(List<Integer> values) {
            addCriterion("level in", values, "level");
            return (Criteria) this;
        }

        public Criteria andLevelNotIn(List<Integer> values) {
            addCriterion("level not in", values, "level");
            return (Criteria) this;
        }

        public Criteria andLevelBetween(Integer value1, Integer value2) {
            addCriterion("level between", value1, value2, "level");
            return (Criteria) this;
        }

        public Criteria andLevelNotBetween(Integer value1, Integer value2) {
            addCriterion("level not between", value1, value2, "level");
            return (Criteria) this;
        }

        public Criteria andIsNewPricingIsNull() {
            addCriterion("is_new_pricing is null");
            return (Criteria) this;
        }

        public Criteria andIsNewPricingIsNotNull() {
            addCriterion("is_new_pricing is not null");
            return (Criteria) this;
        }

        public Criteria andIsNewPricingEqualTo(Integer value) {
            addCriterion("is_new_pricing =", value, "isNewPricing");
            return (Criteria) this;
        }

        public Criteria andIsNewPricingNotEqualTo(Integer value) {
            addCriterion("is_new_pricing <>", value, "isNewPricing");
            return (Criteria) this;
        }

        public Criteria andIsNewPricingGreaterThan(Integer value) {
            addCriterion("is_new_pricing >", value, "isNewPricing");
            return (Criteria) this;
        }

        public Criteria andIsNewPricingGreaterThanOrEqualTo(Integer value) {
            addCriterion("is_new_pricing >=", value, "isNewPricing");
            return (Criteria) this;
        }

        public Criteria andIsNewPricingLessThan(Integer value) {
            addCriterion("is_new_pricing <", value, "isNewPricing");
            return (Criteria) this;
        }

        public Criteria andIsNewPricingLessThanOrEqualTo(Integer value) {
            addCriterion("is_new_pricing <=", value, "isNewPricing");
            return (Criteria) this;
        }

        public Criteria andIsNewPricingIn(List<Integer> values) {
            addCriterion("is_new_pricing in", values, "isNewPricing");
            return (Criteria) this;
        }

        public Criteria andIsNewPricingNotIn(List<Integer> values) {
            addCriterion("is_new_pricing not in", values, "isNewPricing");
            return (Criteria) this;
        }

        public Criteria andIsNewPricingBetween(Integer value1, Integer value2) {
            addCriterion("is_new_pricing between", value1, value2, "isNewPricing");
            return (Criteria) this;
        }

        public Criteria andIsNewPricingNotBetween(Integer value1, Integer value2) {
            addCriterion("is_new_pricing not between", value1, value2, "isNewPricing");
            return (Criteria) this;
        }

        public Criteria andEnableSquareIsNull() {
            addCriterion("enable_square is null");
            return (Criteria) this;
        }

        public Criteria andEnableSquareIsNotNull() {
            addCriterion("enable_square is not null");
            return (Criteria) this;
        }

        public Criteria andEnableSquareEqualTo(Byte value) {
            addCriterion("enable_square =", value, "enableSquare");
            return (Criteria) this;
        }

        public Criteria andEnableSquareNotEqualTo(Byte value) {
            addCriterion("enable_square <>", value, "enableSquare");
            return (Criteria) this;
        }

        public Criteria andEnableSquareGreaterThan(Byte value) {
            addCriterion("enable_square >", value, "enableSquare");
            return (Criteria) this;
        }

        public Criteria andEnableSquareGreaterThanOrEqualTo(Byte value) {
            addCriterion("enable_square >=", value, "enableSquare");
            return (Criteria) this;
        }

        public Criteria andEnableSquareLessThan(Byte value) {
            addCriterion("enable_square <", value, "enableSquare");
            return (Criteria) this;
        }

        public Criteria andEnableSquareLessThanOrEqualTo(Byte value) {
            addCriterion("enable_square <=", value, "enableSquare");
            return (Criteria) this;
        }

        public Criteria andEnableSquareIn(List<Byte> values) {
            addCriterion("enable_square in", values, "enableSquare");
            return (Criteria) this;
        }

        public Criteria andEnableSquareNotIn(List<Byte> values) {
            addCriterion("enable_square not in", values, "enableSquare");
            return (Criteria) this;
        }

        public Criteria andEnableSquareBetween(Byte value1, Byte value2) {
            addCriterion("enable_square between", value1, value2, "enableSquare");
            return (Criteria) this;
        }

        public Criteria andEnableSquareNotBetween(Byte value1, Byte value2) {
            addCriterion("enable_square not between", value1, value2, "enableSquare");
            return (Criteria) this;
        }

        public Criteria andEnableStripeReaderIsNull() {
            addCriterion("enable_stripe_reader is null");
            return (Criteria) this;
        }

        public Criteria andEnableStripeReaderIsNotNull() {
            addCriterion("enable_stripe_reader is not null");
            return (Criteria) this;
        }

        public Criteria andEnableStripeReaderEqualTo(Byte value) {
            addCriterion("enable_stripe_reader =", value, "enableStripeReader");
            return (Criteria) this;
        }

        public Criteria andEnableStripeReaderNotEqualTo(Byte value) {
            addCriterion("enable_stripe_reader <>", value, "enableStripeReader");
            return (Criteria) this;
        }

        public Criteria andEnableStripeReaderGreaterThan(Byte value) {
            addCriterion("enable_stripe_reader >", value, "enableStripeReader");
            return (Criteria) this;
        }

        public Criteria andEnableStripeReaderGreaterThanOrEqualTo(Byte value) {
            addCriterion("enable_stripe_reader >=", value, "enableStripeReader");
            return (Criteria) this;
        }

        public Criteria andEnableStripeReaderLessThan(Byte value) {
            addCriterion("enable_stripe_reader <", value, "enableStripeReader");
            return (Criteria) this;
        }

        public Criteria andEnableStripeReaderLessThanOrEqualTo(Byte value) {
            addCriterion("enable_stripe_reader <=", value, "enableStripeReader");
            return (Criteria) this;
        }

        public Criteria andEnableStripeReaderIn(List<Byte> values) {
            addCriterion("enable_stripe_reader in", values, "enableStripeReader");
            return (Criteria) this;
        }

        public Criteria andEnableStripeReaderNotIn(List<Byte> values) {
            addCriterion("enable_stripe_reader not in", values, "enableStripeReader");
            return (Criteria) this;
        }

        public Criteria andEnableStripeReaderBetween(Byte value1, Byte value2) {
            addCriterion("enable_stripe_reader between", value1, value2, "enableStripeReader");
            return (Criteria) this;
        }

        public Criteria andEnableStripeReaderNotBetween(Byte value1, Byte value2) {
            addCriterion("enable_stripe_reader not between", value1, value2, "enableStripeReader");
            return (Criteria) this;
        }

        public Criteria andEnterpriseIdIsNull() {
            addCriterion("enterprise_id is null");
            return (Criteria) this;
        }

        public Criteria andEnterpriseIdIsNotNull() {
            addCriterion("enterprise_id is not null");
            return (Criteria) this;
        }

        public Criteria andEnterpriseIdEqualTo(Integer value) {
            addCriterion("enterprise_id =", value, "enterpriseId");
            return (Criteria) this;
        }

        public Criteria andEnterpriseIdNotEqualTo(Integer value) {
            addCriterion("enterprise_id <>", value, "enterpriseId");
            return (Criteria) this;
        }

        public Criteria andEnterpriseIdGreaterThan(Integer value) {
            addCriterion("enterprise_id >", value, "enterpriseId");
            return (Criteria) this;
        }

        public Criteria andEnterpriseIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("enterprise_id >=", value, "enterpriseId");
            return (Criteria) this;
        }

        public Criteria andEnterpriseIdLessThan(Integer value) {
            addCriterion("enterprise_id <", value, "enterpriseId");
            return (Criteria) this;
        }

        public Criteria andEnterpriseIdLessThanOrEqualTo(Integer value) {
            addCriterion("enterprise_id <=", value, "enterpriseId");
            return (Criteria) this;
        }

        public Criteria andEnterpriseIdIn(List<Integer> values) {
            addCriterion("enterprise_id in", values, "enterpriseId");
            return (Criteria) this;
        }

        public Criteria andEnterpriseIdNotIn(List<Integer> values) {
            addCriterion("enterprise_id not in", values, "enterpriseId");
            return (Criteria) this;
        }

        public Criteria andEnterpriseIdBetween(Integer value1, Integer value2) {
            addCriterion("enterprise_id between", value1, value2, "enterpriseId");
            return (Criteria) this;
        }

        public Criteria andEnterpriseIdNotBetween(Integer value1, Integer value2) {
            addCriterion("enterprise_id not between", value1, value2, "enterpriseId");
            return (Criteria) this;
        }

        public Criteria andCurrencyCodeIsNull() {
            addCriterion("currency_code is null");
            return (Criteria) this;
        }

        public Criteria andCurrencyCodeIsNotNull() {
            addCriterion("currency_code is not null");
            return (Criteria) this;
        }

        public Criteria andCurrencyCodeEqualTo(String value) {
            addCriterion("currency_code =", value, "currencyCode");
            return (Criteria) this;
        }

        public Criteria andCurrencyCodeNotEqualTo(String value) {
            addCriterion("currency_code <>", value, "currencyCode");
            return (Criteria) this;
        }

        public Criteria andCurrencyCodeGreaterThan(String value) {
            addCriterion("currency_code >", value, "currencyCode");
            return (Criteria) this;
        }

        public Criteria andCurrencyCodeGreaterThanOrEqualTo(String value) {
            addCriterion("currency_code >=", value, "currencyCode");
            return (Criteria) this;
        }

        public Criteria andCurrencyCodeLessThan(String value) {
            addCriterion("currency_code <", value, "currencyCode");
            return (Criteria) this;
        }

        public Criteria andCurrencyCodeLessThanOrEqualTo(String value) {
            addCriterion("currency_code <=", value, "currencyCode");
            return (Criteria) this;
        }

        public Criteria andCurrencyCodeLike(String value) {
            addCriterion("currency_code like", value, "currencyCode");
            return (Criteria) this;
        }

        public Criteria andCurrencyCodeNotLike(String value) {
            addCriterion("currency_code not like", value, "currencyCode");
            return (Criteria) this;
        }

        public Criteria andCurrencyCodeIn(List<String> values) {
            addCriterion("currency_code in", values, "currencyCode");
            return (Criteria) this;
        }

        public Criteria andCurrencyCodeNotIn(List<String> values) {
            addCriterion("currency_code not in", values, "currencyCode");
            return (Criteria) this;
        }

        public Criteria andCurrencyCodeBetween(String value1, String value2) {
            addCriterion("currency_code between", value1, value2, "currencyCode");
            return (Criteria) this;
        }

        public Criteria andCurrencyCodeNotBetween(String value1, String value2) {
            addCriterion("currency_code not between", value1, value2, "currencyCode");
            return (Criteria) this;
        }

        public Criteria andCurrencySymbolIsNull() {
            addCriterion("currency_symbol is null");
            return (Criteria) this;
        }

        public Criteria andCurrencySymbolIsNotNull() {
            addCriterion("currency_symbol is not null");
            return (Criteria) this;
        }

        public Criteria andCurrencySymbolEqualTo(String value) {
            addCriterion("currency_symbol =", value, "currencySymbol");
            return (Criteria) this;
        }

        public Criteria andCurrencySymbolNotEqualTo(String value) {
            addCriterion("currency_symbol <>", value, "currencySymbol");
            return (Criteria) this;
        }

        public Criteria andCurrencySymbolGreaterThan(String value) {
            addCriterion("currency_symbol >", value, "currencySymbol");
            return (Criteria) this;
        }

        public Criteria andCurrencySymbolGreaterThanOrEqualTo(String value) {
            addCriterion("currency_symbol >=", value, "currencySymbol");
            return (Criteria) this;
        }

        public Criteria andCurrencySymbolLessThan(String value) {
            addCriterion("currency_symbol <", value, "currencySymbol");
            return (Criteria) this;
        }

        public Criteria andCurrencySymbolLessThanOrEqualTo(String value) {
            addCriterion("currency_symbol <=", value, "currencySymbol");
            return (Criteria) this;
        }

        public Criteria andCurrencySymbolLike(String value) {
            addCriterion("currency_symbol like", value, "currencySymbol");
            return (Criteria) this;
        }

        public Criteria andCurrencySymbolNotLike(String value) {
            addCriterion("currency_symbol not like", value, "currencySymbol");
            return (Criteria) this;
        }

        public Criteria andCurrencySymbolIn(List<String> values) {
            addCriterion("currency_symbol in", values, "currencySymbol");
            return (Criteria) this;
        }

        public Criteria andCurrencySymbolNotIn(List<String> values) {
            addCriterion("currency_symbol not in", values, "currencySymbol");
            return (Criteria) this;
        }

        public Criteria andCurrencySymbolBetween(String value1, String value2) {
            addCriterion("currency_symbol between", value1, value2, "currencySymbol");
            return (Criteria) this;
        }

        public Criteria andCurrencySymbolNotBetween(String value1, String value2) {
            addCriterion("currency_symbol not between", value1, value2, "currencySymbol");
            return (Criteria) this;
        }

        public Criteria andDateFormatTypeIsNull() {
            addCriterion("date_format_type is null");
            return (Criteria) this;
        }

        public Criteria andDateFormatTypeIsNotNull() {
            addCriterion("date_format_type is not null");
            return (Criteria) this;
        }

        public Criteria andDateFormatTypeEqualTo(Byte value) {
            addCriterion("date_format_type =", value, "dateFormatType");
            return (Criteria) this;
        }

        public Criteria andDateFormatTypeNotEqualTo(Byte value) {
            addCriterion("date_format_type <>", value, "dateFormatType");
            return (Criteria) this;
        }

        public Criteria andDateFormatTypeGreaterThan(Byte value) {
            addCriterion("date_format_type >", value, "dateFormatType");
            return (Criteria) this;
        }

        public Criteria andDateFormatTypeGreaterThanOrEqualTo(Byte value) {
            addCriterion("date_format_type >=", value, "dateFormatType");
            return (Criteria) this;
        }

        public Criteria andDateFormatTypeLessThan(Byte value) {
            addCriterion("date_format_type <", value, "dateFormatType");
            return (Criteria) this;
        }

        public Criteria andDateFormatTypeLessThanOrEqualTo(Byte value) {
            addCriterion("date_format_type <=", value, "dateFormatType");
            return (Criteria) this;
        }

        public Criteria andDateFormatTypeIn(List<Byte> values) {
            addCriterion("date_format_type in", values, "dateFormatType");
            return (Criteria) this;
        }

        public Criteria andDateFormatTypeNotIn(List<Byte> values) {
            addCriterion("date_format_type not in", values, "dateFormatType");
            return (Criteria) this;
        }

        public Criteria andDateFormatTypeBetween(Byte value1, Byte value2) {
            addCriterion("date_format_type between", value1, value2, "dateFormatType");
            return (Criteria) this;
        }

        public Criteria andDateFormatTypeNotBetween(Byte value1, Byte value2) {
            addCriterion("date_format_type not between", value1, value2, "dateFormatType");
            return (Criteria) this;
        }

        public Criteria andTimeFormatTypeIsNull() {
            addCriterion("time_format_type is null");
            return (Criteria) this;
        }

        public Criteria andTimeFormatTypeIsNotNull() {
            addCriterion("time_format_type is not null");
            return (Criteria) this;
        }

        public Criteria andTimeFormatTypeEqualTo(Byte value) {
            addCriterion("time_format_type =", value, "timeFormatType");
            return (Criteria) this;
        }

        public Criteria andTimeFormatTypeNotEqualTo(Byte value) {
            addCriterion("time_format_type <>", value, "timeFormatType");
            return (Criteria) this;
        }

        public Criteria andTimeFormatTypeGreaterThan(Byte value) {
            addCriterion("time_format_type >", value, "timeFormatType");
            return (Criteria) this;
        }

        public Criteria andTimeFormatTypeGreaterThanOrEqualTo(Byte value) {
            addCriterion("time_format_type >=", value, "timeFormatType");
            return (Criteria) this;
        }

        public Criteria andTimeFormatTypeLessThan(Byte value) {
            addCriterion("time_format_type <", value, "timeFormatType");
            return (Criteria) this;
        }

        public Criteria andTimeFormatTypeLessThanOrEqualTo(Byte value) {
            addCriterion("time_format_type <=", value, "timeFormatType");
            return (Criteria) this;
        }

        public Criteria andTimeFormatTypeIn(List<Byte> values) {
            addCriterion("time_format_type in", values, "timeFormatType");
            return (Criteria) this;
        }

        public Criteria andTimeFormatTypeNotIn(List<Byte> values) {
            addCriterion("time_format_type not in", values, "timeFormatType");
            return (Criteria) this;
        }

        public Criteria andTimeFormatTypeBetween(Byte value1, Byte value2) {
            addCriterion("time_format_type between", value1, value2, "timeFormatType");
            return (Criteria) this;
        }

        public Criteria andTimeFormatTypeNotBetween(Byte value1, Byte value2) {
            addCriterion("time_format_type not between", value1, value2, "timeFormatType");
            return (Criteria) this;
        }

        public Criteria andUnitOfWeightTypeIsNull() {
            addCriterion("unit_of_weight_type is null");
            return (Criteria) this;
        }

        public Criteria andUnitOfWeightTypeIsNotNull() {
            addCriterion("unit_of_weight_type is not null");
            return (Criteria) this;
        }

        public Criteria andUnitOfWeightTypeEqualTo(Byte value) {
            addCriterion("unit_of_weight_type =", value, "unitOfWeightType");
            return (Criteria) this;
        }

        public Criteria andUnitOfWeightTypeNotEqualTo(Byte value) {
            addCriterion("unit_of_weight_type <>", value, "unitOfWeightType");
            return (Criteria) this;
        }

        public Criteria andUnitOfWeightTypeGreaterThan(Byte value) {
            addCriterion("unit_of_weight_type >", value, "unitOfWeightType");
            return (Criteria) this;
        }

        public Criteria andUnitOfWeightTypeGreaterThanOrEqualTo(Byte value) {
            addCriterion("unit_of_weight_type >=", value, "unitOfWeightType");
            return (Criteria) this;
        }

        public Criteria andUnitOfWeightTypeLessThan(Byte value) {
            addCriterion("unit_of_weight_type <", value, "unitOfWeightType");
            return (Criteria) this;
        }

        public Criteria andUnitOfWeightTypeLessThanOrEqualTo(Byte value) {
            addCriterion("unit_of_weight_type <=", value, "unitOfWeightType");
            return (Criteria) this;
        }

        public Criteria andUnitOfWeightTypeIn(List<Byte> values) {
            addCriterion("unit_of_weight_type in", values, "unitOfWeightType");
            return (Criteria) this;
        }

        public Criteria andUnitOfWeightTypeNotIn(List<Byte> values) {
            addCriterion("unit_of_weight_type not in", values, "unitOfWeightType");
            return (Criteria) this;
        }

        public Criteria andUnitOfWeightTypeBetween(Byte value1, Byte value2) {
            addCriterion("unit_of_weight_type between", value1, value2, "unitOfWeightType");
            return (Criteria) this;
        }

        public Criteria andUnitOfWeightTypeNotBetween(Byte value1, Byte value2) {
            addCriterion("unit_of_weight_type not between", value1, value2, "unitOfWeightType");
            return (Criteria) this;
        }

        public Criteria andUnitOfDistanceTypeIsNull() {
            addCriterion("unit_of_distance_type is null");
            return (Criteria) this;
        }

        public Criteria andUnitOfDistanceTypeIsNotNull() {
            addCriterion("unit_of_distance_type is not null");
            return (Criteria) this;
        }

        public Criteria andUnitOfDistanceTypeEqualTo(Byte value) {
            addCriterion("unit_of_distance_type =", value, "unitOfDistanceType");
            return (Criteria) this;
        }

        public Criteria andUnitOfDistanceTypeNotEqualTo(Byte value) {
            addCriterion("unit_of_distance_type <>", value, "unitOfDistanceType");
            return (Criteria) this;
        }

        public Criteria andUnitOfDistanceTypeGreaterThan(Byte value) {
            addCriterion("unit_of_distance_type >", value, "unitOfDistanceType");
            return (Criteria) this;
        }

        public Criteria andUnitOfDistanceTypeGreaterThanOrEqualTo(Byte value) {
            addCriterion("unit_of_distance_type >=", value, "unitOfDistanceType");
            return (Criteria) this;
        }

        public Criteria andUnitOfDistanceTypeLessThan(Byte value) {
            addCriterion("unit_of_distance_type <", value, "unitOfDistanceType");
            return (Criteria) this;
        }

        public Criteria andUnitOfDistanceTypeLessThanOrEqualTo(Byte value) {
            addCriterion("unit_of_distance_type <=", value, "unitOfDistanceType");
            return (Criteria) this;
        }

        public Criteria andUnitOfDistanceTypeIn(List<Byte> values) {
            addCriterion("unit_of_distance_type in", values, "unitOfDistanceType");
            return (Criteria) this;
        }

        public Criteria andUnitOfDistanceTypeNotIn(List<Byte> values) {
            addCriterion("unit_of_distance_type not in", values, "unitOfDistanceType");
            return (Criteria) this;
        }

        public Criteria andUnitOfDistanceTypeBetween(Byte value1, Byte value2) {
            addCriterion("unit_of_distance_type between", value1, value2, "unitOfDistanceType");
            return (Criteria) this;
        }

        public Criteria andUnitOfDistanceTypeNotBetween(Byte value1, Byte value2) {
            addCriterion("unit_of_distance_type not between", value1, value2, "unitOfDistanceType");
            return (Criteria) this;
        }

        public Criteria andNotificationSoundEnableIsNull() {
            addCriterion("notification_sound_enable is null");
            return (Criteria) this;
        }

        public Criteria andNotificationSoundEnableIsNotNull() {
            addCriterion("notification_sound_enable is not null");
            return (Criteria) this;
        }

        public Criteria andNotificationSoundEnableEqualTo(Boolean value) {
            addCriterion("notification_sound_enable =", value, "notificationSoundEnable");
            return (Criteria) this;
        }

        public Criteria andNotificationSoundEnableNotEqualTo(Boolean value) {
            addCriterion("notification_sound_enable <>", value, "notificationSoundEnable");
            return (Criteria) this;
        }

        public Criteria andNotificationSoundEnableGreaterThan(Boolean value) {
            addCriterion("notification_sound_enable >", value, "notificationSoundEnable");
            return (Criteria) this;
        }

        public Criteria andNotificationSoundEnableGreaterThanOrEqualTo(Boolean value) {
            addCriterion("notification_sound_enable >=", value, "notificationSoundEnable");
            return (Criteria) this;
        }

        public Criteria andNotificationSoundEnableLessThan(Boolean value) {
            addCriterion("notification_sound_enable <", value, "notificationSoundEnable");
            return (Criteria) this;
        }

        public Criteria andNotificationSoundEnableLessThanOrEqualTo(Boolean value) {
            addCriterion("notification_sound_enable <=", value, "notificationSoundEnable");
            return (Criteria) this;
        }

        public Criteria andNotificationSoundEnableIn(List<Boolean> values) {
            addCriterion("notification_sound_enable in", values, "notificationSoundEnable");
            return (Criteria) this;
        }

        public Criteria andNotificationSoundEnableNotIn(List<Boolean> values) {
            addCriterion("notification_sound_enable not in", values, "notificationSoundEnable");
            return (Criteria) this;
        }

        public Criteria andNotificationSoundEnableBetween(Boolean value1, Boolean value2) {
            addCriterion("notification_sound_enable between", value1, value2, "notificationSoundEnable");
            return (Criteria) this;
        }

        public Criteria andNotificationSoundEnableNotBetween(Boolean value1, Boolean value2) {
            addCriterion("notification_sound_enable not between", value1, value2, "notificationSoundEnable");
            return (Criteria) this;
        }

        public Criteria andCountryAlpha2CodeIsNull() {
            addCriterion("country_alpha2_code is null");
            return (Criteria) this;
        }

        public Criteria andCountryAlpha2CodeIsNotNull() {
            addCriterion("country_alpha2_code is not null");
            return (Criteria) this;
        }

        public Criteria andCountryAlpha2CodeEqualTo(String value) {
            addCriterion("country_alpha2_code =", value, "countryAlpha2Code");
            return (Criteria) this;
        }

        public Criteria andCountryAlpha2CodeNotEqualTo(String value) {
            addCriterion("country_alpha2_code <>", value, "countryAlpha2Code");
            return (Criteria) this;
        }

        public Criteria andCountryAlpha2CodeGreaterThan(String value) {
            addCriterion("country_alpha2_code >", value, "countryAlpha2Code");
            return (Criteria) this;
        }

        public Criteria andCountryAlpha2CodeGreaterThanOrEqualTo(String value) {
            addCriterion("country_alpha2_code >=", value, "countryAlpha2Code");
            return (Criteria) this;
        }

        public Criteria andCountryAlpha2CodeLessThan(String value) {
            addCriterion("country_alpha2_code <", value, "countryAlpha2Code");
            return (Criteria) this;
        }

        public Criteria andCountryAlpha2CodeLessThanOrEqualTo(String value) {
            addCriterion("country_alpha2_code <=", value, "countryAlpha2Code");
            return (Criteria) this;
        }

        public Criteria andCountryAlpha2CodeLike(String value) {
            addCriterion("country_alpha2_code like", value, "countryAlpha2Code");
            return (Criteria) this;
        }

        public Criteria andCountryAlpha2CodeNotLike(String value) {
            addCriterion("country_alpha2_code not like", value, "countryAlpha2Code");
            return (Criteria) this;
        }

        public Criteria andCountryAlpha2CodeIn(List<String> values) {
            addCriterion("country_alpha2_code in", values, "countryAlpha2Code");
            return (Criteria) this;
        }

        public Criteria andCountryAlpha2CodeNotIn(List<String> values) {
            addCriterion("country_alpha2_code not in", values, "countryAlpha2Code");
            return (Criteria) this;
        }

        public Criteria andCountryAlpha2CodeBetween(String value1, String value2) {
            addCriterion("country_alpha2_code between", value1, value2, "countryAlpha2Code");
            return (Criteria) this;
        }

        public Criteria andCountryAlpha2CodeNotBetween(String value1, String value2) {
            addCriterion("country_alpha2_code not between", value1, value2, "countryAlpha2Code");
            return (Criteria) this;
        }

        public Criteria andTimezoneNameIsNull() {
            addCriterion("timezone_name is null");
            return (Criteria) this;
        }

        public Criteria andTimezoneNameIsNotNull() {
            addCriterion("timezone_name is not null");
            return (Criteria) this;
        }

        public Criteria andTimezoneNameEqualTo(String value) {
            addCriterion("timezone_name =", value, "timezoneName");
            return (Criteria) this;
        }

        public Criteria andTimezoneNameNotEqualTo(String value) {
            addCriterion("timezone_name <>", value, "timezoneName");
            return (Criteria) this;
        }

        public Criteria andTimezoneNameGreaterThan(String value) {
            addCriterion("timezone_name >", value, "timezoneName");
            return (Criteria) this;
        }

        public Criteria andTimezoneNameGreaterThanOrEqualTo(String value) {
            addCriterion("timezone_name >=", value, "timezoneName");
            return (Criteria) this;
        }

        public Criteria andTimezoneNameLessThan(String value) {
            addCriterion("timezone_name <", value, "timezoneName");
            return (Criteria) this;
        }

        public Criteria andTimezoneNameLessThanOrEqualTo(String value) {
            addCriterion("timezone_name <=", value, "timezoneName");
            return (Criteria) this;
        }

        public Criteria andTimezoneNameLike(String value) {
            addCriterion("timezone_name like", value, "timezoneName");
            return (Criteria) this;
        }

        public Criteria andTimezoneNameNotLike(String value) {
            addCriterion("timezone_name not like", value, "timezoneName");
            return (Criteria) this;
        }

        public Criteria andTimezoneNameIn(List<String> values) {
            addCriterion("timezone_name in", values, "timezoneName");
            return (Criteria) this;
        }

        public Criteria andTimezoneNameNotIn(List<String> values) {
            addCriterion("timezone_name not in", values, "timezoneName");
            return (Criteria) this;
        }

        public Criteria andTimezoneNameBetween(String value1, String value2) {
            addCriterion("timezone_name between", value1, value2, "timezoneName");
            return (Criteria) this;
        }

        public Criteria andTimezoneNameNotBetween(String value1, String value2) {
            addCriterion("timezone_name not between", value1, value2, "timezoneName");
            return (Criteria) this;
        }

        public Criteria andTimezoneSecondsIsNull() {
            addCriterion("timezone_seconds is null");
            return (Criteria) this;
        }

        public Criteria andTimezoneSecondsIsNotNull() {
            addCriterion("timezone_seconds is not null");
            return (Criteria) this;
        }

        public Criteria andTimezoneSecondsEqualTo(Integer value) {
            addCriterion("timezone_seconds =", value, "timezoneSeconds");
            return (Criteria) this;
        }

        public Criteria andTimezoneSecondsNotEqualTo(Integer value) {
            addCriterion("timezone_seconds <>", value, "timezoneSeconds");
            return (Criteria) this;
        }

        public Criteria andTimezoneSecondsGreaterThan(Integer value) {
            addCriterion("timezone_seconds >", value, "timezoneSeconds");
            return (Criteria) this;
        }

        public Criteria andTimezoneSecondsGreaterThanOrEqualTo(Integer value) {
            addCriterion("timezone_seconds >=", value, "timezoneSeconds");
            return (Criteria) this;
        }

        public Criteria andTimezoneSecondsLessThan(Integer value) {
            addCriterion("timezone_seconds <", value, "timezoneSeconds");
            return (Criteria) this;
        }

        public Criteria andTimezoneSecondsLessThanOrEqualTo(Integer value) {
            addCriterion("timezone_seconds <=", value, "timezoneSeconds");
            return (Criteria) this;
        }

        public Criteria andTimezoneSecondsIn(List<Integer> values) {
            addCriterion("timezone_seconds in", values, "timezoneSeconds");
            return (Criteria) this;
        }

        public Criteria andTimezoneSecondsNotIn(List<Integer> values) {
            addCriterion("timezone_seconds not in", values, "timezoneSeconds");
            return (Criteria) this;
        }

        public Criteria andTimezoneSecondsBetween(Integer value1, Integer value2) {
            addCriterion("timezone_seconds between", value1, value2, "timezoneSeconds");
            return (Criteria) this;
        }

        public Criteria andTimezoneSecondsNotBetween(Integer value1, Integer value2) {
            addCriterion("timezone_seconds not between", value1, value2, "timezoneSeconds");
            return (Criteria) this;
        }

        public Criteria andSourceIsNull() {
            addCriterion("source is null");
            return (Criteria) this;
        }

        public Criteria andSourceIsNotNull() {
            addCriterion("source is not null");
            return (Criteria) this;
        }

        public Criteria andSourceEqualTo(Integer value) {
            addCriterion("source =", value, "source");
            return (Criteria) this;
        }

        public Criteria andSourceNotEqualTo(Integer value) {
            addCriterion("source <>", value, "source");
            return (Criteria) this;
        }

        public Criteria andSourceGreaterThan(Integer value) {
            addCriterion("source >", value, "source");
            return (Criteria) this;
        }

        public Criteria andSourceGreaterThanOrEqualTo(Integer value) {
            addCriterion("source >=", value, "source");
            return (Criteria) this;
        }

        public Criteria andSourceLessThan(Integer value) {
            addCriterion("source <", value, "source");
            return (Criteria) this;
        }

        public Criteria andSourceLessThanOrEqualTo(Integer value) {
            addCriterion("source <=", value, "source");
            return (Criteria) this;
        }

        public Criteria andSourceIn(List<Integer> values) {
            addCriterion("source in", values, "source");
            return (Criteria) this;
        }

        public Criteria andSourceNotIn(List<Integer> values) {
            addCriterion("source not in", values, "source");
            return (Criteria) this;
        }

        public Criteria andSourceBetween(Integer value1, Integer value2) {
            addCriterion("source between", value1, value2, "source");
            return (Criteria) this;
        }

        public Criteria andSourceNotBetween(Integer value1, Integer value2) {
            addCriterion("source not between", value1, value2, "source");
            return (Criteria) this;
        }

        public Criteria andKnowAboutUsIsNull() {
            addCriterion("know_about_us is null");
            return (Criteria) this;
        }

        public Criteria andKnowAboutUsIsNotNull() {
            addCriterion("know_about_us is not null");
            return (Criteria) this;
        }

        public Criteria andKnowAboutUsEqualTo(String value) {
            addCriterion("know_about_us =", value, "knowAboutUs");
            return (Criteria) this;
        }

        public Criteria andKnowAboutUsNotEqualTo(String value) {
            addCriterion("know_about_us <>", value, "knowAboutUs");
            return (Criteria) this;
        }

        public Criteria andKnowAboutUsGreaterThan(String value) {
            addCriterion("know_about_us >", value, "knowAboutUs");
            return (Criteria) this;
        }

        public Criteria andKnowAboutUsGreaterThanOrEqualTo(String value) {
            addCriterion("know_about_us >=", value, "knowAboutUs");
            return (Criteria) this;
        }

        public Criteria andKnowAboutUsLessThan(String value) {
            addCriterion("know_about_us <", value, "knowAboutUs");
            return (Criteria) this;
        }

        public Criteria andKnowAboutUsLessThanOrEqualTo(String value) {
            addCriterion("know_about_us <=", value, "knowAboutUs");
            return (Criteria) this;
        }

        public Criteria andKnowAboutUsLike(String value) {
            addCriterion("know_about_us like", value, "knowAboutUs");
            return (Criteria) this;
        }

        public Criteria andKnowAboutUsNotLike(String value) {
            addCriterion("know_about_us not like", value, "knowAboutUs");
            return (Criteria) this;
        }

        public Criteria andKnowAboutUsIn(List<String> values) {
            addCriterion("know_about_us in", values, "knowAboutUs");
            return (Criteria) this;
        }

        public Criteria andKnowAboutUsNotIn(List<String> values) {
            addCriterion("know_about_us not in", values, "knowAboutUs");
            return (Criteria) this;
        }

        public Criteria andKnowAboutUsBetween(String value1, String value2) {
            addCriterion("know_about_us between", value1, value2, "knowAboutUs");
            return (Criteria) this;
        }

        public Criteria andKnowAboutUsNotBetween(String value1, String value2) {
            addCriterion("know_about_us not between", value1, value2, "knowAboutUs");
            return (Criteria) this;
        }

        public Criteria andCompanyTypeIsNull() {
            addCriterion("company_type is null");
            return (Criteria) this;
        }

        public Criteria andCompanyTypeIsNotNull() {
            addCriterion("company_type is not null");
            return (Criteria) this;
        }

        public Criteria andCompanyTypeEqualTo(Byte value) {
            addCriterion("company_type =", value, "companyType");
            return (Criteria) this;
        }

        public Criteria andCompanyTypeNotEqualTo(Byte value) {
            addCriterion("company_type <>", value, "companyType");
            return (Criteria) this;
        }

        public Criteria andCompanyTypeGreaterThan(Byte value) {
            addCriterion("company_type >", value, "companyType");
            return (Criteria) this;
        }

        public Criteria andCompanyTypeGreaterThanOrEqualTo(Byte value) {
            addCriterion("company_type >=", value, "companyType");
            return (Criteria) this;
        }

        public Criteria andCompanyTypeLessThan(Byte value) {
            addCriterion("company_type <", value, "companyType");
            return (Criteria) this;
        }

        public Criteria andCompanyTypeLessThanOrEqualTo(Byte value) {
            addCriterion("company_type <=", value, "companyType");
            return (Criteria) this;
        }

        public Criteria andCompanyTypeIn(List<Byte> values) {
            addCriterion("company_type in", values, "companyType");
            return (Criteria) this;
        }

        public Criteria andCompanyTypeNotIn(List<Byte> values) {
            addCriterion("company_type not in", values, "companyType");
            return (Criteria) this;
        }

        public Criteria andCompanyTypeBetween(Byte value1, Byte value2) {
            addCriterion("company_type between", value1, value2, "companyType");
            return (Criteria) this;
        }

        public Criteria andCompanyTypeNotBetween(Byte value1, Byte value2) {
            addCriterion("company_type not between", value1, value2, "companyType");
            return (Criteria) this;
        }

        public Criteria andThemeColorIsNull() {
            addCriterion("theme_color is null");
            return (Criteria) this;
        }

        public Criteria andThemeColorIsNotNull() {
            addCriterion("theme_color is not null");
            return (Criteria) this;
        }

        public Criteria andThemeColorEqualTo(String value) {
            addCriterion("theme_color =", value, "themeColor");
            return (Criteria) this;
        }

        public Criteria andThemeColorNotEqualTo(String value) {
            addCriterion("theme_color <>", value, "themeColor");
            return (Criteria) this;
        }

        public Criteria andThemeColorGreaterThan(String value) {
            addCriterion("theme_color >", value, "themeColor");
            return (Criteria) this;
        }

        public Criteria andThemeColorGreaterThanOrEqualTo(String value) {
            addCriterion("theme_color >=", value, "themeColor");
            return (Criteria) this;
        }

        public Criteria andThemeColorLessThan(String value) {
            addCriterion("theme_color <", value, "themeColor");
            return (Criteria) this;
        }

        public Criteria andThemeColorLessThanOrEqualTo(String value) {
            addCriterion("theme_color <=", value, "themeColor");
            return (Criteria) this;
        }

        public Criteria andThemeColorLike(String value) {
            addCriterion("theme_color like", value, "themeColor");
            return (Criteria) this;
        }

        public Criteria andThemeColorNotLike(String value) {
            addCriterion("theme_color not like", value, "themeColor");
            return (Criteria) this;
        }

        public Criteria andThemeColorIn(List<String> values) {
            addCriterion("theme_color in", values, "themeColor");
            return (Criteria) this;
        }

        public Criteria andThemeColorNotIn(List<String> values) {
            addCriterion("theme_color not in", values, "themeColor");
            return (Criteria) this;
        }

        public Criteria andThemeColorBetween(String value1, String value2) {
            addCriterion("theme_color between", value1, value2, "themeColor");
            return (Criteria) this;
        }

        public Criteria andThemeColorNotBetween(String value1, String value2) {
            addCriterion("theme_color not between", value1, value2, "themeColor");
            return (Criteria) this;
        }

        public Criteria andLogoPathIsNull() {
            addCriterion("logo_path is null");
            return (Criteria) this;
        }

        public Criteria andLogoPathIsNotNull() {
            addCriterion("logo_path is not null");
            return (Criteria) this;
        }

        public Criteria andLogoPathEqualTo(String value) {
            addCriterion("logo_path =", value, "logoPath");
            return (Criteria) this;
        }

        public Criteria andLogoPathNotEqualTo(String value) {
            addCriterion("logo_path <>", value, "logoPath");
            return (Criteria) this;
        }

        public Criteria andLogoPathGreaterThan(String value) {
            addCriterion("logo_path >", value, "logoPath");
            return (Criteria) this;
        }

        public Criteria andLogoPathGreaterThanOrEqualTo(String value) {
            addCriterion("logo_path >=", value, "logoPath");
            return (Criteria) this;
        }

        public Criteria andLogoPathLessThan(String value) {
            addCriterion("logo_path <", value, "logoPath");
            return (Criteria) this;
        }

        public Criteria andLogoPathLessThanOrEqualTo(String value) {
            addCriterion("logo_path <=", value, "logoPath");
            return (Criteria) this;
        }

        public Criteria andLogoPathLike(String value) {
            addCriterion("logo_path like", value, "logoPath");
            return (Criteria) this;
        }

        public Criteria andLogoPathNotLike(String value) {
            addCriterion("logo_path not like", value, "logoPath");
            return (Criteria) this;
        }

        public Criteria andLogoPathIn(List<String> values) {
            addCriterion("logo_path in", values, "logoPath");
            return (Criteria) this;
        }

        public Criteria andLogoPathNotIn(List<String> values) {
            addCriterion("logo_path not in", values, "logoPath");
            return (Criteria) this;
        }

        public Criteria andLogoPathBetween(String value1, String value2) {
            addCriterion("logo_path between", value1, value2, "logoPath");
            return (Criteria) this;
        }

        public Criteria andLogoPathNotBetween(String value1, String value2) {
            addCriterion("logo_path not between", value1, value2, "logoPath");
            return (Criteria) this;
        }
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table moe_company
     *
     * @mbg.generated do_not_delete_during_merge
     */
    public static class Criteria extends GeneratedCriteria {
        protected Criteria() {
            super();
        }
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table moe_company
     *
     * @mbg.generated
     */
    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}
