package com.moego.svc.organization.entity;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

public class MoeBusinessPayrollSettingExample {
    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table moe_business_payroll_setting
     *
     * @mbg.generated
     */
    protected String orderByClause;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table moe_business_payroll_setting
     *
     * @mbg.generated
     */
    protected boolean distinct;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table moe_business_payroll_setting
     *
     * @mbg.generated
     */
    protected List<Criteria> oredCriteria;

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_business_payroll_setting
     *
     * @mbg.generated
     */
    public MoeBusinessPayrollSettingExample() {
        oredCriteria = new ArrayList<>();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_business_payroll_setting
     *
     * @mbg.generated
     */
    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_business_payroll_setting
     *
     * @mbg.generated
     */
    public String getOrderByClause() {
        return orderByClause;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_business_payroll_setting
     *
     * @mbg.generated
     */
    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_business_payroll_setting
     *
     * @mbg.generated
     */
    public boolean isDistinct() {
        return distinct;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_business_payroll_setting
     *
     * @mbg.generated
     */
    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_business_payroll_setting
     *
     * @mbg.generated
     */
    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_business_payroll_setting
     *
     * @mbg.generated
     */
    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_business_payroll_setting
     *
     * @mbg.generated
     */
    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_business_payroll_setting
     *
     * @mbg.generated
     */
    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_business_payroll_setting
     *
     * @mbg.generated
     */
    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table moe_business_payroll_setting
     *
     * @mbg.generated
     */
    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Integer value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Integer value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Integer value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Integer value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Integer value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Integer> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Integer> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Integer value1, Integer value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Integer value1, Integer value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andBusinessIdIsNull() {
            addCriterion("business_id is null");
            return (Criteria) this;
        }

        public Criteria andBusinessIdIsNotNull() {
            addCriterion("business_id is not null");
            return (Criteria) this;
        }

        public Criteria andBusinessIdEqualTo(Integer value) {
            addCriterion("business_id =", value, "businessId");
            return (Criteria) this;
        }

        public Criteria andBusinessIdNotEqualTo(Integer value) {
            addCriterion("business_id <>", value, "businessId");
            return (Criteria) this;
        }

        public Criteria andBusinessIdGreaterThan(Integer value) {
            addCriterion("business_id >", value, "businessId");
            return (Criteria) this;
        }

        public Criteria andBusinessIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("business_id >=", value, "businessId");
            return (Criteria) this;
        }

        public Criteria andBusinessIdLessThan(Integer value) {
            addCriterion("business_id <", value, "businessId");
            return (Criteria) this;
        }

        public Criteria andBusinessIdLessThanOrEqualTo(Integer value) {
            addCriterion("business_id <=", value, "businessId");
            return (Criteria) this;
        }

        public Criteria andBusinessIdIn(List<Integer> values) {
            addCriterion("business_id in", values, "businessId");
            return (Criteria) this;
        }

        public Criteria andBusinessIdNotIn(List<Integer> values) {
            addCriterion("business_id not in", values, "businessId");
            return (Criteria) this;
        }

        public Criteria andBusinessIdBetween(Integer value1, Integer value2) {
            addCriterion("business_id between", value1, value2, "businessId");
            return (Criteria) this;
        }

        public Criteria andBusinessIdNotBetween(Integer value1, Integer value2) {
            addCriterion("business_id not between", value1, value2, "businessId");
            return (Criteria) this;
        }

        public Criteria andSplitTipsMethodIsNull() {
            addCriterion("split_tips_method is null");
            return (Criteria) this;
        }

        public Criteria andSplitTipsMethodIsNotNull() {
            addCriterion("split_tips_method is not null");
            return (Criteria) this;
        }

        public Criteria andSplitTipsMethodEqualTo(Byte value) {
            addCriterion("split_tips_method =", value, "splitTipsMethod");
            return (Criteria) this;
        }

        public Criteria andSplitTipsMethodNotEqualTo(Byte value) {
            addCriterion("split_tips_method <>", value, "splitTipsMethod");
            return (Criteria) this;
        }

        public Criteria andSplitTipsMethodGreaterThan(Byte value) {
            addCriterion("split_tips_method >", value, "splitTipsMethod");
            return (Criteria) this;
        }

        public Criteria andSplitTipsMethodGreaterThanOrEqualTo(Byte value) {
            addCriterion("split_tips_method >=", value, "splitTipsMethod");
            return (Criteria) this;
        }

        public Criteria andSplitTipsMethodLessThan(Byte value) {
            addCriterion("split_tips_method <", value, "splitTipsMethod");
            return (Criteria) this;
        }

        public Criteria andSplitTipsMethodLessThanOrEqualTo(Byte value) {
            addCriterion("split_tips_method <=", value, "splitTipsMethod");
            return (Criteria) this;
        }

        public Criteria andSplitTipsMethodIn(List<Byte> values) {
            addCriterion("split_tips_method in", values, "splitTipsMethod");
            return (Criteria) this;
        }

        public Criteria andSplitTipsMethodNotIn(List<Byte> values) {
            addCriterion("split_tips_method not in", values, "splitTipsMethod");
            return (Criteria) this;
        }

        public Criteria andSplitTipsMethodBetween(Byte value1, Byte value2) {
            addCriterion("split_tips_method between", value1, value2, "splitTipsMethod");
            return (Criteria) this;
        }

        public Criteria andSplitTipsMethodNotBetween(Byte value1, Byte value2) {
            addCriterion("split_tips_method not between", value1, value2, "splitTipsMethod");
            return (Criteria) this;
        }

        public Criteria andNewPayrollEnableIsNull() {
            addCriterion("new_payroll_enable is null");
            return (Criteria) this;
        }

        public Criteria andNewPayrollEnableIsNotNull() {
            addCriterion("new_payroll_enable is not null");
            return (Criteria) this;
        }

        public Criteria andNewPayrollEnableEqualTo(Boolean value) {
            addCriterion("new_payroll_enable =", value, "newPayrollEnable");
            return (Criteria) this;
        }

        public Criteria andNewPayrollEnableNotEqualTo(Boolean value) {
            addCriterion("new_payroll_enable <>", value, "newPayrollEnable");
            return (Criteria) this;
        }

        public Criteria andNewPayrollEnableGreaterThan(Boolean value) {
            addCriterion("new_payroll_enable >", value, "newPayrollEnable");
            return (Criteria) this;
        }

        public Criteria andNewPayrollEnableGreaterThanOrEqualTo(Boolean value) {
            addCriterion("new_payroll_enable >=", value, "newPayrollEnable");
            return (Criteria) this;
        }

        public Criteria andNewPayrollEnableLessThan(Boolean value) {
            addCriterion("new_payroll_enable <", value, "newPayrollEnable");
            return (Criteria) this;
        }

        public Criteria andNewPayrollEnableLessThanOrEqualTo(Boolean value) {
            addCriterion("new_payroll_enable <=", value, "newPayrollEnable");
            return (Criteria) this;
        }

        public Criteria andNewPayrollEnableIn(List<Boolean> values) {
            addCriterion("new_payroll_enable in", values, "newPayrollEnable");
            return (Criteria) this;
        }

        public Criteria andNewPayrollEnableNotIn(List<Boolean> values) {
            addCriterion("new_payroll_enable not in", values, "newPayrollEnable");
            return (Criteria) this;
        }

        public Criteria andNewPayrollEnableBetween(Boolean value1, Boolean value2) {
            addCriterion("new_payroll_enable between", value1, value2, "newPayrollEnable");
            return (Criteria) this;
        }

        public Criteria andNewPayrollEnableNotBetween(Boolean value1, Boolean value2) {
            addCriterion("new_payroll_enable not between", value1, value2, "newPayrollEnable");
            return (Criteria) this;
        }

        public Criteria andServiceCommissionBasedIsNull() {
            addCriterion("service_commission_based is null");
            return (Criteria) this;
        }

        public Criteria andServiceCommissionBasedIsNotNull() {
            addCriterion("service_commission_based is not null");
            return (Criteria) this;
        }

        public Criteria andServiceCommissionBasedEqualTo(Byte value) {
            addCriterion("service_commission_based =", value, "serviceCommissionBased");
            return (Criteria) this;
        }

        public Criteria andServiceCommissionBasedNotEqualTo(Byte value) {
            addCriterion("service_commission_based <>", value, "serviceCommissionBased");
            return (Criteria) this;
        }

        public Criteria andServiceCommissionBasedGreaterThan(Byte value) {
            addCriterion("service_commission_based >", value, "serviceCommissionBased");
            return (Criteria) this;
        }

        public Criteria andServiceCommissionBasedGreaterThanOrEqualTo(Byte value) {
            addCriterion("service_commission_based >=", value, "serviceCommissionBased");
            return (Criteria) this;
        }

        public Criteria andServiceCommissionBasedLessThan(Byte value) {
            addCriterion("service_commission_based <", value, "serviceCommissionBased");
            return (Criteria) this;
        }

        public Criteria andServiceCommissionBasedLessThanOrEqualTo(Byte value) {
            addCriterion("service_commission_based <=", value, "serviceCommissionBased");
            return (Criteria) this;
        }

        public Criteria andServiceCommissionBasedIn(List<Byte> values) {
            addCriterion("service_commission_based in", values, "serviceCommissionBased");
            return (Criteria) this;
        }

        public Criteria andServiceCommissionBasedNotIn(List<Byte> values) {
            addCriterion("service_commission_based not in", values, "serviceCommissionBased");
            return (Criteria) this;
        }

        public Criteria andServiceCommissionBasedBetween(Byte value1, Byte value2) {
            addCriterion("service_commission_based between", value1, value2, "serviceCommissionBased");
            return (Criteria) this;
        }

        public Criteria andServiceCommissionBasedNotBetween(Byte value1, Byte value2) {
            addCriterion("service_commission_based not between", value1, value2, "serviceCommissionBased");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNull() {
            addCriterion("create_time is null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNotNull() {
            addCriterion("create_time is not null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeEqualTo(LocalDateTime value) {
            addCriterion("create_time =", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotEqualTo(LocalDateTime value) {
            addCriterion("create_time <>", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThan(LocalDateTime value) {
            addCriterion("create_time >", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThanOrEqualTo(LocalDateTime value) {
            addCriterion("create_time >=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThan(LocalDateTime value) {
            addCriterion("create_time <", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThanOrEqualTo(LocalDateTime value) {
            addCriterion("create_time <=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIn(List<LocalDateTime> values) {
            addCriterion("create_time in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotIn(List<LocalDateTime> values) {
            addCriterion("create_time not in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeBetween(LocalDateTime value1, LocalDateTime value2) {
            addCriterion("create_time between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotBetween(LocalDateTime value1, LocalDateTime value2) {
            addCriterion("create_time not between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNull() {
            addCriterion("update_time is null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNotNull() {
            addCriterion("update_time is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeEqualTo(LocalDateTime value) {
            addCriterion("update_time =", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotEqualTo(LocalDateTime value) {
            addCriterion("update_time <>", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThan(LocalDateTime value) {
            addCriterion("update_time >", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThanOrEqualTo(LocalDateTime value) {
            addCriterion("update_time >=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThan(LocalDateTime value) {
            addCriterion("update_time <", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThanOrEqualTo(LocalDateTime value) {
            addCriterion("update_time <=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIn(List<LocalDateTime> values) {
            addCriterion("update_time in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotIn(List<LocalDateTime> values) {
            addCriterion("update_time not in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeBetween(LocalDateTime value1, LocalDateTime value2) {
            addCriterion("update_time between", value1, value2, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotBetween(LocalDateTime value1, LocalDateTime value2) {
            addCriterion("update_time not between", value1, value2, "updateTime");
            return (Criteria) this;
        }

        public Criteria andCompanyIdIsNull() {
            addCriterion("company_id is null");
            return (Criteria) this;
        }

        public Criteria andCompanyIdIsNotNull() {
            addCriterion("company_id is not null");
            return (Criteria) this;
        }

        public Criteria andCompanyIdEqualTo(Long value) {
            addCriterion("company_id =", value, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdNotEqualTo(Long value) {
            addCriterion("company_id <>", value, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdGreaterThan(Long value) {
            addCriterion("company_id >", value, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdGreaterThanOrEqualTo(Long value) {
            addCriterion("company_id >=", value, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdLessThan(Long value) {
            addCriterion("company_id <", value, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdLessThanOrEqualTo(Long value) {
            addCriterion("company_id <=", value, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdIn(List<Long> values) {
            addCriterion("company_id in", values, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdNotIn(List<Long> values) {
            addCriterion("company_id not in", values, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdBetween(Long value1, Long value2) {
            addCriterion("company_id between", value1, value2, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdNotBetween(Long value1, Long value2) {
            addCriterion("company_id not between", value1, value2, "companyId");
            return (Criteria) this;
        }
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table moe_business_payroll_setting
     *
     * @mbg.generated do_not_delete_during_merge
     */
    public static class Criteria extends GeneratedCriteria {
        protected Criteria() {
            super();
        }
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table moe_business_payroll_setting
     *
     * @mbg.generated
     */
    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}
