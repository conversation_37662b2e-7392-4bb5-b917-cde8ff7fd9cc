package com.moego.svc.organization.entity;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

public class MoeStaffPayrollSettingExample {
    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table moe_staff_payroll_setting
     *
     * @mbg.generated
     */
    protected String orderByClause;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table moe_staff_payroll_setting
     *
     * @mbg.generated
     */
    protected boolean distinct;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table moe_staff_payroll_setting
     *
     * @mbg.generated
     */
    protected List<Criteria> oredCriteria;

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_staff_payroll_setting
     *
     * @mbg.generated
     */
    public MoeStaffPayrollSettingExample() {
        oredCriteria = new ArrayList<>();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_staff_payroll_setting
     *
     * @mbg.generated
     */
    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_staff_payroll_setting
     *
     * @mbg.generated
     */
    public String getOrderByClause() {
        return orderByClause;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_staff_payroll_setting
     *
     * @mbg.generated
     */
    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_staff_payroll_setting
     *
     * @mbg.generated
     */
    public boolean isDistinct() {
        return distinct;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_staff_payroll_setting
     *
     * @mbg.generated
     */
    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_staff_payroll_setting
     *
     * @mbg.generated
     */
    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_staff_payroll_setting
     *
     * @mbg.generated
     */
    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_staff_payroll_setting
     *
     * @mbg.generated
     */
    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_staff_payroll_setting
     *
     * @mbg.generated
     */
    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_staff_payroll_setting
     *
     * @mbg.generated
     */
    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table moe_staff_payroll_setting
     *
     * @mbg.generated
     */
    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Integer value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Integer value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Integer value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Integer value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Integer value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Integer> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Integer> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Integer value1, Integer value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Integer value1, Integer value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andBusinessIdIsNull() {
            addCriterion("business_id is null");
            return (Criteria) this;
        }

        public Criteria andBusinessIdIsNotNull() {
            addCriterion("business_id is not null");
            return (Criteria) this;
        }

        public Criteria andBusinessIdEqualTo(Integer value) {
            addCriterion("business_id =", value, "businessId");
            return (Criteria) this;
        }

        public Criteria andBusinessIdNotEqualTo(Integer value) {
            addCriterion("business_id <>", value, "businessId");
            return (Criteria) this;
        }

        public Criteria andBusinessIdGreaterThan(Integer value) {
            addCriterion("business_id >", value, "businessId");
            return (Criteria) this;
        }

        public Criteria andBusinessIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("business_id >=", value, "businessId");
            return (Criteria) this;
        }

        public Criteria andBusinessIdLessThan(Integer value) {
            addCriterion("business_id <", value, "businessId");
            return (Criteria) this;
        }

        public Criteria andBusinessIdLessThanOrEqualTo(Integer value) {
            addCriterion("business_id <=", value, "businessId");
            return (Criteria) this;
        }

        public Criteria andBusinessIdIn(List<Integer> values) {
            addCriterion("business_id in", values, "businessId");
            return (Criteria) this;
        }

        public Criteria andBusinessIdNotIn(List<Integer> values) {
            addCriterion("business_id not in", values, "businessId");
            return (Criteria) this;
        }

        public Criteria andBusinessIdBetween(Integer value1, Integer value2) {
            addCriterion("business_id between", value1, value2, "businessId");
            return (Criteria) this;
        }

        public Criteria andBusinessIdNotBetween(Integer value1, Integer value2) {
            addCriterion("business_id not between", value1, value2, "businessId");
            return (Criteria) this;
        }

        public Criteria andStaffIdIsNull() {
            addCriterion("staff_id is null");
            return (Criteria) this;
        }

        public Criteria andStaffIdIsNotNull() {
            addCriterion("staff_id is not null");
            return (Criteria) this;
        }

        public Criteria andStaffIdEqualTo(Integer value) {
            addCriterion("staff_id =", value, "staffId");
            return (Criteria) this;
        }

        public Criteria andStaffIdNotEqualTo(Integer value) {
            addCriterion("staff_id <>", value, "staffId");
            return (Criteria) this;
        }

        public Criteria andStaffIdGreaterThan(Integer value) {
            addCriterion("staff_id >", value, "staffId");
            return (Criteria) this;
        }

        public Criteria andStaffIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("staff_id >=", value, "staffId");
            return (Criteria) this;
        }

        public Criteria andStaffIdLessThan(Integer value) {
            addCriterion("staff_id <", value, "staffId");
            return (Criteria) this;
        }

        public Criteria andStaffIdLessThanOrEqualTo(Integer value) {
            addCriterion("staff_id <=", value, "staffId");
            return (Criteria) this;
        }

        public Criteria andStaffIdIn(List<Integer> values) {
            addCriterion("staff_id in", values, "staffId");
            return (Criteria) this;
        }

        public Criteria andStaffIdNotIn(List<Integer> values) {
            addCriterion("staff_id not in", values, "staffId");
            return (Criteria) this;
        }

        public Criteria andStaffIdBetween(Integer value1, Integer value2) {
            addCriterion("staff_id between", value1, value2, "staffId");
            return (Criteria) this;
        }

        public Criteria andStaffIdNotBetween(Integer value1, Integer value2) {
            addCriterion("staff_id not between", value1, value2, "staffId");
            return (Criteria) this;
        }

        public Criteria andServiceCommissionEnableIsNull() {
            addCriterion("service_commission_enable is null");
            return (Criteria) this;
        }

        public Criteria andServiceCommissionEnableIsNotNull() {
            addCriterion("service_commission_enable is not null");
            return (Criteria) this;
        }

        public Criteria andServiceCommissionEnableEqualTo(Boolean value) {
            addCriterion("service_commission_enable =", value, "serviceCommissionEnable");
            return (Criteria) this;
        }

        public Criteria andServiceCommissionEnableNotEqualTo(Boolean value) {
            addCriterion("service_commission_enable <>", value, "serviceCommissionEnable");
            return (Criteria) this;
        }

        public Criteria andServiceCommissionEnableGreaterThan(Boolean value) {
            addCriterion("service_commission_enable >", value, "serviceCommissionEnable");
            return (Criteria) this;
        }

        public Criteria andServiceCommissionEnableGreaterThanOrEqualTo(Boolean value) {
            addCriterion("service_commission_enable >=", value, "serviceCommissionEnable");
            return (Criteria) this;
        }

        public Criteria andServiceCommissionEnableLessThan(Boolean value) {
            addCriterion("service_commission_enable <", value, "serviceCommissionEnable");
            return (Criteria) this;
        }

        public Criteria andServiceCommissionEnableLessThanOrEqualTo(Boolean value) {
            addCriterion("service_commission_enable <=", value, "serviceCommissionEnable");
            return (Criteria) this;
        }

        public Criteria andServiceCommissionEnableIn(List<Boolean> values) {
            addCriterion("service_commission_enable in", values, "serviceCommissionEnable");
            return (Criteria) this;
        }

        public Criteria andServiceCommissionEnableNotIn(List<Boolean> values) {
            addCriterion("service_commission_enable not in", values, "serviceCommissionEnable");
            return (Criteria) this;
        }

        public Criteria andServiceCommissionEnableBetween(Boolean value1, Boolean value2) {
            addCriterion("service_commission_enable between", value1, value2, "serviceCommissionEnable");
            return (Criteria) this;
        }

        public Criteria andServiceCommissionEnableNotBetween(Boolean value1, Boolean value2) {
            addCriterion("service_commission_enable not between", value1, value2, "serviceCommissionEnable");
            return (Criteria) this;
        }

        public Criteria andServiceCommissionTypeIsNull() {
            addCriterion("service_commission_type is null");
            return (Criteria) this;
        }

        public Criteria andServiceCommissionTypeIsNotNull() {
            addCriterion("service_commission_type is not null");
            return (Criteria) this;
        }

        public Criteria andServiceCommissionTypeEqualTo(Byte value) {
            addCriterion("service_commission_type =", value, "serviceCommissionType");
            return (Criteria) this;
        }

        public Criteria andServiceCommissionTypeNotEqualTo(Byte value) {
            addCriterion("service_commission_type <>", value, "serviceCommissionType");
            return (Criteria) this;
        }

        public Criteria andServiceCommissionTypeGreaterThan(Byte value) {
            addCriterion("service_commission_type >", value, "serviceCommissionType");
            return (Criteria) this;
        }

        public Criteria andServiceCommissionTypeGreaterThanOrEqualTo(Byte value) {
            addCriterion("service_commission_type >=", value, "serviceCommissionType");
            return (Criteria) this;
        }

        public Criteria andServiceCommissionTypeLessThan(Byte value) {
            addCriterion("service_commission_type <", value, "serviceCommissionType");
            return (Criteria) this;
        }

        public Criteria andServiceCommissionTypeLessThanOrEqualTo(Byte value) {
            addCriterion("service_commission_type <=", value, "serviceCommissionType");
            return (Criteria) this;
        }

        public Criteria andServiceCommissionTypeIn(List<Byte> values) {
            addCriterion("service_commission_type in", values, "serviceCommissionType");
            return (Criteria) this;
        }

        public Criteria andServiceCommissionTypeNotIn(List<Byte> values) {
            addCriterion("service_commission_type not in", values, "serviceCommissionType");
            return (Criteria) this;
        }

        public Criteria andServiceCommissionTypeBetween(Byte value1, Byte value2) {
            addCriterion("service_commission_type between", value1, value2, "serviceCommissionType");
            return (Criteria) this;
        }

        public Criteria andServiceCommissionTypeNotBetween(Byte value1, Byte value2) {
            addCriterion("service_commission_type not between", value1, value2, "serviceCommissionType");
            return (Criteria) this;
        }

        public Criteria andTierTypeIsNull() {
            addCriterion("tier_type is null");
            return (Criteria) this;
        }

        public Criteria andTierTypeIsNotNull() {
            addCriterion("tier_type is not null");
            return (Criteria) this;
        }

        public Criteria andTierTypeEqualTo(Byte value) {
            addCriterion("tier_type =", value, "tierType");
            return (Criteria) this;
        }

        public Criteria andTierTypeNotEqualTo(Byte value) {
            addCriterion("tier_type <>", value, "tierType");
            return (Criteria) this;
        }

        public Criteria andTierTypeGreaterThan(Byte value) {
            addCriterion("tier_type >", value, "tierType");
            return (Criteria) this;
        }

        public Criteria andTierTypeGreaterThanOrEqualTo(Byte value) {
            addCriterion("tier_type >=", value, "tierType");
            return (Criteria) this;
        }

        public Criteria andTierTypeLessThan(Byte value) {
            addCriterion("tier_type <", value, "tierType");
            return (Criteria) this;
        }

        public Criteria andTierTypeLessThanOrEqualTo(Byte value) {
            addCriterion("tier_type <=", value, "tierType");
            return (Criteria) this;
        }

        public Criteria andTierTypeIn(List<Byte> values) {
            addCriterion("tier_type in", values, "tierType");
            return (Criteria) this;
        }

        public Criteria andTierTypeNotIn(List<Byte> values) {
            addCriterion("tier_type not in", values, "tierType");
            return (Criteria) this;
        }

        public Criteria andTierTypeBetween(Byte value1, Byte value2) {
            addCriterion("tier_type between", value1, value2, "tierType");
            return (Criteria) this;
        }

        public Criteria andTierTypeNotBetween(Byte value1, Byte value2) {
            addCriterion("tier_type not between", value1, value2, "tierType");
            return (Criteria) this;
        }

        public Criteria andServicePayRateIsNull() {
            addCriterion("service_pay_rate is null");
            return (Criteria) this;
        }

        public Criteria andServicePayRateIsNotNull() {
            addCriterion("service_pay_rate is not null");
            return (Criteria) this;
        }

        public Criteria andServicePayRateEqualTo(BigDecimal value) {
            addCriterion("service_pay_rate =", value, "servicePayRate");
            return (Criteria) this;
        }

        public Criteria andServicePayRateNotEqualTo(BigDecimal value) {
            addCriterion("service_pay_rate <>", value, "servicePayRate");
            return (Criteria) this;
        }

        public Criteria andServicePayRateGreaterThan(BigDecimal value) {
            addCriterion("service_pay_rate >", value, "servicePayRate");
            return (Criteria) this;
        }

        public Criteria andServicePayRateGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("service_pay_rate >=", value, "servicePayRate");
            return (Criteria) this;
        }

        public Criteria andServicePayRateLessThan(BigDecimal value) {
            addCriterion("service_pay_rate <", value, "servicePayRate");
            return (Criteria) this;
        }

        public Criteria andServicePayRateLessThanOrEqualTo(BigDecimal value) {
            addCriterion("service_pay_rate <=", value, "servicePayRate");
            return (Criteria) this;
        }

        public Criteria andServicePayRateIn(List<BigDecimal> values) {
            addCriterion("service_pay_rate in", values, "servicePayRate");
            return (Criteria) this;
        }

        public Criteria andServicePayRateNotIn(List<BigDecimal> values) {
            addCriterion("service_pay_rate not in", values, "servicePayRate");
            return (Criteria) this;
        }

        public Criteria andServicePayRateBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("service_pay_rate between", value1, value2, "servicePayRate");
            return (Criteria) this;
        }

        public Criteria andServicePayRateNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("service_pay_rate not between", value1, value2, "servicePayRate");
            return (Criteria) this;
        }

        public Criteria andAddonPayRateIsNull() {
            addCriterion("addon_pay_rate is null");
            return (Criteria) this;
        }

        public Criteria andAddonPayRateIsNotNull() {
            addCriterion("addon_pay_rate is not null");
            return (Criteria) this;
        }

        public Criteria andAddonPayRateEqualTo(BigDecimal value) {
            addCriterion("addon_pay_rate =", value, "addonPayRate");
            return (Criteria) this;
        }

        public Criteria andAddonPayRateNotEqualTo(BigDecimal value) {
            addCriterion("addon_pay_rate <>", value, "addonPayRate");
            return (Criteria) this;
        }

        public Criteria andAddonPayRateGreaterThan(BigDecimal value) {
            addCriterion("addon_pay_rate >", value, "addonPayRate");
            return (Criteria) this;
        }

        public Criteria andAddonPayRateGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("addon_pay_rate >=", value, "addonPayRate");
            return (Criteria) this;
        }

        public Criteria andAddonPayRateLessThan(BigDecimal value) {
            addCriterion("addon_pay_rate <", value, "addonPayRate");
            return (Criteria) this;
        }

        public Criteria andAddonPayRateLessThanOrEqualTo(BigDecimal value) {
            addCriterion("addon_pay_rate <=", value, "addonPayRate");
            return (Criteria) this;
        }

        public Criteria andAddonPayRateIn(List<BigDecimal> values) {
            addCriterion("addon_pay_rate in", values, "addonPayRate");
            return (Criteria) this;
        }

        public Criteria andAddonPayRateNotIn(List<BigDecimal> values) {
            addCriterion("addon_pay_rate not in", values, "addonPayRate");
            return (Criteria) this;
        }

        public Criteria andAddonPayRateBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("addon_pay_rate between", value1, value2, "addonPayRate");
            return (Criteria) this;
        }

        public Criteria andAddonPayRateNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("addon_pay_rate not between", value1, value2, "addonPayRate");
            return (Criteria) this;
        }

        public Criteria andHourlyCommissionEnableIsNull() {
            addCriterion("hourly_commission_enable is null");
            return (Criteria) this;
        }

        public Criteria andHourlyCommissionEnableIsNotNull() {
            addCriterion("hourly_commission_enable is not null");
            return (Criteria) this;
        }

        public Criteria andHourlyCommissionEnableEqualTo(Boolean value) {
            addCriterion("hourly_commission_enable =", value, "hourlyCommissionEnable");
            return (Criteria) this;
        }

        public Criteria andHourlyCommissionEnableNotEqualTo(Boolean value) {
            addCriterion("hourly_commission_enable <>", value, "hourlyCommissionEnable");
            return (Criteria) this;
        }

        public Criteria andHourlyCommissionEnableGreaterThan(Boolean value) {
            addCriterion("hourly_commission_enable >", value, "hourlyCommissionEnable");
            return (Criteria) this;
        }

        public Criteria andHourlyCommissionEnableGreaterThanOrEqualTo(Boolean value) {
            addCriterion("hourly_commission_enable >=", value, "hourlyCommissionEnable");
            return (Criteria) this;
        }

        public Criteria andHourlyCommissionEnableLessThan(Boolean value) {
            addCriterion("hourly_commission_enable <", value, "hourlyCommissionEnable");
            return (Criteria) this;
        }

        public Criteria andHourlyCommissionEnableLessThanOrEqualTo(Boolean value) {
            addCriterion("hourly_commission_enable <=", value, "hourlyCommissionEnable");
            return (Criteria) this;
        }

        public Criteria andHourlyCommissionEnableIn(List<Boolean> values) {
            addCriterion("hourly_commission_enable in", values, "hourlyCommissionEnable");
            return (Criteria) this;
        }

        public Criteria andHourlyCommissionEnableNotIn(List<Boolean> values) {
            addCriterion("hourly_commission_enable not in", values, "hourlyCommissionEnable");
            return (Criteria) this;
        }

        public Criteria andHourlyCommissionEnableBetween(Boolean value1, Boolean value2) {
            addCriterion("hourly_commission_enable between", value1, value2, "hourlyCommissionEnable");
            return (Criteria) this;
        }

        public Criteria andHourlyCommissionEnableNotBetween(Boolean value1, Boolean value2) {
            addCriterion("hourly_commission_enable not between", value1, value2, "hourlyCommissionEnable");
            return (Criteria) this;
        }

        public Criteria andHourlyPayIsNull() {
            addCriterion("hourly_pay is null");
            return (Criteria) this;
        }

        public Criteria andHourlyPayIsNotNull() {
            addCriterion("hourly_pay is not null");
            return (Criteria) this;
        }

        public Criteria andHourlyPayEqualTo(BigDecimal value) {
            addCriterion("hourly_pay =", value, "hourlyPay");
            return (Criteria) this;
        }

        public Criteria andHourlyPayNotEqualTo(BigDecimal value) {
            addCriterion("hourly_pay <>", value, "hourlyPay");
            return (Criteria) this;
        }

        public Criteria andHourlyPayGreaterThan(BigDecimal value) {
            addCriterion("hourly_pay >", value, "hourlyPay");
            return (Criteria) this;
        }

        public Criteria andHourlyPayGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("hourly_pay >=", value, "hourlyPay");
            return (Criteria) this;
        }

        public Criteria andHourlyPayLessThan(BigDecimal value) {
            addCriterion("hourly_pay <", value, "hourlyPay");
            return (Criteria) this;
        }

        public Criteria andHourlyPayLessThanOrEqualTo(BigDecimal value) {
            addCriterion("hourly_pay <=", value, "hourlyPay");
            return (Criteria) this;
        }

        public Criteria andHourlyPayIn(List<BigDecimal> values) {
            addCriterion("hourly_pay in", values, "hourlyPay");
            return (Criteria) this;
        }

        public Criteria andHourlyPayNotIn(List<BigDecimal> values) {
            addCriterion("hourly_pay not in", values, "hourlyPay");
            return (Criteria) this;
        }

        public Criteria andHourlyPayBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("hourly_pay between", value1, value2, "hourlyPay");
            return (Criteria) this;
        }

        public Criteria andHourlyPayNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("hourly_pay not between", value1, value2, "hourlyPay");
            return (Criteria) this;
        }

        public Criteria andTipsCommissionEnableIsNull() {
            addCriterion("tips_commission_enable is null");
            return (Criteria) this;
        }

        public Criteria andTipsCommissionEnableIsNotNull() {
            addCriterion("tips_commission_enable is not null");
            return (Criteria) this;
        }

        public Criteria andTipsCommissionEnableEqualTo(Boolean value) {
            addCriterion("tips_commission_enable =", value, "tipsCommissionEnable");
            return (Criteria) this;
        }

        public Criteria andTipsCommissionEnableNotEqualTo(Boolean value) {
            addCriterion("tips_commission_enable <>", value, "tipsCommissionEnable");
            return (Criteria) this;
        }

        public Criteria andTipsCommissionEnableGreaterThan(Boolean value) {
            addCriterion("tips_commission_enable >", value, "tipsCommissionEnable");
            return (Criteria) this;
        }

        public Criteria andTipsCommissionEnableGreaterThanOrEqualTo(Boolean value) {
            addCriterion("tips_commission_enable >=", value, "tipsCommissionEnable");
            return (Criteria) this;
        }

        public Criteria andTipsCommissionEnableLessThan(Boolean value) {
            addCriterion("tips_commission_enable <", value, "tipsCommissionEnable");
            return (Criteria) this;
        }

        public Criteria andTipsCommissionEnableLessThanOrEqualTo(Boolean value) {
            addCriterion("tips_commission_enable <=", value, "tipsCommissionEnable");
            return (Criteria) this;
        }

        public Criteria andTipsCommissionEnableIn(List<Boolean> values) {
            addCriterion("tips_commission_enable in", values, "tipsCommissionEnable");
            return (Criteria) this;
        }

        public Criteria andTipsCommissionEnableNotIn(List<Boolean> values) {
            addCriterion("tips_commission_enable not in", values, "tipsCommissionEnable");
            return (Criteria) this;
        }

        public Criteria andTipsCommissionEnableBetween(Boolean value1, Boolean value2) {
            addCriterion("tips_commission_enable between", value1, value2, "tipsCommissionEnable");
            return (Criteria) this;
        }

        public Criteria andTipsCommissionEnableNotBetween(Boolean value1, Boolean value2) {
            addCriterion("tips_commission_enable not between", value1, value2, "tipsCommissionEnable");
            return (Criteria) this;
        }

        public Criteria andTipsPayRateIsNull() {
            addCriterion("tips_pay_rate is null");
            return (Criteria) this;
        }

        public Criteria andTipsPayRateIsNotNull() {
            addCriterion("tips_pay_rate is not null");
            return (Criteria) this;
        }

        public Criteria andTipsPayRateEqualTo(BigDecimal value) {
            addCriterion("tips_pay_rate =", value, "tipsPayRate");
            return (Criteria) this;
        }

        public Criteria andTipsPayRateNotEqualTo(BigDecimal value) {
            addCriterion("tips_pay_rate <>", value, "tipsPayRate");
            return (Criteria) this;
        }

        public Criteria andTipsPayRateGreaterThan(BigDecimal value) {
            addCriterion("tips_pay_rate >", value, "tipsPayRate");
            return (Criteria) this;
        }

        public Criteria andTipsPayRateGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("tips_pay_rate >=", value, "tipsPayRate");
            return (Criteria) this;
        }

        public Criteria andTipsPayRateLessThan(BigDecimal value) {
            addCriterion("tips_pay_rate <", value, "tipsPayRate");
            return (Criteria) this;
        }

        public Criteria andTipsPayRateLessThanOrEqualTo(BigDecimal value) {
            addCriterion("tips_pay_rate <=", value, "tipsPayRate");
            return (Criteria) this;
        }

        public Criteria andTipsPayRateIn(List<BigDecimal> values) {
            addCriterion("tips_pay_rate in", values, "tipsPayRate");
            return (Criteria) this;
        }

        public Criteria andTipsPayRateNotIn(List<BigDecimal> values) {
            addCriterion("tips_pay_rate not in", values, "tipsPayRate");
            return (Criteria) this;
        }

        public Criteria andTipsPayRateBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("tips_pay_rate between", value1, value2, "tipsPayRate");
            return (Criteria) this;
        }

        public Criteria andTipsPayRateNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("tips_pay_rate not between", value1, value2, "tipsPayRate");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNull() {
            addCriterion("create_time is null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNotNull() {
            addCriterion("create_time is not null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeEqualTo(LocalDateTime value) {
            addCriterion("create_time =", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotEqualTo(LocalDateTime value) {
            addCriterion("create_time <>", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThan(LocalDateTime value) {
            addCriterion("create_time >", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThanOrEqualTo(LocalDateTime value) {
            addCriterion("create_time >=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThan(LocalDateTime value) {
            addCriterion("create_time <", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThanOrEqualTo(LocalDateTime value) {
            addCriterion("create_time <=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIn(List<LocalDateTime> values) {
            addCriterion("create_time in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotIn(List<LocalDateTime> values) {
            addCriterion("create_time not in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeBetween(LocalDateTime value1, LocalDateTime value2) {
            addCriterion("create_time between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotBetween(LocalDateTime value1, LocalDateTime value2) {
            addCriterion("create_time not between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNull() {
            addCriterion("update_time is null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNotNull() {
            addCriterion("update_time is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeEqualTo(LocalDateTime value) {
            addCriterion("update_time =", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotEqualTo(LocalDateTime value) {
            addCriterion("update_time <>", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThan(LocalDateTime value) {
            addCriterion("update_time >", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThanOrEqualTo(LocalDateTime value) {
            addCriterion("update_time >=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThan(LocalDateTime value) {
            addCriterion("update_time <", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThanOrEqualTo(LocalDateTime value) {
            addCriterion("update_time <=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIn(List<LocalDateTime> values) {
            addCriterion("update_time in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotIn(List<LocalDateTime> values) {
            addCriterion("update_time not in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeBetween(LocalDateTime value1, LocalDateTime value2) {
            addCriterion("update_time between", value1, value2, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotBetween(LocalDateTime value1, LocalDateTime value2) {
            addCriterion("update_time not between", value1, value2, "updateTime");
            return (Criteria) this;
        }

        public Criteria andCompanyIdIsNull() {
            addCriterion("company_id is null");
            return (Criteria) this;
        }

        public Criteria andCompanyIdIsNotNull() {
            addCriterion("company_id is not null");
            return (Criteria) this;
        }

        public Criteria andCompanyIdEqualTo(Long value) {
            addCriterion("company_id =", value, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdNotEqualTo(Long value) {
            addCriterion("company_id <>", value, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdGreaterThan(Long value) {
            addCriterion("company_id >", value, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdGreaterThanOrEqualTo(Long value) {
            addCriterion("company_id >=", value, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdLessThan(Long value) {
            addCriterion("company_id <", value, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdLessThanOrEqualTo(Long value) {
            addCriterion("company_id <=", value, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdIn(List<Long> values) {
            addCriterion("company_id in", values, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdNotIn(List<Long> values) {
            addCriterion("company_id not in", values, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdBetween(Long value1, Long value2) {
            addCriterion("company_id between", value1, value2, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdNotBetween(Long value1, Long value2) {
            addCriterion("company_id not between", value1, value2, "companyId");
            return (Criteria) this;
        }
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table moe_staff_payroll_setting
     *
     * @mbg.generated do_not_delete_during_merge
     */
    public static class Criteria extends GeneratedCriteria {
        protected Criteria() {
            super();
        }
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table moe_staff_payroll_setting
     *
     * @mbg.generated
     */
    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}
