package com.moego.svc.organization.entity;

import java.time.LocalDateTime;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder(toBuilder = true)
@AllArgsConstructor(staticName = "")
@NoArgsConstructor(staticName = "")
public class MoeStaffWorkingHour {
    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_staff_working_hour.id
     *
     * @mbg.generated
     */
    private Integer id;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_staff_working_hour.business_id
     *
     * @mbg.generated
     */
    private Integer businessId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_staff_working_hour.staff_id
     *
     * @mbg.generated
     */
    private Integer staffId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_staff_working_hour.schedule_type
     *
     * @mbg.generated
     */
    private Byte scheduleType;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_staff_working_hour.start_date
     *
     * @mbg.generated
     */
    private String startDate;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_staff_working_hour.end_date
     *
     * @mbg.generated
     */
    private String endDate;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_staff_working_hour.create_time
     *
     * @mbg.generated
     */
    private LocalDateTime createTime;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_staff_working_hour.update_time
     *
     * @mbg.generated
     */
    private LocalDateTime updateTime;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_staff_working_hour.company_id
     *
     * @mbg.generated
     */
    private Long companyId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_staff_working_hour.first_week
     *
     * @mbg.generated
     */
    private String firstWeek;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_staff_working_hour.second_week
     *
     * @mbg.generated
     */
    private String secondWeek;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_staff_working_hour.third_week
     *
     * @mbg.generated
     */
    private String thirdWeek;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_staff_working_hour.forth_week
     *
     * @mbg.generated
     */
    private String forthWeek;
}
