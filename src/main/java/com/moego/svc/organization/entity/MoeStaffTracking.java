package com.moego.svc.organization.entity;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder(toBuilder = true)
@AllArgsConstructor(staticName = "")
@NoArgsConstructor(staticName = "")
public class MoeStaffTracking {
    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_staff_tracking.id
     *
     * @mbg.generated
     */
    private Long id;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_staff_tracking.company_id
     *
     * @mbg.generated
     */
    private Long companyId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_staff_tracking.staff_id
     *
     * @mbg.generated
     */
    private Long staffId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_staff_tracking.device_id
     *
     * @mbg.generated
     */
    private String deviceId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_staff_tracking.lat
     *
     * @mbg.generated
     */
    private BigDecimal lat;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_staff_tracking.lng
     *
     * @mbg.generated
     */
    private BigDecimal lng;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_staff_tracking.created_at
     *
     * @mbg.generated
     */
    private LocalDateTime createdAt;
}
