package com.moego.svc.organization.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder(toBuilder = true)
@AllArgsConstructor(staticName = "")
@NoArgsConstructor(staticName = "")
public class MoeCompany {
    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_company.id
     *
     * @mbg.generated
     */
    private Integer id;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_company.name
     *
     * @mbg.generated
     */
    private String name;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_company.account_id
     *
     * @mbg.generated
     */
    private Integer accountId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_company.country
     *
     * @mbg.generated
     */
    private String country;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_company.create_time
     *
     * @mbg.generated
     */
    private Long createTime;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_company.update_time
     *
     * @mbg.generated
     */
    private Long updateTime;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_company.location_num
     *
     * @mbg.generated
     */
    private Integer locationNum;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_company.staff_num
     *
     * @mbg.generated
     */
    private Integer staffNum;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_company.vans_num
     *
     * @mbg.generated
     */
    private Integer vansNum;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_company.level
     *
     * @mbg.generated
     */
    private Integer level;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_company.is_new_pricing
     *
     * @mbg.generated
     */
    private Integer isNewPricing;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_company.enable_square
     *
     * @mbg.generated
     */
    private Byte enableSquare;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_company.enable_stripe_reader
     *
     * @mbg.generated
     */
    private Byte enableStripeReader;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_company.enterprise_id
     *
     * @mbg.generated
     */
    private Integer enterpriseId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_company.currency_code
     *
     * @mbg.generated
     */
    private String currencyCode;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_company.currency_symbol
     *
     * @mbg.generated
     */
    private String currencySymbol;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_company.date_format_type
     *
     * @mbg.generated
     */
    private Byte dateFormatType;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_company.time_format_type
     *
     * @mbg.generated
     */
    private Byte timeFormatType;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_company.unit_of_weight_type
     *
     * @mbg.generated
     */
    private Byte unitOfWeightType;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_company.unit_of_distance_type
     *
     * @mbg.generated
     */
    private Byte unitOfDistanceType;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_company.notification_sound_enable
     *
     * @mbg.generated
     */
    private Boolean notificationSoundEnable;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_company.country_alpha2_code
     *
     * @mbg.generated
     */
    private String countryAlpha2Code;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_company.timezone_name
     *
     * @mbg.generated
     */
    private String timezoneName;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_company.timezone_seconds
     *
     * @mbg.generated
     */
    private Integer timezoneSeconds;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_company.source
     *
     * @mbg.generated
     */
    private Integer source;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_company.know_about_us
     *
     * @mbg.generated
     */
    private String knowAboutUs;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_company.company_type
     *
     * @mbg.generated
     */
    private Byte companyType;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_company.theme_color
     *
     * @mbg.generated
     */
    private String themeColor;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_company.logo_path
     *
     * @mbg.generated
     */
    private String logoPath;
}
