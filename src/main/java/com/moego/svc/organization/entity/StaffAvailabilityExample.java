package com.moego.svc.organization.entity;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

public class StaffAvailabilityExample {
    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table staff_availability
     *
     * @mbg.generated
     */
    protected String orderByClause;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table staff_availability
     *
     * @mbg.generated
     */
    protected boolean distinct;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table staff_availability
     *
     * @mbg.generated
     */
    protected List<Criteria> oredCriteria;

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table staff_availability
     *
     * @mbg.generated
     */
    public StaffAvailabilityExample() {
        oredCriteria = new ArrayList<>();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table staff_availability
     *
     * @mbg.generated
     */
    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table staff_availability
     *
     * @mbg.generated
     */
    public String getOrderByClause() {
        return orderByClause;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table staff_availability
     *
     * @mbg.generated
     */
    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table staff_availability
     *
     * @mbg.generated
     */
    public boolean isDistinct() {
        return distinct;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table staff_availability
     *
     * @mbg.generated
     */
    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table staff_availability
     *
     * @mbg.generated
     */
    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table staff_availability
     *
     * @mbg.generated
     */
    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table staff_availability
     *
     * @mbg.generated
     */
    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table staff_availability
     *
     * @mbg.generated
     */
    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table staff_availability
     *
     * @mbg.generated
     */
    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table staff_availability
     *
     * @mbg.generated
     */
    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andCompanyIdIsNull() {
            addCriterion("company_id is null");
            return (Criteria) this;
        }

        public Criteria andCompanyIdIsNotNull() {
            addCriterion("company_id is not null");
            return (Criteria) this;
        }

        public Criteria andCompanyIdEqualTo(Long value) {
            addCriterion("company_id =", value, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdNotEqualTo(Long value) {
            addCriterion("company_id <>", value, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdGreaterThan(Long value) {
            addCriterion("company_id >", value, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdGreaterThanOrEqualTo(Long value) {
            addCriterion("company_id >=", value, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdLessThan(Long value) {
            addCriterion("company_id <", value, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdLessThanOrEqualTo(Long value) {
            addCriterion("company_id <=", value, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdIn(List<Long> values) {
            addCriterion("company_id in", values, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdNotIn(List<Long> values) {
            addCriterion("company_id not in", values, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdBetween(Long value1, Long value2) {
            addCriterion("company_id between", value1, value2, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdNotBetween(Long value1, Long value2) {
            addCriterion("company_id not between", value1, value2, "companyId");
            return (Criteria) this;
        }

        public Criteria andBusinessIdIsNull() {
            addCriterion("business_id is null");
            return (Criteria) this;
        }

        public Criteria andBusinessIdIsNotNull() {
            addCriterion("business_id is not null");
            return (Criteria) this;
        }

        public Criteria andBusinessIdEqualTo(Long value) {
            addCriterion("business_id =", value, "businessId");
            return (Criteria) this;
        }

        public Criteria andBusinessIdNotEqualTo(Long value) {
            addCriterion("business_id <>", value, "businessId");
            return (Criteria) this;
        }

        public Criteria andBusinessIdGreaterThan(Long value) {
            addCriterion("business_id >", value, "businessId");
            return (Criteria) this;
        }

        public Criteria andBusinessIdGreaterThanOrEqualTo(Long value) {
            addCriterion("business_id >=", value, "businessId");
            return (Criteria) this;
        }

        public Criteria andBusinessIdLessThan(Long value) {
            addCriterion("business_id <", value, "businessId");
            return (Criteria) this;
        }

        public Criteria andBusinessIdLessThanOrEqualTo(Long value) {
            addCriterion("business_id <=", value, "businessId");
            return (Criteria) this;
        }

        public Criteria andBusinessIdIn(List<Long> values) {
            addCriterion("business_id in", values, "businessId");
            return (Criteria) this;
        }

        public Criteria andBusinessIdNotIn(List<Long> values) {
            addCriterion("business_id not in", values, "businessId");
            return (Criteria) this;
        }

        public Criteria andBusinessIdBetween(Long value1, Long value2) {
            addCriterion("business_id between", value1, value2, "businessId");
            return (Criteria) this;
        }

        public Criteria andBusinessIdNotBetween(Long value1, Long value2) {
            addCriterion("business_id not between", value1, value2, "businessId");
            return (Criteria) this;
        }

        public Criteria andStaffIdIsNull() {
            addCriterion("staff_id is null");
            return (Criteria) this;
        }

        public Criteria andStaffIdIsNotNull() {
            addCriterion("staff_id is not null");
            return (Criteria) this;
        }

        public Criteria andStaffIdEqualTo(Long value) {
            addCriterion("staff_id =", value, "staffId");
            return (Criteria) this;
        }

        public Criteria andStaffIdNotEqualTo(Long value) {
            addCriterion("staff_id <>", value, "staffId");
            return (Criteria) this;
        }

        public Criteria andStaffIdGreaterThan(Long value) {
            addCriterion("staff_id >", value, "staffId");
            return (Criteria) this;
        }

        public Criteria andStaffIdGreaterThanOrEqualTo(Long value) {
            addCriterion("staff_id >=", value, "staffId");
            return (Criteria) this;
        }

        public Criteria andStaffIdLessThan(Long value) {
            addCriterion("staff_id <", value, "staffId");
            return (Criteria) this;
        }

        public Criteria andStaffIdLessThanOrEqualTo(Long value) {
            addCriterion("staff_id <=", value, "staffId");
            return (Criteria) this;
        }

        public Criteria andStaffIdIn(List<Long> values) {
            addCriterion("staff_id in", values, "staffId");
            return (Criteria) this;
        }

        public Criteria andStaffIdNotIn(List<Long> values) {
            addCriterion("staff_id not in", values, "staffId");
            return (Criteria) this;
        }

        public Criteria andStaffIdBetween(Long value1, Long value2) {
            addCriterion("staff_id between", value1, value2, "staffId");
            return (Criteria) this;
        }

        public Criteria andStaffIdNotBetween(Long value1, Long value2) {
            addCriterion("staff_id not between", value1, value2, "staffId");
            return (Criteria) this;
        }

        public Criteria andSlotScheduleTypeIsNull() {
            addCriterion("slot_schedule_type is null");
            return (Criteria) this;
        }

        public Criteria andSlotScheduleTypeIsNotNull() {
            addCriterion("slot_schedule_type is not null");
            return (Criteria) this;
        }

        public Criteria andSlotScheduleTypeEqualTo(Integer value) {
            addCriterion("slot_schedule_type =", value, "slotScheduleType");
            return (Criteria) this;
        }

        public Criteria andSlotScheduleTypeNotEqualTo(Integer value) {
            addCriterion("slot_schedule_type <>", value, "slotScheduleType");
            return (Criteria) this;
        }

        public Criteria andSlotScheduleTypeGreaterThan(Integer value) {
            addCriterion("slot_schedule_type >", value, "slotScheduleType");
            return (Criteria) this;
        }

        public Criteria andSlotScheduleTypeGreaterThanOrEqualTo(Integer value) {
            addCriterion("slot_schedule_type >=", value, "slotScheduleType");
            return (Criteria) this;
        }

        public Criteria andSlotScheduleTypeLessThan(Integer value) {
            addCriterion("slot_schedule_type <", value, "slotScheduleType");
            return (Criteria) this;
        }

        public Criteria andSlotScheduleTypeLessThanOrEqualTo(Integer value) {
            addCriterion("slot_schedule_type <=", value, "slotScheduleType");
            return (Criteria) this;
        }

        public Criteria andSlotScheduleTypeIn(List<Integer> values) {
            addCriterion("slot_schedule_type in", values, "slotScheduleType");
            return (Criteria) this;
        }

        public Criteria andSlotScheduleTypeNotIn(List<Integer> values) {
            addCriterion("slot_schedule_type not in", values, "slotScheduleType");
            return (Criteria) this;
        }

        public Criteria andSlotScheduleTypeBetween(Integer value1, Integer value2) {
            addCriterion("slot_schedule_type between", value1, value2, "slotScheduleType");
            return (Criteria) this;
        }

        public Criteria andSlotScheduleTypeNotBetween(Integer value1, Integer value2) {
            addCriterion("slot_schedule_type not between", value1, value2, "slotScheduleType");
            return (Criteria) this;
        }

        public Criteria andSlotStartSundayIsNull() {
            addCriterion("slot_start_sunday is null");
            return (Criteria) this;
        }

        public Criteria andSlotStartSundayIsNotNull() {
            addCriterion("slot_start_sunday is not null");
            return (Criteria) this;
        }

        public Criteria andSlotStartSundayEqualTo(LocalDate value) {
            addCriterion("slot_start_sunday =", value, "slotStartSunday");
            return (Criteria) this;
        }

        public Criteria andSlotStartSundayNotEqualTo(LocalDate value) {
            addCriterion("slot_start_sunday <>", value, "slotStartSunday");
            return (Criteria) this;
        }

        public Criteria andSlotStartSundayGreaterThan(LocalDate value) {
            addCriterion("slot_start_sunday >", value, "slotStartSunday");
            return (Criteria) this;
        }

        public Criteria andSlotStartSundayGreaterThanOrEqualTo(LocalDate value) {
            addCriterion("slot_start_sunday >=", value, "slotStartSunday");
            return (Criteria) this;
        }

        public Criteria andSlotStartSundayLessThan(LocalDate value) {
            addCriterion("slot_start_sunday <", value, "slotStartSunday");
            return (Criteria) this;
        }

        public Criteria andSlotStartSundayLessThanOrEqualTo(LocalDate value) {
            addCriterion("slot_start_sunday <=", value, "slotStartSunday");
            return (Criteria) this;
        }

        public Criteria andSlotStartSundayIn(List<LocalDate> values) {
            addCriterion("slot_start_sunday in", values, "slotStartSunday");
            return (Criteria) this;
        }

        public Criteria andSlotStartSundayNotIn(List<LocalDate> values) {
            addCriterion("slot_start_sunday not in", values, "slotStartSunday");
            return (Criteria) this;
        }

        public Criteria andSlotStartSundayBetween(LocalDate value1, LocalDate value2) {
            addCriterion("slot_start_sunday between", value1, value2, "slotStartSunday");
            return (Criteria) this;
        }

        public Criteria andSlotStartSundayNotBetween(LocalDate value1, LocalDate value2) {
            addCriterion("slot_start_sunday not between", value1, value2, "slotStartSunday");
            return (Criteria) this;
        }

        public Criteria andTimeScheduleTypeIsNull() {
            addCriterion("time_schedule_type is null");
            return (Criteria) this;
        }

        public Criteria andTimeScheduleTypeIsNotNull() {
            addCriterion("time_schedule_type is not null");
            return (Criteria) this;
        }

        public Criteria andTimeScheduleTypeEqualTo(Integer value) {
            addCriterion("time_schedule_type =", value, "timeScheduleType");
            return (Criteria) this;
        }

        public Criteria andTimeScheduleTypeNotEqualTo(Integer value) {
            addCriterion("time_schedule_type <>", value, "timeScheduleType");
            return (Criteria) this;
        }

        public Criteria andTimeScheduleTypeGreaterThan(Integer value) {
            addCriterion("time_schedule_type >", value, "timeScheduleType");
            return (Criteria) this;
        }

        public Criteria andTimeScheduleTypeGreaterThanOrEqualTo(Integer value) {
            addCriterion("time_schedule_type >=", value, "timeScheduleType");
            return (Criteria) this;
        }

        public Criteria andTimeScheduleTypeLessThan(Integer value) {
            addCriterion("time_schedule_type <", value, "timeScheduleType");
            return (Criteria) this;
        }

        public Criteria andTimeScheduleTypeLessThanOrEqualTo(Integer value) {
            addCriterion("time_schedule_type <=", value, "timeScheduleType");
            return (Criteria) this;
        }

        public Criteria andTimeScheduleTypeIn(List<Integer> values) {
            addCriterion("time_schedule_type in", values, "timeScheduleType");
            return (Criteria) this;
        }

        public Criteria andTimeScheduleTypeNotIn(List<Integer> values) {
            addCriterion("time_schedule_type not in", values, "timeScheduleType");
            return (Criteria) this;
        }

        public Criteria andTimeScheduleTypeBetween(Integer value1, Integer value2) {
            addCriterion("time_schedule_type between", value1, value2, "timeScheduleType");
            return (Criteria) this;
        }

        public Criteria andTimeScheduleTypeNotBetween(Integer value1, Integer value2) {
            addCriterion("time_schedule_type not between", value1, value2, "timeScheduleType");
            return (Criteria) this;
        }

        public Criteria andTimeStartSundayIsNull() {
            addCriterion("time_start_sunday is null");
            return (Criteria) this;
        }

        public Criteria andTimeStartSundayIsNotNull() {
            addCriterion("time_start_sunday is not null");
            return (Criteria) this;
        }

        public Criteria andTimeStartSundayEqualTo(LocalDate value) {
            addCriterion("time_start_sunday =", value, "timeStartSunday");
            return (Criteria) this;
        }

        public Criteria andTimeStartSundayNotEqualTo(LocalDate value) {
            addCriterion("time_start_sunday <>", value, "timeStartSunday");
            return (Criteria) this;
        }

        public Criteria andTimeStartSundayGreaterThan(LocalDate value) {
            addCriterion("time_start_sunday >", value, "timeStartSunday");
            return (Criteria) this;
        }

        public Criteria andTimeStartSundayGreaterThanOrEqualTo(LocalDate value) {
            addCriterion("time_start_sunday >=", value, "timeStartSunday");
            return (Criteria) this;
        }

        public Criteria andTimeStartSundayLessThan(LocalDate value) {
            addCriterion("time_start_sunday <", value, "timeStartSunday");
            return (Criteria) this;
        }

        public Criteria andTimeStartSundayLessThanOrEqualTo(LocalDate value) {
            addCriterion("time_start_sunday <=", value, "timeStartSunday");
            return (Criteria) this;
        }

        public Criteria andTimeStartSundayIn(List<LocalDate> values) {
            addCriterion("time_start_sunday in", values, "timeStartSunday");
            return (Criteria) this;
        }

        public Criteria andTimeStartSundayNotIn(List<LocalDate> values) {
            addCriterion("time_start_sunday not in", values, "timeStartSunday");
            return (Criteria) this;
        }

        public Criteria andTimeStartSundayBetween(LocalDate value1, LocalDate value2) {
            addCriterion("time_start_sunday between", value1, value2, "timeStartSunday");
            return (Criteria) this;
        }

        public Criteria andTimeStartSundayNotBetween(LocalDate value1, LocalDate value2) {
            addCriterion("time_start_sunday not between", value1, value2, "timeStartSunday");
            return (Criteria) this;
        }

        public Criteria andCreatedAtIsNull() {
            addCriterion("created_at is null");
            return (Criteria) this;
        }

        public Criteria andCreatedAtIsNotNull() {
            addCriterion("created_at is not null");
            return (Criteria) this;
        }

        public Criteria andCreatedAtEqualTo(LocalDateTime value) {
            addCriterion("created_at =", value, "createdAt");
            return (Criteria) this;
        }

        public Criteria andCreatedAtNotEqualTo(LocalDateTime value) {
            addCriterion("created_at <>", value, "createdAt");
            return (Criteria) this;
        }

        public Criteria andCreatedAtGreaterThan(LocalDateTime value) {
            addCriterion("created_at >", value, "createdAt");
            return (Criteria) this;
        }

        public Criteria andCreatedAtGreaterThanOrEqualTo(LocalDateTime value) {
            addCriterion("created_at >=", value, "createdAt");
            return (Criteria) this;
        }

        public Criteria andCreatedAtLessThan(LocalDateTime value) {
            addCriterion("created_at <", value, "createdAt");
            return (Criteria) this;
        }

        public Criteria andCreatedAtLessThanOrEqualTo(LocalDateTime value) {
            addCriterion("created_at <=", value, "createdAt");
            return (Criteria) this;
        }

        public Criteria andCreatedAtIn(List<LocalDateTime> values) {
            addCriterion("created_at in", values, "createdAt");
            return (Criteria) this;
        }

        public Criteria andCreatedAtNotIn(List<LocalDateTime> values) {
            addCriterion("created_at not in", values, "createdAt");
            return (Criteria) this;
        }

        public Criteria andCreatedAtBetween(LocalDateTime value1, LocalDateTime value2) {
            addCriterion("created_at between", value1, value2, "createdAt");
            return (Criteria) this;
        }

        public Criteria andCreatedAtNotBetween(LocalDateTime value1, LocalDateTime value2) {
            addCriterion("created_at not between", value1, value2, "createdAt");
            return (Criteria) this;
        }

        public Criteria andUpdatedAtIsNull() {
            addCriterion("updated_at is null");
            return (Criteria) this;
        }

        public Criteria andUpdatedAtIsNotNull() {
            addCriterion("updated_at is not null");
            return (Criteria) this;
        }

        public Criteria andUpdatedAtEqualTo(LocalDateTime value) {
            addCriterion("updated_at =", value, "updatedAt");
            return (Criteria) this;
        }

        public Criteria andUpdatedAtNotEqualTo(LocalDateTime value) {
            addCriterion("updated_at <>", value, "updatedAt");
            return (Criteria) this;
        }

        public Criteria andUpdatedAtGreaterThan(LocalDateTime value) {
            addCriterion("updated_at >", value, "updatedAt");
            return (Criteria) this;
        }

        public Criteria andUpdatedAtGreaterThanOrEqualTo(LocalDateTime value) {
            addCriterion("updated_at >=", value, "updatedAt");
            return (Criteria) this;
        }

        public Criteria andUpdatedAtLessThan(LocalDateTime value) {
            addCriterion("updated_at <", value, "updatedAt");
            return (Criteria) this;
        }

        public Criteria andUpdatedAtLessThanOrEqualTo(LocalDateTime value) {
            addCriterion("updated_at <=", value, "updatedAt");
            return (Criteria) this;
        }

        public Criteria andUpdatedAtIn(List<LocalDateTime> values) {
            addCriterion("updated_at in", values, "updatedAt");
            return (Criteria) this;
        }

        public Criteria andUpdatedAtNotIn(List<LocalDateTime> values) {
            addCriterion("updated_at not in", values, "updatedAt");
            return (Criteria) this;
        }

        public Criteria andUpdatedAtBetween(LocalDateTime value1, LocalDateTime value2) {
            addCriterion("updated_at between", value1, value2, "updatedAt");
            return (Criteria) this;
        }

        public Criteria andUpdatedAtNotBetween(LocalDateTime value1, LocalDateTime value2) {
            addCriterion("updated_at not between", value1, value2, "updatedAt");
            return (Criteria) this;
        }
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table staff_availability
     *
     * @mbg.generated do_not_delete_during_merge
     */
    public static class Criteria extends GeneratedCriteria {
        protected Criteria() {
            super();
        }
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table staff_availability
     *
     * @mbg.generated
     */
    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}
