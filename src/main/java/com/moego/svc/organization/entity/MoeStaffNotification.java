package com.moego.svc.organization.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder(toBuilder = true)
@AllArgsConstructor(staticName = "")
@NoArgsConstructor(staticName = "")
public class MoeStaffNotification {
    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_staff_notification.id
     *
     * @mbg.generated
     */
    private Integer id;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_staff_notification.staff_id
     *
     * @mbg.generated
     */
    private Integer staffId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_staff_notification.booking_created
     *
     * @mbg.generated
     */
    private Byte bookingCreated;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_staff_notification.booking_cancelled
     *
     * @mbg.generated
     */
    private Byte bookingCancelled;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_staff_notification.booking_rescheduled
     *
     * @mbg.generated
     */
    private Byte bookingRescheduled;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_staff_notification.new_booking
     *
     * @mbg.generated
     */
    private Byte newBooking;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_staff_notification.new_intake_form
     *
     * @mbg.generated
     */
    private Byte newIntakeForm;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_staff_notification.agreement_signed
     *
     * @mbg.generated
     */
    private Byte agreementSigned;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_staff_notification.invoice_paid
     *
     * @mbg.generated
     */
    private Byte invoicePaid;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_staff_notification.review_submitted
     *
     * @mbg.generated
     */
    private Byte reviewSubmitted;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_staff_notification.push_calendar_switch
     *
     * @mbg.generated
     */
    private Byte pushCalendarSwitch;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_staff_notification.before_mins
     *
     * @mbg.generated
     */
    private Integer beforeMins;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_staff_notification.create_time
     *
     * @mbg.generated
     */
    private Long createTime;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_staff_notification.update_time
     *
     * @mbg.generated
     */
    private Long updateTime;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_staff_notification.new_abandoned_bookings
     *
     * @mbg.generated
     */
    private Byte newAbandonedBookings;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_staff_notification.company_id
     *
     * @mbg.generated
     */
    private Long companyId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_staff_notification.business_id
     *
     * @mbg.generated
     */
    private Long businessId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_staff_notification.assigned_task
     *
     * @mbg.generated
     */
    private Byte assignedTask;
}
