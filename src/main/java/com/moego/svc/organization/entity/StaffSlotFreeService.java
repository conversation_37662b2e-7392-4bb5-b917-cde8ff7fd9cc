package com.moego.svc.organization.entity;

import java.time.LocalDateTime;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder(toBuilder = true)
@AllArgsConstructor(staticName = "")
@NoArgsConstructor(staticName = "")
public class StaffSlotFreeService {
    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column staff_slot_free_service.id
     *
     * @mbg.generated
     */
    private Long id;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column staff_slot_free_service.company_id
     *
     * @mbg.generated
     */
    private Long companyId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column staff_slot_free_service.business_id
     *
     * @mbg.generated
     */
    private Long businessId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column staff_slot_free_service.staff_id
     *
     * @mbg.generated
     */
    private Long staffId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column staff_slot_free_service.service_id
     *
     * @mbg.generated
     */
    private Long serviceId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column staff_slot_free_service.created_at
     *
     * @mbg.generated
     */
    private LocalDateTime createdAt;
}
