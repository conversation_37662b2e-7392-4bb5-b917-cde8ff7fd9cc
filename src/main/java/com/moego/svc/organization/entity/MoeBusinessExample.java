package com.moego.svc.organization.entity;

import java.util.ArrayList;
import java.util.List;

public class MoeBusinessExample {
    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table moe_business
     *
     * @mbg.generated
     */
    protected String orderByClause;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table moe_business
     *
     * @mbg.generated
     */
    protected boolean distinct;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table moe_business
     *
     * @mbg.generated
     */
    protected List<Criteria> oredCriteria;

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_business
     *
     * @mbg.generated
     */
    public MoeBusinessExample() {
        oredCriteria = new ArrayList<>();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_business
     *
     * @mbg.generated
     */
    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_business
     *
     * @mbg.generated
     */
    public String getOrderByClause() {
        return orderByClause;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_business
     *
     * @mbg.generated
     */
    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_business
     *
     * @mbg.generated
     */
    public boolean isDistinct() {
        return distinct;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_business
     *
     * @mbg.generated
     */
    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_business
     *
     * @mbg.generated
     */
    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_business
     *
     * @mbg.generated
     */
    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_business
     *
     * @mbg.generated
     */
    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_business
     *
     * @mbg.generated
     */
    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_business
     *
     * @mbg.generated
     */
    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table moe_business
     *
     * @mbg.generated
     */
    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Integer value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Integer value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Integer value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Integer value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Integer value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Integer> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Integer> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Integer value1, Integer value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Integer value1, Integer value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andCompanyIdIsNull() {
            addCriterion("company_id is null");
            return (Criteria) this;
        }

        public Criteria andCompanyIdIsNotNull() {
            addCriterion("company_id is not null");
            return (Criteria) this;
        }

        public Criteria andCompanyIdEqualTo(Integer value) {
            addCriterion("company_id =", value, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdNotEqualTo(Integer value) {
            addCriterion("company_id <>", value, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdGreaterThan(Integer value) {
            addCriterion("company_id >", value, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("company_id >=", value, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdLessThan(Integer value) {
            addCriterion("company_id <", value, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdLessThanOrEqualTo(Integer value) {
            addCriterion("company_id <=", value, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdIn(List<Integer> values) {
            addCriterion("company_id in", values, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdNotIn(List<Integer> values) {
            addCriterion("company_id not in", values, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdBetween(Integer value1, Integer value2) {
            addCriterion("company_id between", value1, value2, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdNotBetween(Integer value1, Integer value2) {
            addCriterion("company_id not between", value1, value2, "companyId");
            return (Criteria) this;
        }

        public Criteria andBusinessNameIsNull() {
            addCriterion("business_name is null");
            return (Criteria) this;
        }

        public Criteria andBusinessNameIsNotNull() {
            addCriterion("business_name is not null");
            return (Criteria) this;
        }

        public Criteria andBusinessNameEqualTo(String value) {
            addCriterion("business_name =", value, "businessName");
            return (Criteria) this;
        }

        public Criteria andBusinessNameNotEqualTo(String value) {
            addCriterion("business_name <>", value, "businessName");
            return (Criteria) this;
        }

        public Criteria andBusinessNameGreaterThan(String value) {
            addCriterion("business_name >", value, "businessName");
            return (Criteria) this;
        }

        public Criteria andBusinessNameGreaterThanOrEqualTo(String value) {
            addCriterion("business_name >=", value, "businessName");
            return (Criteria) this;
        }

        public Criteria andBusinessNameLessThan(String value) {
            addCriterion("business_name <", value, "businessName");
            return (Criteria) this;
        }

        public Criteria andBusinessNameLessThanOrEqualTo(String value) {
            addCriterion("business_name <=", value, "businessName");
            return (Criteria) this;
        }

        public Criteria andBusinessNameLike(String value) {
            addCriterion("business_name like", value, "businessName");
            return (Criteria) this;
        }

        public Criteria andBusinessNameNotLike(String value) {
            addCriterion("business_name not like", value, "businessName");
            return (Criteria) this;
        }

        public Criteria andBusinessNameIn(List<String> values) {
            addCriterion("business_name in", values, "businessName");
            return (Criteria) this;
        }

        public Criteria andBusinessNameNotIn(List<String> values) {
            addCriterion("business_name not in", values, "businessName");
            return (Criteria) this;
        }

        public Criteria andBusinessNameBetween(String value1, String value2) {
            addCriterion("business_name between", value1, value2, "businessName");
            return (Criteria) this;
        }

        public Criteria andBusinessNameNotBetween(String value1, String value2) {
            addCriterion("business_name not between", value1, value2, "businessName");
            return (Criteria) this;
        }

        public Criteria andPhoneNumberIsNull() {
            addCriterion("phone_number is null");
            return (Criteria) this;
        }

        public Criteria andPhoneNumberIsNotNull() {
            addCriterion("phone_number is not null");
            return (Criteria) this;
        }

        public Criteria andPhoneNumberEqualTo(String value) {
            addCriterion("phone_number =", value, "phoneNumber");
            return (Criteria) this;
        }

        public Criteria andPhoneNumberNotEqualTo(String value) {
            addCriterion("phone_number <>", value, "phoneNumber");
            return (Criteria) this;
        }

        public Criteria andPhoneNumberGreaterThan(String value) {
            addCriterion("phone_number >", value, "phoneNumber");
            return (Criteria) this;
        }

        public Criteria andPhoneNumberGreaterThanOrEqualTo(String value) {
            addCriterion("phone_number >=", value, "phoneNumber");
            return (Criteria) this;
        }

        public Criteria andPhoneNumberLessThan(String value) {
            addCriterion("phone_number <", value, "phoneNumber");
            return (Criteria) this;
        }

        public Criteria andPhoneNumberLessThanOrEqualTo(String value) {
            addCriterion("phone_number <=", value, "phoneNumber");
            return (Criteria) this;
        }

        public Criteria andPhoneNumberLike(String value) {
            addCriterion("phone_number like", value, "phoneNumber");
            return (Criteria) this;
        }

        public Criteria andPhoneNumberNotLike(String value) {
            addCriterion("phone_number not like", value, "phoneNumber");
            return (Criteria) this;
        }

        public Criteria andPhoneNumberIn(List<String> values) {
            addCriterion("phone_number in", values, "phoneNumber");
            return (Criteria) this;
        }

        public Criteria andPhoneNumberNotIn(List<String> values) {
            addCriterion("phone_number not in", values, "phoneNumber");
            return (Criteria) this;
        }

        public Criteria andPhoneNumberBetween(String value1, String value2) {
            addCriterion("phone_number between", value1, value2, "phoneNumber");
            return (Criteria) this;
        }

        public Criteria andPhoneNumberNotBetween(String value1, String value2) {
            addCriterion("phone_number not between", value1, value2, "phoneNumber");
            return (Criteria) this;
        }

        public Criteria andAvatarPathIsNull() {
            addCriterion("avatar_path is null");
            return (Criteria) this;
        }

        public Criteria andAvatarPathIsNotNull() {
            addCriterion("avatar_path is not null");
            return (Criteria) this;
        }

        public Criteria andAvatarPathEqualTo(String value) {
            addCriterion("avatar_path =", value, "avatarPath");
            return (Criteria) this;
        }

        public Criteria andAvatarPathNotEqualTo(String value) {
            addCriterion("avatar_path <>", value, "avatarPath");
            return (Criteria) this;
        }

        public Criteria andAvatarPathGreaterThan(String value) {
            addCriterion("avatar_path >", value, "avatarPath");
            return (Criteria) this;
        }

        public Criteria andAvatarPathGreaterThanOrEqualTo(String value) {
            addCriterion("avatar_path >=", value, "avatarPath");
            return (Criteria) this;
        }

        public Criteria andAvatarPathLessThan(String value) {
            addCriterion("avatar_path <", value, "avatarPath");
            return (Criteria) this;
        }

        public Criteria andAvatarPathLessThanOrEqualTo(String value) {
            addCriterion("avatar_path <=", value, "avatarPath");
            return (Criteria) this;
        }

        public Criteria andAvatarPathLike(String value) {
            addCriterion("avatar_path like", value, "avatarPath");
            return (Criteria) this;
        }

        public Criteria andAvatarPathNotLike(String value) {
            addCriterion("avatar_path not like", value, "avatarPath");
            return (Criteria) this;
        }

        public Criteria andAvatarPathIn(List<String> values) {
            addCriterion("avatar_path in", values, "avatarPath");
            return (Criteria) this;
        }

        public Criteria andAvatarPathNotIn(List<String> values) {
            addCriterion("avatar_path not in", values, "avatarPath");
            return (Criteria) this;
        }

        public Criteria andAvatarPathBetween(String value1, String value2) {
            addCriterion("avatar_path between", value1, value2, "avatarPath");
            return (Criteria) this;
        }

        public Criteria andAvatarPathNotBetween(String value1, String value2) {
            addCriterion("avatar_path not between", value1, value2, "avatarPath");
            return (Criteria) this;
        }

        public Criteria andWebsiteIsNull() {
            addCriterion("website is null");
            return (Criteria) this;
        }

        public Criteria andWebsiteIsNotNull() {
            addCriterion("website is not null");
            return (Criteria) this;
        }

        public Criteria andWebsiteEqualTo(String value) {
            addCriterion("website =", value, "website");
            return (Criteria) this;
        }

        public Criteria andWebsiteNotEqualTo(String value) {
            addCriterion("website <>", value, "website");
            return (Criteria) this;
        }

        public Criteria andWebsiteGreaterThan(String value) {
            addCriterion("website >", value, "website");
            return (Criteria) this;
        }

        public Criteria andWebsiteGreaterThanOrEqualTo(String value) {
            addCriterion("website >=", value, "website");
            return (Criteria) this;
        }

        public Criteria andWebsiteLessThan(String value) {
            addCriterion("website <", value, "website");
            return (Criteria) this;
        }

        public Criteria andWebsiteLessThanOrEqualTo(String value) {
            addCriterion("website <=", value, "website");
            return (Criteria) this;
        }

        public Criteria andWebsiteLike(String value) {
            addCriterion("website like", value, "website");
            return (Criteria) this;
        }

        public Criteria andWebsiteNotLike(String value) {
            addCriterion("website not like", value, "website");
            return (Criteria) this;
        }

        public Criteria andWebsiteIn(List<String> values) {
            addCriterion("website in", values, "website");
            return (Criteria) this;
        }

        public Criteria andWebsiteNotIn(List<String> values) {
            addCriterion("website not in", values, "website");
            return (Criteria) this;
        }

        public Criteria andWebsiteBetween(String value1, String value2) {
            addCriterion("website between", value1, value2, "website");
            return (Criteria) this;
        }

        public Criteria andWebsiteNotBetween(String value1, String value2) {
            addCriterion("website not between", value1, value2, "website");
            return (Criteria) this;
        }

        public Criteria andAddressIsNull() {
            addCriterion("address is null");
            return (Criteria) this;
        }

        public Criteria andAddressIsNotNull() {
            addCriterion("address is not null");
            return (Criteria) this;
        }

        public Criteria andAddressEqualTo(String value) {
            addCriterion("address =", value, "address");
            return (Criteria) this;
        }

        public Criteria andAddressNotEqualTo(String value) {
            addCriterion("address <>", value, "address");
            return (Criteria) this;
        }

        public Criteria andAddressGreaterThan(String value) {
            addCriterion("address >", value, "address");
            return (Criteria) this;
        }

        public Criteria andAddressGreaterThanOrEqualTo(String value) {
            addCriterion("address >=", value, "address");
            return (Criteria) this;
        }

        public Criteria andAddressLessThan(String value) {
            addCriterion("address <", value, "address");
            return (Criteria) this;
        }

        public Criteria andAddressLessThanOrEqualTo(String value) {
            addCriterion("address <=", value, "address");
            return (Criteria) this;
        }

        public Criteria andAddressLike(String value) {
            addCriterion("address like", value, "address");
            return (Criteria) this;
        }

        public Criteria andAddressNotLike(String value) {
            addCriterion("address not like", value, "address");
            return (Criteria) this;
        }

        public Criteria andAddressIn(List<String> values) {
            addCriterion("address in", values, "address");
            return (Criteria) this;
        }

        public Criteria andAddressNotIn(List<String> values) {
            addCriterion("address not in", values, "address");
            return (Criteria) this;
        }

        public Criteria andAddressBetween(String value1, String value2) {
            addCriterion("address between", value1, value2, "address");
            return (Criteria) this;
        }

        public Criteria andAddressNotBetween(String value1, String value2) {
            addCriterion("address not between", value1, value2, "address");
            return (Criteria) this;
        }

        public Criteria andAddress1IsNull() {
            addCriterion("address1 is null");
            return (Criteria) this;
        }

        public Criteria andAddress1IsNotNull() {
            addCriterion("address1 is not null");
            return (Criteria) this;
        }

        public Criteria andAddress1EqualTo(String value) {
            addCriterion("address1 =", value, "address1");
            return (Criteria) this;
        }

        public Criteria andAddress1NotEqualTo(String value) {
            addCriterion("address1 <>", value, "address1");
            return (Criteria) this;
        }

        public Criteria andAddress1GreaterThan(String value) {
            addCriterion("address1 >", value, "address1");
            return (Criteria) this;
        }

        public Criteria andAddress1GreaterThanOrEqualTo(String value) {
            addCriterion("address1 >=", value, "address1");
            return (Criteria) this;
        }

        public Criteria andAddress1LessThan(String value) {
            addCriterion("address1 <", value, "address1");
            return (Criteria) this;
        }

        public Criteria andAddress1LessThanOrEqualTo(String value) {
            addCriterion("address1 <=", value, "address1");
            return (Criteria) this;
        }

        public Criteria andAddress1Like(String value) {
            addCriterion("address1 like", value, "address1");
            return (Criteria) this;
        }

        public Criteria andAddress1NotLike(String value) {
            addCriterion("address1 not like", value, "address1");
            return (Criteria) this;
        }

        public Criteria andAddress1In(List<String> values) {
            addCriterion("address1 in", values, "address1");
            return (Criteria) this;
        }

        public Criteria andAddress1NotIn(List<String> values) {
            addCriterion("address1 not in", values, "address1");
            return (Criteria) this;
        }

        public Criteria andAddress1Between(String value1, String value2) {
            addCriterion("address1 between", value1, value2, "address1");
            return (Criteria) this;
        }

        public Criteria andAddress1NotBetween(String value1, String value2) {
            addCriterion("address1 not between", value1, value2, "address1");
            return (Criteria) this;
        }

        public Criteria andAddress2IsNull() {
            addCriterion("address2 is null");
            return (Criteria) this;
        }

        public Criteria andAddress2IsNotNull() {
            addCriterion("address2 is not null");
            return (Criteria) this;
        }

        public Criteria andAddress2EqualTo(String value) {
            addCriterion("address2 =", value, "address2");
            return (Criteria) this;
        }

        public Criteria andAddress2NotEqualTo(String value) {
            addCriterion("address2 <>", value, "address2");
            return (Criteria) this;
        }

        public Criteria andAddress2GreaterThan(String value) {
            addCriterion("address2 >", value, "address2");
            return (Criteria) this;
        }

        public Criteria andAddress2GreaterThanOrEqualTo(String value) {
            addCriterion("address2 >=", value, "address2");
            return (Criteria) this;
        }

        public Criteria andAddress2LessThan(String value) {
            addCriterion("address2 <", value, "address2");
            return (Criteria) this;
        }

        public Criteria andAddress2LessThanOrEqualTo(String value) {
            addCriterion("address2 <=", value, "address2");
            return (Criteria) this;
        }

        public Criteria andAddress2Like(String value) {
            addCriterion("address2 like", value, "address2");
            return (Criteria) this;
        }

        public Criteria andAddress2NotLike(String value) {
            addCriterion("address2 not like", value, "address2");
            return (Criteria) this;
        }

        public Criteria andAddress2In(List<String> values) {
            addCriterion("address2 in", values, "address2");
            return (Criteria) this;
        }

        public Criteria andAddress2NotIn(List<String> values) {
            addCriterion("address2 not in", values, "address2");
            return (Criteria) this;
        }

        public Criteria andAddress2Between(String value1, String value2) {
            addCriterion("address2 between", value1, value2, "address2");
            return (Criteria) this;
        }

        public Criteria andAddress2NotBetween(String value1, String value2) {
            addCriterion("address2 not between", value1, value2, "address2");
            return (Criteria) this;
        }

        public Criteria andAddressCityIsNull() {
            addCriterion("address_city is null");
            return (Criteria) this;
        }

        public Criteria andAddressCityIsNotNull() {
            addCriterion("address_city is not null");
            return (Criteria) this;
        }

        public Criteria andAddressCityEqualTo(String value) {
            addCriterion("address_city =", value, "addressCity");
            return (Criteria) this;
        }

        public Criteria andAddressCityNotEqualTo(String value) {
            addCriterion("address_city <>", value, "addressCity");
            return (Criteria) this;
        }

        public Criteria andAddressCityGreaterThan(String value) {
            addCriterion("address_city >", value, "addressCity");
            return (Criteria) this;
        }

        public Criteria andAddressCityGreaterThanOrEqualTo(String value) {
            addCriterion("address_city >=", value, "addressCity");
            return (Criteria) this;
        }

        public Criteria andAddressCityLessThan(String value) {
            addCriterion("address_city <", value, "addressCity");
            return (Criteria) this;
        }

        public Criteria andAddressCityLessThanOrEqualTo(String value) {
            addCriterion("address_city <=", value, "addressCity");
            return (Criteria) this;
        }

        public Criteria andAddressCityLike(String value) {
            addCriterion("address_city like", value, "addressCity");
            return (Criteria) this;
        }

        public Criteria andAddressCityNotLike(String value) {
            addCriterion("address_city not like", value, "addressCity");
            return (Criteria) this;
        }

        public Criteria andAddressCityIn(List<String> values) {
            addCriterion("address_city in", values, "addressCity");
            return (Criteria) this;
        }

        public Criteria andAddressCityNotIn(List<String> values) {
            addCriterion("address_city not in", values, "addressCity");
            return (Criteria) this;
        }

        public Criteria andAddressCityBetween(String value1, String value2) {
            addCriterion("address_city between", value1, value2, "addressCity");
            return (Criteria) this;
        }

        public Criteria andAddressCityNotBetween(String value1, String value2) {
            addCriterion("address_city not between", value1, value2, "addressCity");
            return (Criteria) this;
        }

        public Criteria andAddressStateIsNull() {
            addCriterion("address_state is null");
            return (Criteria) this;
        }

        public Criteria andAddressStateIsNotNull() {
            addCriterion("address_state is not null");
            return (Criteria) this;
        }

        public Criteria andAddressStateEqualTo(String value) {
            addCriterion("address_state =", value, "addressState");
            return (Criteria) this;
        }

        public Criteria andAddressStateNotEqualTo(String value) {
            addCriterion("address_state <>", value, "addressState");
            return (Criteria) this;
        }

        public Criteria andAddressStateGreaterThan(String value) {
            addCriterion("address_state >", value, "addressState");
            return (Criteria) this;
        }

        public Criteria andAddressStateGreaterThanOrEqualTo(String value) {
            addCriterion("address_state >=", value, "addressState");
            return (Criteria) this;
        }

        public Criteria andAddressStateLessThan(String value) {
            addCriterion("address_state <", value, "addressState");
            return (Criteria) this;
        }

        public Criteria andAddressStateLessThanOrEqualTo(String value) {
            addCriterion("address_state <=", value, "addressState");
            return (Criteria) this;
        }

        public Criteria andAddressStateLike(String value) {
            addCriterion("address_state like", value, "addressState");
            return (Criteria) this;
        }

        public Criteria andAddressStateNotLike(String value) {
            addCriterion("address_state not like", value, "addressState");
            return (Criteria) this;
        }

        public Criteria andAddressStateIn(List<String> values) {
            addCriterion("address_state in", values, "addressState");
            return (Criteria) this;
        }

        public Criteria andAddressStateNotIn(List<String> values) {
            addCriterion("address_state not in", values, "addressState");
            return (Criteria) this;
        }

        public Criteria andAddressStateBetween(String value1, String value2) {
            addCriterion("address_state between", value1, value2, "addressState");
            return (Criteria) this;
        }

        public Criteria andAddressStateNotBetween(String value1, String value2) {
            addCriterion("address_state not between", value1, value2, "addressState");
            return (Criteria) this;
        }

        public Criteria andAddressZipcodeIsNull() {
            addCriterion("address_zipcode is null");
            return (Criteria) this;
        }

        public Criteria andAddressZipcodeIsNotNull() {
            addCriterion("address_zipcode is not null");
            return (Criteria) this;
        }

        public Criteria andAddressZipcodeEqualTo(String value) {
            addCriterion("address_zipcode =", value, "addressZipcode");
            return (Criteria) this;
        }

        public Criteria andAddressZipcodeNotEqualTo(String value) {
            addCriterion("address_zipcode <>", value, "addressZipcode");
            return (Criteria) this;
        }

        public Criteria andAddressZipcodeGreaterThan(String value) {
            addCriterion("address_zipcode >", value, "addressZipcode");
            return (Criteria) this;
        }

        public Criteria andAddressZipcodeGreaterThanOrEqualTo(String value) {
            addCriterion("address_zipcode >=", value, "addressZipcode");
            return (Criteria) this;
        }

        public Criteria andAddressZipcodeLessThan(String value) {
            addCriterion("address_zipcode <", value, "addressZipcode");
            return (Criteria) this;
        }

        public Criteria andAddressZipcodeLessThanOrEqualTo(String value) {
            addCriterion("address_zipcode <=", value, "addressZipcode");
            return (Criteria) this;
        }

        public Criteria andAddressZipcodeLike(String value) {
            addCriterion("address_zipcode like", value, "addressZipcode");
            return (Criteria) this;
        }

        public Criteria andAddressZipcodeNotLike(String value) {
            addCriterion("address_zipcode not like", value, "addressZipcode");
            return (Criteria) this;
        }

        public Criteria andAddressZipcodeIn(List<String> values) {
            addCriterion("address_zipcode in", values, "addressZipcode");
            return (Criteria) this;
        }

        public Criteria andAddressZipcodeNotIn(List<String> values) {
            addCriterion("address_zipcode not in", values, "addressZipcode");
            return (Criteria) this;
        }

        public Criteria andAddressZipcodeBetween(String value1, String value2) {
            addCriterion("address_zipcode between", value1, value2, "addressZipcode");
            return (Criteria) this;
        }

        public Criteria andAddressZipcodeNotBetween(String value1, String value2) {
            addCriterion("address_zipcode not between", value1, value2, "addressZipcode");
            return (Criteria) this;
        }

        public Criteria andAddressCountryIsNull() {
            addCriterion("address_country is null");
            return (Criteria) this;
        }

        public Criteria andAddressCountryIsNotNull() {
            addCriterion("address_country is not null");
            return (Criteria) this;
        }

        public Criteria andAddressCountryEqualTo(String value) {
            addCriterion("address_country =", value, "addressCountry");
            return (Criteria) this;
        }

        public Criteria andAddressCountryNotEqualTo(String value) {
            addCriterion("address_country <>", value, "addressCountry");
            return (Criteria) this;
        }

        public Criteria andAddressCountryGreaterThan(String value) {
            addCriterion("address_country >", value, "addressCountry");
            return (Criteria) this;
        }

        public Criteria andAddressCountryGreaterThanOrEqualTo(String value) {
            addCriterion("address_country >=", value, "addressCountry");
            return (Criteria) this;
        }

        public Criteria andAddressCountryLessThan(String value) {
            addCriterion("address_country <", value, "addressCountry");
            return (Criteria) this;
        }

        public Criteria andAddressCountryLessThanOrEqualTo(String value) {
            addCriterion("address_country <=", value, "addressCountry");
            return (Criteria) this;
        }

        public Criteria andAddressCountryLike(String value) {
            addCriterion("address_country like", value, "addressCountry");
            return (Criteria) this;
        }

        public Criteria andAddressCountryNotLike(String value) {
            addCriterion("address_country not like", value, "addressCountry");
            return (Criteria) this;
        }

        public Criteria andAddressCountryIn(List<String> values) {
            addCriterion("address_country in", values, "addressCountry");
            return (Criteria) this;
        }

        public Criteria andAddressCountryNotIn(List<String> values) {
            addCriterion("address_country not in", values, "addressCountry");
            return (Criteria) this;
        }

        public Criteria andAddressCountryBetween(String value1, String value2) {
            addCriterion("address_country between", value1, value2, "addressCountry");
            return (Criteria) this;
        }

        public Criteria andAddressCountryNotBetween(String value1, String value2) {
            addCriterion("address_country not between", value1, value2, "addressCountry");
            return (Criteria) this;
        }

        public Criteria andAddressLatIsNull() {
            addCriterion("address_lat is null");
            return (Criteria) this;
        }

        public Criteria andAddressLatIsNotNull() {
            addCriterion("address_lat is not null");
            return (Criteria) this;
        }

        public Criteria andAddressLatEqualTo(String value) {
            addCriterion("address_lat =", value, "addressLat");
            return (Criteria) this;
        }

        public Criteria andAddressLatNotEqualTo(String value) {
            addCriterion("address_lat <>", value, "addressLat");
            return (Criteria) this;
        }

        public Criteria andAddressLatGreaterThan(String value) {
            addCriterion("address_lat >", value, "addressLat");
            return (Criteria) this;
        }

        public Criteria andAddressLatGreaterThanOrEqualTo(String value) {
            addCriterion("address_lat >=", value, "addressLat");
            return (Criteria) this;
        }

        public Criteria andAddressLatLessThan(String value) {
            addCriterion("address_lat <", value, "addressLat");
            return (Criteria) this;
        }

        public Criteria andAddressLatLessThanOrEqualTo(String value) {
            addCriterion("address_lat <=", value, "addressLat");
            return (Criteria) this;
        }

        public Criteria andAddressLatLike(String value) {
            addCriterion("address_lat like", value, "addressLat");
            return (Criteria) this;
        }

        public Criteria andAddressLatNotLike(String value) {
            addCriterion("address_lat not like", value, "addressLat");
            return (Criteria) this;
        }

        public Criteria andAddressLatIn(List<String> values) {
            addCriterion("address_lat in", values, "addressLat");
            return (Criteria) this;
        }

        public Criteria andAddressLatNotIn(List<String> values) {
            addCriterion("address_lat not in", values, "addressLat");
            return (Criteria) this;
        }

        public Criteria andAddressLatBetween(String value1, String value2) {
            addCriterion("address_lat between", value1, value2, "addressLat");
            return (Criteria) this;
        }

        public Criteria andAddressLatNotBetween(String value1, String value2) {
            addCriterion("address_lat not between", value1, value2, "addressLat");
            return (Criteria) this;
        }

        public Criteria andAddressLngIsNull() {
            addCriterion("address_lng is null");
            return (Criteria) this;
        }

        public Criteria andAddressLngIsNotNull() {
            addCriterion("address_lng is not null");
            return (Criteria) this;
        }

        public Criteria andAddressLngEqualTo(String value) {
            addCriterion("address_lng =", value, "addressLng");
            return (Criteria) this;
        }

        public Criteria andAddressLngNotEqualTo(String value) {
            addCriterion("address_lng <>", value, "addressLng");
            return (Criteria) this;
        }

        public Criteria andAddressLngGreaterThan(String value) {
            addCriterion("address_lng >", value, "addressLng");
            return (Criteria) this;
        }

        public Criteria andAddressLngGreaterThanOrEqualTo(String value) {
            addCriterion("address_lng >=", value, "addressLng");
            return (Criteria) this;
        }

        public Criteria andAddressLngLessThan(String value) {
            addCriterion("address_lng <", value, "addressLng");
            return (Criteria) this;
        }

        public Criteria andAddressLngLessThanOrEqualTo(String value) {
            addCriterion("address_lng <=", value, "addressLng");
            return (Criteria) this;
        }

        public Criteria andAddressLngLike(String value) {
            addCriterion("address_lng like", value, "addressLng");
            return (Criteria) this;
        }

        public Criteria andAddressLngNotLike(String value) {
            addCriterion("address_lng not like", value, "addressLng");
            return (Criteria) this;
        }

        public Criteria andAddressLngIn(List<String> values) {
            addCriterion("address_lng in", values, "addressLng");
            return (Criteria) this;
        }

        public Criteria andAddressLngNotIn(List<String> values) {
            addCriterion("address_lng not in", values, "addressLng");
            return (Criteria) this;
        }

        public Criteria andAddressLngBetween(String value1, String value2) {
            addCriterion("address_lng between", value1, value2, "addressLng");
            return (Criteria) this;
        }

        public Criteria andAddressLngNotBetween(String value1, String value2) {
            addCriterion("address_lng not between", value1, value2, "addressLng");
            return (Criteria) this;
        }

        public Criteria andCountryIsNull() {
            addCriterion("country is null");
            return (Criteria) this;
        }

        public Criteria andCountryIsNotNull() {
            addCriterion("country is not null");
            return (Criteria) this;
        }

        public Criteria andCountryEqualTo(String value) {
            addCriterion("country =", value, "country");
            return (Criteria) this;
        }

        public Criteria andCountryNotEqualTo(String value) {
            addCriterion("country <>", value, "country");
            return (Criteria) this;
        }

        public Criteria andCountryGreaterThan(String value) {
            addCriterion("country >", value, "country");
            return (Criteria) this;
        }

        public Criteria andCountryGreaterThanOrEqualTo(String value) {
            addCriterion("country >=", value, "country");
            return (Criteria) this;
        }

        public Criteria andCountryLessThan(String value) {
            addCriterion("country <", value, "country");
            return (Criteria) this;
        }

        public Criteria andCountryLessThanOrEqualTo(String value) {
            addCriterion("country <=", value, "country");
            return (Criteria) this;
        }

        public Criteria andCountryLike(String value) {
            addCriterion("country like", value, "country");
            return (Criteria) this;
        }

        public Criteria andCountryNotLike(String value) {
            addCriterion("country not like", value, "country");
            return (Criteria) this;
        }

        public Criteria andCountryIn(List<String> values) {
            addCriterion("country in", values, "country");
            return (Criteria) this;
        }

        public Criteria andCountryNotIn(List<String> values) {
            addCriterion("country not in", values, "country");
            return (Criteria) this;
        }

        public Criteria andCountryBetween(String value1, String value2) {
            addCriterion("country between", value1, value2, "country");
            return (Criteria) this;
        }

        public Criteria andCountryNotBetween(String value1, String value2) {
            addCriterion("country not between", value1, value2, "country");
            return (Criteria) this;
        }

        public Criteria andCountryAlpha2CodeIsNull() {
            addCriterion("country_alpha2_code is null");
            return (Criteria) this;
        }

        public Criteria andCountryAlpha2CodeIsNotNull() {
            addCriterion("country_alpha2_code is not null");
            return (Criteria) this;
        }

        public Criteria andCountryAlpha2CodeEqualTo(String value) {
            addCriterion("country_alpha2_code =", value, "countryAlpha2Code");
            return (Criteria) this;
        }

        public Criteria andCountryAlpha2CodeNotEqualTo(String value) {
            addCriterion("country_alpha2_code <>", value, "countryAlpha2Code");
            return (Criteria) this;
        }

        public Criteria andCountryAlpha2CodeGreaterThan(String value) {
            addCriterion("country_alpha2_code >", value, "countryAlpha2Code");
            return (Criteria) this;
        }

        public Criteria andCountryAlpha2CodeGreaterThanOrEqualTo(String value) {
            addCriterion("country_alpha2_code >=", value, "countryAlpha2Code");
            return (Criteria) this;
        }

        public Criteria andCountryAlpha2CodeLessThan(String value) {
            addCriterion("country_alpha2_code <", value, "countryAlpha2Code");
            return (Criteria) this;
        }

        public Criteria andCountryAlpha2CodeLessThanOrEqualTo(String value) {
            addCriterion("country_alpha2_code <=", value, "countryAlpha2Code");
            return (Criteria) this;
        }

        public Criteria andCountryAlpha2CodeLike(String value) {
            addCriterion("country_alpha2_code like", value, "countryAlpha2Code");
            return (Criteria) this;
        }

        public Criteria andCountryAlpha2CodeNotLike(String value) {
            addCriterion("country_alpha2_code not like", value, "countryAlpha2Code");
            return (Criteria) this;
        }

        public Criteria andCountryAlpha2CodeIn(List<String> values) {
            addCriterion("country_alpha2_code in", values, "countryAlpha2Code");
            return (Criteria) this;
        }

        public Criteria andCountryAlpha2CodeNotIn(List<String> values) {
            addCriterion("country_alpha2_code not in", values, "countryAlpha2Code");
            return (Criteria) this;
        }

        public Criteria andCountryAlpha2CodeBetween(String value1, String value2) {
            addCriterion("country_alpha2_code between", value1, value2, "countryAlpha2Code");
            return (Criteria) this;
        }

        public Criteria andCountryAlpha2CodeNotBetween(String value1, String value2) {
            addCriterion("country_alpha2_code not between", value1, value2, "countryAlpha2Code");
            return (Criteria) this;
        }

        public Criteria andCountryCodeIsNull() {
            addCriterion("country_code is null");
            return (Criteria) this;
        }

        public Criteria andCountryCodeIsNotNull() {
            addCriterion("country_code is not null");
            return (Criteria) this;
        }

        public Criteria andCountryCodeEqualTo(String value) {
            addCriterion("country_code =", value, "countryCode");
            return (Criteria) this;
        }

        public Criteria andCountryCodeNotEqualTo(String value) {
            addCriterion("country_code <>", value, "countryCode");
            return (Criteria) this;
        }

        public Criteria andCountryCodeGreaterThan(String value) {
            addCriterion("country_code >", value, "countryCode");
            return (Criteria) this;
        }

        public Criteria andCountryCodeGreaterThanOrEqualTo(String value) {
            addCriterion("country_code >=", value, "countryCode");
            return (Criteria) this;
        }

        public Criteria andCountryCodeLessThan(String value) {
            addCriterion("country_code <", value, "countryCode");
            return (Criteria) this;
        }

        public Criteria andCountryCodeLessThanOrEqualTo(String value) {
            addCriterion("country_code <=", value, "countryCode");
            return (Criteria) this;
        }

        public Criteria andCountryCodeLike(String value) {
            addCriterion("country_code like", value, "countryCode");
            return (Criteria) this;
        }

        public Criteria andCountryCodeNotLike(String value) {
            addCriterion("country_code not like", value, "countryCode");
            return (Criteria) this;
        }

        public Criteria andCountryCodeIn(List<String> values) {
            addCriterion("country_code in", values, "countryCode");
            return (Criteria) this;
        }

        public Criteria andCountryCodeNotIn(List<String> values) {
            addCriterion("country_code not in", values, "countryCode");
            return (Criteria) this;
        }

        public Criteria andCountryCodeBetween(String value1, String value2) {
            addCriterion("country_code between", value1, value2, "countryCode");
            return (Criteria) this;
        }

        public Criteria andCountryCodeNotBetween(String value1, String value2) {
            addCriterion("country_code not between", value1, value2, "countryCode");
            return (Criteria) this;
        }

        public Criteria andCurrencySymbolIsNull() {
            addCriterion("currency_symbol is null");
            return (Criteria) this;
        }

        public Criteria andCurrencySymbolIsNotNull() {
            addCriterion("currency_symbol is not null");
            return (Criteria) this;
        }

        public Criteria andCurrencySymbolEqualTo(String value) {
            addCriterion("currency_symbol =", value, "currencySymbol");
            return (Criteria) this;
        }

        public Criteria andCurrencySymbolNotEqualTo(String value) {
            addCriterion("currency_symbol <>", value, "currencySymbol");
            return (Criteria) this;
        }

        public Criteria andCurrencySymbolGreaterThan(String value) {
            addCriterion("currency_symbol >", value, "currencySymbol");
            return (Criteria) this;
        }

        public Criteria andCurrencySymbolGreaterThanOrEqualTo(String value) {
            addCriterion("currency_symbol >=", value, "currencySymbol");
            return (Criteria) this;
        }

        public Criteria andCurrencySymbolLessThan(String value) {
            addCriterion("currency_symbol <", value, "currencySymbol");
            return (Criteria) this;
        }

        public Criteria andCurrencySymbolLessThanOrEqualTo(String value) {
            addCriterion("currency_symbol <=", value, "currencySymbol");
            return (Criteria) this;
        }

        public Criteria andCurrencySymbolLike(String value) {
            addCriterion("currency_symbol like", value, "currencySymbol");
            return (Criteria) this;
        }

        public Criteria andCurrencySymbolNotLike(String value) {
            addCriterion("currency_symbol not like", value, "currencySymbol");
            return (Criteria) this;
        }

        public Criteria andCurrencySymbolIn(List<String> values) {
            addCriterion("currency_symbol in", values, "currencySymbol");
            return (Criteria) this;
        }

        public Criteria andCurrencySymbolNotIn(List<String> values) {
            addCriterion("currency_symbol not in", values, "currencySymbol");
            return (Criteria) this;
        }

        public Criteria andCurrencySymbolBetween(String value1, String value2) {
            addCriterion("currency_symbol between", value1, value2, "currencySymbol");
            return (Criteria) this;
        }

        public Criteria andCurrencySymbolNotBetween(String value1, String value2) {
            addCriterion("currency_symbol not between", value1, value2, "currencySymbol");
            return (Criteria) this;
        }

        public Criteria andCurrencyCodeIsNull() {
            addCriterion("currency_code is null");
            return (Criteria) this;
        }

        public Criteria andCurrencyCodeIsNotNull() {
            addCriterion("currency_code is not null");
            return (Criteria) this;
        }

        public Criteria andCurrencyCodeEqualTo(String value) {
            addCriterion("currency_code =", value, "currencyCode");
            return (Criteria) this;
        }

        public Criteria andCurrencyCodeNotEqualTo(String value) {
            addCriterion("currency_code <>", value, "currencyCode");
            return (Criteria) this;
        }

        public Criteria andCurrencyCodeGreaterThan(String value) {
            addCriterion("currency_code >", value, "currencyCode");
            return (Criteria) this;
        }

        public Criteria andCurrencyCodeGreaterThanOrEqualTo(String value) {
            addCriterion("currency_code >=", value, "currencyCode");
            return (Criteria) this;
        }

        public Criteria andCurrencyCodeLessThan(String value) {
            addCriterion("currency_code <", value, "currencyCode");
            return (Criteria) this;
        }

        public Criteria andCurrencyCodeLessThanOrEqualTo(String value) {
            addCriterion("currency_code <=", value, "currencyCode");
            return (Criteria) this;
        }

        public Criteria andCurrencyCodeLike(String value) {
            addCriterion("currency_code like", value, "currencyCode");
            return (Criteria) this;
        }

        public Criteria andCurrencyCodeNotLike(String value) {
            addCriterion("currency_code not like", value, "currencyCode");
            return (Criteria) this;
        }

        public Criteria andCurrencyCodeIn(List<String> values) {
            addCriterion("currency_code in", values, "currencyCode");
            return (Criteria) this;
        }

        public Criteria andCurrencyCodeNotIn(List<String> values) {
            addCriterion("currency_code not in", values, "currencyCode");
            return (Criteria) this;
        }

        public Criteria andCurrencyCodeBetween(String value1, String value2) {
            addCriterion("currency_code between", value1, value2, "currencyCode");
            return (Criteria) this;
        }

        public Criteria andCurrencyCodeNotBetween(String value1, String value2) {
            addCriterion("currency_code not between", value1, value2, "currencyCode");
            return (Criteria) this;
        }

        public Criteria andTimeFormatTypeIsNull() {
            addCriterion("time_format_type is null");
            return (Criteria) this;
        }

        public Criteria andTimeFormatTypeIsNotNull() {
            addCriterion("time_format_type is not null");
            return (Criteria) this;
        }

        public Criteria andTimeFormatTypeEqualTo(Byte value) {
            addCriterion("time_format_type =", value, "timeFormatType");
            return (Criteria) this;
        }

        public Criteria andTimeFormatTypeNotEqualTo(Byte value) {
            addCriterion("time_format_type <>", value, "timeFormatType");
            return (Criteria) this;
        }

        public Criteria andTimeFormatTypeGreaterThan(Byte value) {
            addCriterion("time_format_type >", value, "timeFormatType");
            return (Criteria) this;
        }

        public Criteria andTimeFormatTypeGreaterThanOrEqualTo(Byte value) {
            addCriterion("time_format_type >=", value, "timeFormatType");
            return (Criteria) this;
        }

        public Criteria andTimeFormatTypeLessThan(Byte value) {
            addCriterion("time_format_type <", value, "timeFormatType");
            return (Criteria) this;
        }

        public Criteria andTimeFormatTypeLessThanOrEqualTo(Byte value) {
            addCriterion("time_format_type <=", value, "timeFormatType");
            return (Criteria) this;
        }

        public Criteria andTimeFormatTypeIn(List<Byte> values) {
            addCriterion("time_format_type in", values, "timeFormatType");
            return (Criteria) this;
        }

        public Criteria andTimeFormatTypeNotIn(List<Byte> values) {
            addCriterion("time_format_type not in", values, "timeFormatType");
            return (Criteria) this;
        }

        public Criteria andTimeFormatTypeBetween(Byte value1, Byte value2) {
            addCriterion("time_format_type between", value1, value2, "timeFormatType");
            return (Criteria) this;
        }

        public Criteria andTimeFormatTypeNotBetween(Byte value1, Byte value2) {
            addCriterion("time_format_type not between", value1, value2, "timeFormatType");
            return (Criteria) this;
        }

        public Criteria andUnitOfWeightTypeIsNull() {
            addCriterion("unit_of_weight_type is null");
            return (Criteria) this;
        }

        public Criteria andUnitOfWeightTypeIsNotNull() {
            addCriterion("unit_of_weight_type is not null");
            return (Criteria) this;
        }

        public Criteria andUnitOfWeightTypeEqualTo(Byte value) {
            addCriterion("unit_of_weight_type =", value, "unitOfWeightType");
            return (Criteria) this;
        }

        public Criteria andUnitOfWeightTypeNotEqualTo(Byte value) {
            addCriterion("unit_of_weight_type <>", value, "unitOfWeightType");
            return (Criteria) this;
        }

        public Criteria andUnitOfWeightTypeGreaterThan(Byte value) {
            addCriterion("unit_of_weight_type >", value, "unitOfWeightType");
            return (Criteria) this;
        }

        public Criteria andUnitOfWeightTypeGreaterThanOrEqualTo(Byte value) {
            addCriterion("unit_of_weight_type >=", value, "unitOfWeightType");
            return (Criteria) this;
        }

        public Criteria andUnitOfWeightTypeLessThan(Byte value) {
            addCriterion("unit_of_weight_type <", value, "unitOfWeightType");
            return (Criteria) this;
        }

        public Criteria andUnitOfWeightTypeLessThanOrEqualTo(Byte value) {
            addCriterion("unit_of_weight_type <=", value, "unitOfWeightType");
            return (Criteria) this;
        }

        public Criteria andUnitOfWeightTypeIn(List<Byte> values) {
            addCriterion("unit_of_weight_type in", values, "unitOfWeightType");
            return (Criteria) this;
        }

        public Criteria andUnitOfWeightTypeNotIn(List<Byte> values) {
            addCriterion("unit_of_weight_type not in", values, "unitOfWeightType");
            return (Criteria) this;
        }

        public Criteria andUnitOfWeightTypeBetween(Byte value1, Byte value2) {
            addCriterion("unit_of_weight_type between", value1, value2, "unitOfWeightType");
            return (Criteria) this;
        }

        public Criteria andUnitOfWeightTypeNotBetween(Byte value1, Byte value2) {
            addCriterion("unit_of_weight_type not between", value1, value2, "unitOfWeightType");
            return (Criteria) this;
        }

        public Criteria andUnitOfDistanceTypeIsNull() {
            addCriterion("unit_of_distance_type is null");
            return (Criteria) this;
        }

        public Criteria andUnitOfDistanceTypeIsNotNull() {
            addCriterion("unit_of_distance_type is not null");
            return (Criteria) this;
        }

        public Criteria andUnitOfDistanceTypeEqualTo(Byte value) {
            addCriterion("unit_of_distance_type =", value, "unitOfDistanceType");
            return (Criteria) this;
        }

        public Criteria andUnitOfDistanceTypeNotEqualTo(Byte value) {
            addCriterion("unit_of_distance_type <>", value, "unitOfDistanceType");
            return (Criteria) this;
        }

        public Criteria andUnitOfDistanceTypeGreaterThan(Byte value) {
            addCriterion("unit_of_distance_type >", value, "unitOfDistanceType");
            return (Criteria) this;
        }

        public Criteria andUnitOfDistanceTypeGreaterThanOrEqualTo(Byte value) {
            addCriterion("unit_of_distance_type >=", value, "unitOfDistanceType");
            return (Criteria) this;
        }

        public Criteria andUnitOfDistanceTypeLessThan(Byte value) {
            addCriterion("unit_of_distance_type <", value, "unitOfDistanceType");
            return (Criteria) this;
        }

        public Criteria andUnitOfDistanceTypeLessThanOrEqualTo(Byte value) {
            addCriterion("unit_of_distance_type <=", value, "unitOfDistanceType");
            return (Criteria) this;
        }

        public Criteria andUnitOfDistanceTypeIn(List<Byte> values) {
            addCriterion("unit_of_distance_type in", values, "unitOfDistanceType");
            return (Criteria) this;
        }

        public Criteria andUnitOfDistanceTypeNotIn(List<Byte> values) {
            addCriterion("unit_of_distance_type not in", values, "unitOfDistanceType");
            return (Criteria) this;
        }

        public Criteria andUnitOfDistanceTypeBetween(Byte value1, Byte value2) {
            addCriterion("unit_of_distance_type between", value1, value2, "unitOfDistanceType");
            return (Criteria) this;
        }

        public Criteria andUnitOfDistanceTypeNotBetween(Byte value1, Byte value2) {
            addCriterion("unit_of_distance_type not between", value1, value2, "unitOfDistanceType");
            return (Criteria) this;
        }

        public Criteria andTimezoneNameIsNull() {
            addCriterion("timezone_name is null");
            return (Criteria) this;
        }

        public Criteria andTimezoneNameIsNotNull() {
            addCriterion("timezone_name is not null");
            return (Criteria) this;
        }

        public Criteria andTimezoneNameEqualTo(String value) {
            addCriterion("timezone_name =", value, "timezoneName");
            return (Criteria) this;
        }

        public Criteria andTimezoneNameNotEqualTo(String value) {
            addCriterion("timezone_name <>", value, "timezoneName");
            return (Criteria) this;
        }

        public Criteria andTimezoneNameGreaterThan(String value) {
            addCriterion("timezone_name >", value, "timezoneName");
            return (Criteria) this;
        }

        public Criteria andTimezoneNameGreaterThanOrEqualTo(String value) {
            addCriterion("timezone_name >=", value, "timezoneName");
            return (Criteria) this;
        }

        public Criteria andTimezoneNameLessThan(String value) {
            addCriterion("timezone_name <", value, "timezoneName");
            return (Criteria) this;
        }

        public Criteria andTimezoneNameLessThanOrEqualTo(String value) {
            addCriterion("timezone_name <=", value, "timezoneName");
            return (Criteria) this;
        }

        public Criteria andTimezoneNameLike(String value) {
            addCriterion("timezone_name like", value, "timezoneName");
            return (Criteria) this;
        }

        public Criteria andTimezoneNameNotLike(String value) {
            addCriterion("timezone_name not like", value, "timezoneName");
            return (Criteria) this;
        }

        public Criteria andTimezoneNameIn(List<String> values) {
            addCriterion("timezone_name in", values, "timezoneName");
            return (Criteria) this;
        }

        public Criteria andTimezoneNameNotIn(List<String> values) {
            addCriterion("timezone_name not in", values, "timezoneName");
            return (Criteria) this;
        }

        public Criteria andTimezoneNameBetween(String value1, String value2) {
            addCriterion("timezone_name between", value1, value2, "timezoneName");
            return (Criteria) this;
        }

        public Criteria andTimezoneNameNotBetween(String value1, String value2) {
            addCriterion("timezone_name not between", value1, value2, "timezoneName");
            return (Criteria) this;
        }

        public Criteria andTimezoneSecondsIsNull() {
            addCriterion("timezone_seconds is null");
            return (Criteria) this;
        }

        public Criteria andTimezoneSecondsIsNotNull() {
            addCriterion("timezone_seconds is not null");
            return (Criteria) this;
        }

        public Criteria andTimezoneSecondsEqualTo(Integer value) {
            addCriterion("timezone_seconds =", value, "timezoneSeconds");
            return (Criteria) this;
        }

        public Criteria andTimezoneSecondsNotEqualTo(Integer value) {
            addCriterion("timezone_seconds <>", value, "timezoneSeconds");
            return (Criteria) this;
        }

        public Criteria andTimezoneSecondsGreaterThan(Integer value) {
            addCriterion("timezone_seconds >", value, "timezoneSeconds");
            return (Criteria) this;
        }

        public Criteria andTimezoneSecondsGreaterThanOrEqualTo(Integer value) {
            addCriterion("timezone_seconds >=", value, "timezoneSeconds");
            return (Criteria) this;
        }

        public Criteria andTimezoneSecondsLessThan(Integer value) {
            addCriterion("timezone_seconds <", value, "timezoneSeconds");
            return (Criteria) this;
        }

        public Criteria andTimezoneSecondsLessThanOrEqualTo(Integer value) {
            addCriterion("timezone_seconds <=", value, "timezoneSeconds");
            return (Criteria) this;
        }

        public Criteria andTimezoneSecondsIn(List<Integer> values) {
            addCriterion("timezone_seconds in", values, "timezoneSeconds");
            return (Criteria) this;
        }

        public Criteria andTimezoneSecondsNotIn(List<Integer> values) {
            addCriterion("timezone_seconds not in", values, "timezoneSeconds");
            return (Criteria) this;
        }

        public Criteria andTimezoneSecondsBetween(Integer value1, Integer value2) {
            addCriterion("timezone_seconds between", value1, value2, "timezoneSeconds");
            return (Criteria) this;
        }

        public Criteria andTimezoneSecondsNotBetween(Integer value1, Integer value2) {
            addCriterion("timezone_seconds not between", value1, value2, "timezoneSeconds");
            return (Criteria) this;
        }

        public Criteria andDateFormatTypeIsNull() {
            addCriterion("date_format_type is null");
            return (Criteria) this;
        }

        public Criteria andDateFormatTypeIsNotNull() {
            addCriterion("date_format_type is not null");
            return (Criteria) this;
        }

        public Criteria andDateFormatTypeEqualTo(Byte value) {
            addCriterion("date_format_type =", value, "dateFormatType");
            return (Criteria) this;
        }

        public Criteria andDateFormatTypeNotEqualTo(Byte value) {
            addCriterion("date_format_type <>", value, "dateFormatType");
            return (Criteria) this;
        }

        public Criteria andDateFormatTypeGreaterThan(Byte value) {
            addCriterion("date_format_type >", value, "dateFormatType");
            return (Criteria) this;
        }

        public Criteria andDateFormatTypeGreaterThanOrEqualTo(Byte value) {
            addCriterion("date_format_type >=", value, "dateFormatType");
            return (Criteria) this;
        }

        public Criteria andDateFormatTypeLessThan(Byte value) {
            addCriterion("date_format_type <", value, "dateFormatType");
            return (Criteria) this;
        }

        public Criteria andDateFormatTypeLessThanOrEqualTo(Byte value) {
            addCriterion("date_format_type <=", value, "dateFormatType");
            return (Criteria) this;
        }

        public Criteria andDateFormatTypeIn(List<Byte> values) {
            addCriterion("date_format_type in", values, "dateFormatType");
            return (Criteria) this;
        }

        public Criteria andDateFormatTypeNotIn(List<Byte> values) {
            addCriterion("date_format_type not in", values, "dateFormatType");
            return (Criteria) this;
        }

        public Criteria andDateFormatTypeBetween(Byte value1, Byte value2) {
            addCriterion("date_format_type between", value1, value2, "dateFormatType");
            return (Criteria) this;
        }

        public Criteria andDateFormatTypeNotBetween(Byte value1, Byte value2) {
            addCriterion("date_format_type not between", value1, value2, "dateFormatType");
            return (Criteria) this;
        }

        public Criteria andCalendarFormatTypeIsNull() {
            addCriterion("calendar_format_type is null");
            return (Criteria) this;
        }

        public Criteria andCalendarFormatTypeIsNotNull() {
            addCriterion("calendar_format_type is not null");
            return (Criteria) this;
        }

        public Criteria andCalendarFormatTypeEqualTo(Byte value) {
            addCriterion("calendar_format_type =", value, "calendarFormatType");
            return (Criteria) this;
        }

        public Criteria andCalendarFormatTypeNotEqualTo(Byte value) {
            addCriterion("calendar_format_type <>", value, "calendarFormatType");
            return (Criteria) this;
        }

        public Criteria andCalendarFormatTypeGreaterThan(Byte value) {
            addCriterion("calendar_format_type >", value, "calendarFormatType");
            return (Criteria) this;
        }

        public Criteria andCalendarFormatTypeGreaterThanOrEqualTo(Byte value) {
            addCriterion("calendar_format_type >=", value, "calendarFormatType");
            return (Criteria) this;
        }

        public Criteria andCalendarFormatTypeLessThan(Byte value) {
            addCriterion("calendar_format_type <", value, "calendarFormatType");
            return (Criteria) this;
        }

        public Criteria andCalendarFormatTypeLessThanOrEqualTo(Byte value) {
            addCriterion("calendar_format_type <=", value, "calendarFormatType");
            return (Criteria) this;
        }

        public Criteria andCalendarFormatTypeIn(List<Byte> values) {
            addCriterion("calendar_format_type in", values, "calendarFormatType");
            return (Criteria) this;
        }

        public Criteria andCalendarFormatTypeNotIn(List<Byte> values) {
            addCriterion("calendar_format_type not in", values, "calendarFormatType");
            return (Criteria) this;
        }

        public Criteria andCalendarFormatTypeBetween(Byte value1, Byte value2) {
            addCriterion("calendar_format_type between", value1, value2, "calendarFormatType");
            return (Criteria) this;
        }

        public Criteria andCalendarFormatTypeNotBetween(Byte value1, Byte value2) {
            addCriterion("calendar_format_type not between", value1, value2, "calendarFormatType");
            return (Criteria) this;
        }

        public Criteria andNumberFormatTypeIsNull() {
            addCriterion("number_format_type is null");
            return (Criteria) this;
        }

        public Criteria andNumberFormatTypeIsNotNull() {
            addCriterion("number_format_type is not null");
            return (Criteria) this;
        }

        public Criteria andNumberFormatTypeEqualTo(Byte value) {
            addCriterion("number_format_type =", value, "numberFormatType");
            return (Criteria) this;
        }

        public Criteria andNumberFormatTypeNotEqualTo(Byte value) {
            addCriterion("number_format_type <>", value, "numberFormatType");
            return (Criteria) this;
        }

        public Criteria andNumberFormatTypeGreaterThan(Byte value) {
            addCriterion("number_format_type >", value, "numberFormatType");
            return (Criteria) this;
        }

        public Criteria andNumberFormatTypeGreaterThanOrEqualTo(Byte value) {
            addCriterion("number_format_type >=", value, "numberFormatType");
            return (Criteria) this;
        }

        public Criteria andNumberFormatTypeLessThan(Byte value) {
            addCriterion("number_format_type <", value, "numberFormatType");
            return (Criteria) this;
        }

        public Criteria andNumberFormatTypeLessThanOrEqualTo(Byte value) {
            addCriterion("number_format_type <=", value, "numberFormatType");
            return (Criteria) this;
        }

        public Criteria andNumberFormatTypeIn(List<Byte> values) {
            addCriterion("number_format_type in", values, "numberFormatType");
            return (Criteria) this;
        }

        public Criteria andNumberFormatTypeNotIn(List<Byte> values) {
            addCriterion("number_format_type not in", values, "numberFormatType");
            return (Criteria) this;
        }

        public Criteria andNumberFormatTypeBetween(Byte value1, Byte value2) {
            addCriterion("number_format_type between", value1, value2, "numberFormatType");
            return (Criteria) this;
        }

        public Criteria andNumberFormatTypeNotBetween(Byte value1, Byte value2) {
            addCriterion("number_format_type not between", value1, value2, "numberFormatType");
            return (Criteria) this;
        }

        public Criteria andBookOnlineNameIsNull() {
            addCriterion("book_online_name is null");
            return (Criteria) this;
        }

        public Criteria andBookOnlineNameIsNotNull() {
            addCriterion("book_online_name is not null");
            return (Criteria) this;
        }

        public Criteria andBookOnlineNameEqualTo(String value) {
            addCriterion("book_online_name =", value, "bookOnlineName");
            return (Criteria) this;
        }

        public Criteria andBookOnlineNameNotEqualTo(String value) {
            addCriterion("book_online_name <>", value, "bookOnlineName");
            return (Criteria) this;
        }

        public Criteria andBookOnlineNameGreaterThan(String value) {
            addCriterion("book_online_name >", value, "bookOnlineName");
            return (Criteria) this;
        }

        public Criteria andBookOnlineNameGreaterThanOrEqualTo(String value) {
            addCriterion("book_online_name >=", value, "bookOnlineName");
            return (Criteria) this;
        }

        public Criteria andBookOnlineNameLessThan(String value) {
            addCriterion("book_online_name <", value, "bookOnlineName");
            return (Criteria) this;
        }

        public Criteria andBookOnlineNameLessThanOrEqualTo(String value) {
            addCriterion("book_online_name <=", value, "bookOnlineName");
            return (Criteria) this;
        }

        public Criteria andBookOnlineNameLike(String value) {
            addCriterion("book_online_name like", value, "bookOnlineName");
            return (Criteria) this;
        }

        public Criteria andBookOnlineNameNotLike(String value) {
            addCriterion("book_online_name not like", value, "bookOnlineName");
            return (Criteria) this;
        }

        public Criteria andBookOnlineNameIn(List<String> values) {
            addCriterion("book_online_name in", values, "bookOnlineName");
            return (Criteria) this;
        }

        public Criteria andBookOnlineNameNotIn(List<String> values) {
            addCriterion("book_online_name not in", values, "bookOnlineName");
            return (Criteria) this;
        }

        public Criteria andBookOnlineNameBetween(String value1, String value2) {
            addCriterion("book_online_name between", value1, value2, "bookOnlineName");
            return (Criteria) this;
        }

        public Criteria andBookOnlineNameNotBetween(String value1, String value2) {
            addCriterion("book_online_name not between", value1, value2, "bookOnlineName");
            return (Criteria) this;
        }

        public Criteria andAppTypeIsNull() {
            addCriterion("app_type is null");
            return (Criteria) this;
        }

        public Criteria andAppTypeIsNotNull() {
            addCriterion("app_type is not null");
            return (Criteria) this;
        }

        public Criteria andAppTypeEqualTo(Byte value) {
            addCriterion("app_type =", value, "appType");
            return (Criteria) this;
        }

        public Criteria andAppTypeNotEqualTo(Byte value) {
            addCriterion("app_type <>", value, "appType");
            return (Criteria) this;
        }

        public Criteria andAppTypeGreaterThan(Byte value) {
            addCriterion("app_type >", value, "appType");
            return (Criteria) this;
        }

        public Criteria andAppTypeGreaterThanOrEqualTo(Byte value) {
            addCriterion("app_type >=", value, "appType");
            return (Criteria) this;
        }

        public Criteria andAppTypeLessThan(Byte value) {
            addCriterion("app_type <", value, "appType");
            return (Criteria) this;
        }

        public Criteria andAppTypeLessThanOrEqualTo(Byte value) {
            addCriterion("app_type <=", value, "appType");
            return (Criteria) this;
        }

        public Criteria andAppTypeIn(List<Byte> values) {
            addCriterion("app_type in", values, "appType");
            return (Criteria) this;
        }

        public Criteria andAppTypeNotIn(List<Byte> values) {
            addCriterion("app_type not in", values, "appType");
            return (Criteria) this;
        }

        public Criteria andAppTypeBetween(Byte value1, Byte value2) {
            addCriterion("app_type between", value1, value2, "appType");
            return (Criteria) this;
        }

        public Criteria andAppTypeNotBetween(Byte value1, Byte value2) {
            addCriterion("app_type not between", value1, value2, "appType");
            return (Criteria) this;
        }

        public Criteria andPrimaryPayTypeIsNull() {
            addCriterion("primary_pay_type is null");
            return (Criteria) this;
        }

        public Criteria andPrimaryPayTypeIsNotNull() {
            addCriterion("primary_pay_type is not null");
            return (Criteria) this;
        }

        public Criteria andPrimaryPayTypeEqualTo(Byte value) {
            addCriterion("primary_pay_type =", value, "primaryPayType");
            return (Criteria) this;
        }

        public Criteria andPrimaryPayTypeNotEqualTo(Byte value) {
            addCriterion("primary_pay_type <>", value, "primaryPayType");
            return (Criteria) this;
        }

        public Criteria andPrimaryPayTypeGreaterThan(Byte value) {
            addCriterion("primary_pay_type >", value, "primaryPayType");
            return (Criteria) this;
        }

        public Criteria andPrimaryPayTypeGreaterThanOrEqualTo(Byte value) {
            addCriterion("primary_pay_type >=", value, "primaryPayType");
            return (Criteria) this;
        }

        public Criteria andPrimaryPayTypeLessThan(Byte value) {
            addCriterion("primary_pay_type <", value, "primaryPayType");
            return (Criteria) this;
        }

        public Criteria andPrimaryPayTypeLessThanOrEqualTo(Byte value) {
            addCriterion("primary_pay_type <=", value, "primaryPayType");
            return (Criteria) this;
        }

        public Criteria andPrimaryPayTypeIn(List<Byte> values) {
            addCriterion("primary_pay_type in", values, "primaryPayType");
            return (Criteria) this;
        }

        public Criteria andPrimaryPayTypeNotIn(List<Byte> values) {
            addCriterion("primary_pay_type not in", values, "primaryPayType");
            return (Criteria) this;
        }

        public Criteria andPrimaryPayTypeBetween(Byte value1, Byte value2) {
            addCriterion("primary_pay_type between", value1, value2, "primaryPayType");
            return (Criteria) this;
        }

        public Criteria andPrimaryPayTypeNotBetween(Byte value1, Byte value2) {
            addCriterion("primary_pay_type not between", value1, value2, "primaryPayType");
            return (Criteria) this;
        }

        public Criteria andSourceIsNull() {
            addCriterion("source is null");
            return (Criteria) this;
        }

        public Criteria andSourceIsNotNull() {
            addCriterion("source is not null");
            return (Criteria) this;
        }

        public Criteria andSourceEqualTo(Byte value) {
            addCriterion("source =", value, "source");
            return (Criteria) this;
        }

        public Criteria andSourceNotEqualTo(Byte value) {
            addCriterion("source <>", value, "source");
            return (Criteria) this;
        }

        public Criteria andSourceGreaterThan(Byte value) {
            addCriterion("source >", value, "source");
            return (Criteria) this;
        }

        public Criteria andSourceGreaterThanOrEqualTo(Byte value) {
            addCriterion("source >=", value, "source");
            return (Criteria) this;
        }

        public Criteria andSourceLessThan(Byte value) {
            addCriterion("source <", value, "source");
            return (Criteria) this;
        }

        public Criteria andSourceLessThanOrEqualTo(Byte value) {
            addCriterion("source <=", value, "source");
            return (Criteria) this;
        }

        public Criteria andSourceIn(List<Byte> values) {
            addCriterion("source in", values, "source");
            return (Criteria) this;
        }

        public Criteria andSourceNotIn(List<Byte> values) {
            addCriterion("source not in", values, "source");
            return (Criteria) this;
        }

        public Criteria andSourceBetween(Byte value1, Byte value2) {
            addCriterion("source between", value1, value2, "source");
            return (Criteria) this;
        }

        public Criteria andSourceNotBetween(Byte value1, Byte value2) {
            addCriterion("source not between", value1, value2, "source");
            return (Criteria) this;
        }

        public Criteria andClockInOutEnableIsNull() {
            addCriterion("clock_in_out_enable is null");
            return (Criteria) this;
        }

        public Criteria andClockInOutEnableIsNotNull() {
            addCriterion("clock_in_out_enable is not null");
            return (Criteria) this;
        }

        public Criteria andClockInOutEnableEqualTo(Byte value) {
            addCriterion("clock_in_out_enable =", value, "clockInOutEnable");
            return (Criteria) this;
        }

        public Criteria andClockInOutEnableNotEqualTo(Byte value) {
            addCriterion("clock_in_out_enable <>", value, "clockInOutEnable");
            return (Criteria) this;
        }

        public Criteria andClockInOutEnableGreaterThan(Byte value) {
            addCriterion("clock_in_out_enable >", value, "clockInOutEnable");
            return (Criteria) this;
        }

        public Criteria andClockInOutEnableGreaterThanOrEqualTo(Byte value) {
            addCriterion("clock_in_out_enable >=", value, "clockInOutEnable");
            return (Criteria) this;
        }

        public Criteria andClockInOutEnableLessThan(Byte value) {
            addCriterion("clock_in_out_enable <", value, "clockInOutEnable");
            return (Criteria) this;
        }

        public Criteria andClockInOutEnableLessThanOrEqualTo(Byte value) {
            addCriterion("clock_in_out_enable <=", value, "clockInOutEnable");
            return (Criteria) this;
        }

        public Criteria andClockInOutEnableIn(List<Byte> values) {
            addCriterion("clock_in_out_enable in", values, "clockInOutEnable");
            return (Criteria) this;
        }

        public Criteria andClockInOutEnableNotIn(List<Byte> values) {
            addCriterion("clock_in_out_enable not in", values, "clockInOutEnable");
            return (Criteria) this;
        }

        public Criteria andClockInOutEnableBetween(Byte value1, Byte value2) {
            addCriterion("clock_in_out_enable between", value1, value2, "clockInOutEnable");
            return (Criteria) this;
        }

        public Criteria andClockInOutEnableNotBetween(Byte value1, Byte value2) {
            addCriterion("clock_in_out_enable not between", value1, value2, "clockInOutEnable");
            return (Criteria) this;
        }

        public Criteria andClockInOutNotifyIsNull() {
            addCriterion("clock_in_out_notify is null");
            return (Criteria) this;
        }

        public Criteria andClockInOutNotifyIsNotNull() {
            addCriterion("clock_in_out_notify is not null");
            return (Criteria) this;
        }

        public Criteria andClockInOutNotifyEqualTo(Byte value) {
            addCriterion("clock_in_out_notify =", value, "clockInOutNotify");
            return (Criteria) this;
        }

        public Criteria andClockInOutNotifyNotEqualTo(Byte value) {
            addCriterion("clock_in_out_notify <>", value, "clockInOutNotify");
            return (Criteria) this;
        }

        public Criteria andClockInOutNotifyGreaterThan(Byte value) {
            addCriterion("clock_in_out_notify >", value, "clockInOutNotify");
            return (Criteria) this;
        }

        public Criteria andClockInOutNotifyGreaterThanOrEqualTo(Byte value) {
            addCriterion("clock_in_out_notify >=", value, "clockInOutNotify");
            return (Criteria) this;
        }

        public Criteria andClockInOutNotifyLessThan(Byte value) {
            addCriterion("clock_in_out_notify <", value, "clockInOutNotify");
            return (Criteria) this;
        }

        public Criteria andClockInOutNotifyLessThanOrEqualTo(Byte value) {
            addCriterion("clock_in_out_notify <=", value, "clockInOutNotify");
            return (Criteria) this;
        }

        public Criteria andClockInOutNotifyIn(List<Byte> values) {
            addCriterion("clock_in_out_notify in", values, "clockInOutNotify");
            return (Criteria) this;
        }

        public Criteria andClockInOutNotifyNotIn(List<Byte> values) {
            addCriterion("clock_in_out_notify not in", values, "clockInOutNotify");
            return (Criteria) this;
        }

        public Criteria andClockInOutNotifyBetween(Byte value1, Byte value2) {
            addCriterion("clock_in_out_notify between", value1, value2, "clockInOutNotify");
            return (Criteria) this;
        }

        public Criteria andClockInOutNotifyNotBetween(Byte value1, Byte value2) {
            addCriterion("clock_in_out_notify not between", value1, value2, "clockInOutNotify");
            return (Criteria) this;
        }

        public Criteria andIsEnableAccessCodeIsNull() {
            addCriterion("is_enable_access_code is null");
            return (Criteria) this;
        }

        public Criteria andIsEnableAccessCodeIsNotNull() {
            addCriterion("is_enable_access_code is not null");
            return (Criteria) this;
        }

        public Criteria andIsEnableAccessCodeEqualTo(Byte value) {
            addCriterion("is_enable_access_code =", value, "isEnableAccessCode");
            return (Criteria) this;
        }

        public Criteria andIsEnableAccessCodeNotEqualTo(Byte value) {
            addCriterion("is_enable_access_code <>", value, "isEnableAccessCode");
            return (Criteria) this;
        }

        public Criteria andIsEnableAccessCodeGreaterThan(Byte value) {
            addCriterion("is_enable_access_code >", value, "isEnableAccessCode");
            return (Criteria) this;
        }

        public Criteria andIsEnableAccessCodeGreaterThanOrEqualTo(Byte value) {
            addCriterion("is_enable_access_code >=", value, "isEnableAccessCode");
            return (Criteria) this;
        }

        public Criteria andIsEnableAccessCodeLessThan(Byte value) {
            addCriterion("is_enable_access_code <", value, "isEnableAccessCode");
            return (Criteria) this;
        }

        public Criteria andIsEnableAccessCodeLessThanOrEqualTo(Byte value) {
            addCriterion("is_enable_access_code <=", value, "isEnableAccessCode");
            return (Criteria) this;
        }

        public Criteria andIsEnableAccessCodeIn(List<Byte> values) {
            addCriterion("is_enable_access_code in", values, "isEnableAccessCode");
            return (Criteria) this;
        }

        public Criteria andIsEnableAccessCodeNotIn(List<Byte> values) {
            addCriterion("is_enable_access_code not in", values, "isEnableAccessCode");
            return (Criteria) this;
        }

        public Criteria andIsEnableAccessCodeBetween(Byte value1, Byte value2) {
            addCriterion("is_enable_access_code between", value1, value2, "isEnableAccessCode");
            return (Criteria) this;
        }

        public Criteria andIsEnableAccessCodeNotBetween(Byte value1, Byte value2) {
            addCriterion("is_enable_access_code not between", value1, value2, "isEnableAccessCode");
            return (Criteria) this;
        }

        public Criteria andSmartScheduleMaxDistIsNull() {
            addCriterion("smart_schedule_max_dist is null");
            return (Criteria) this;
        }

        public Criteria andSmartScheduleMaxDistIsNotNull() {
            addCriterion("smart_schedule_max_dist is not null");
            return (Criteria) this;
        }

        public Criteria andSmartScheduleMaxDistEqualTo(Integer value) {
            addCriterion("smart_schedule_max_dist =", value, "smartScheduleMaxDist");
            return (Criteria) this;
        }

        public Criteria andSmartScheduleMaxDistNotEqualTo(Integer value) {
            addCriterion("smart_schedule_max_dist <>", value, "smartScheduleMaxDist");
            return (Criteria) this;
        }

        public Criteria andSmartScheduleMaxDistGreaterThan(Integer value) {
            addCriterion("smart_schedule_max_dist >", value, "smartScheduleMaxDist");
            return (Criteria) this;
        }

        public Criteria andSmartScheduleMaxDistGreaterThanOrEqualTo(Integer value) {
            addCriterion("smart_schedule_max_dist >=", value, "smartScheduleMaxDist");
            return (Criteria) this;
        }

        public Criteria andSmartScheduleMaxDistLessThan(Integer value) {
            addCriterion("smart_schedule_max_dist <", value, "smartScheduleMaxDist");
            return (Criteria) this;
        }

        public Criteria andSmartScheduleMaxDistLessThanOrEqualTo(Integer value) {
            addCriterion("smart_schedule_max_dist <=", value, "smartScheduleMaxDist");
            return (Criteria) this;
        }

        public Criteria andSmartScheduleMaxDistIn(List<Integer> values) {
            addCriterion("smart_schedule_max_dist in", values, "smartScheduleMaxDist");
            return (Criteria) this;
        }

        public Criteria andSmartScheduleMaxDistNotIn(List<Integer> values) {
            addCriterion("smart_schedule_max_dist not in", values, "smartScheduleMaxDist");
            return (Criteria) this;
        }

        public Criteria andSmartScheduleMaxDistBetween(Integer value1, Integer value2) {
            addCriterion("smart_schedule_max_dist between", value1, value2, "smartScheduleMaxDist");
            return (Criteria) this;
        }

        public Criteria andSmartScheduleMaxDistNotBetween(Integer value1, Integer value2) {
            addCriterion("smart_schedule_max_dist not between", value1, value2, "smartScheduleMaxDist");
            return (Criteria) this;
        }

        public Criteria andSmartScheduleMaxTimeIsNull() {
            addCriterion("smart_schedule_max_time is null");
            return (Criteria) this;
        }

        public Criteria andSmartScheduleMaxTimeIsNotNull() {
            addCriterion("smart_schedule_max_time is not null");
            return (Criteria) this;
        }

        public Criteria andSmartScheduleMaxTimeEqualTo(Integer value) {
            addCriterion("smart_schedule_max_time =", value, "smartScheduleMaxTime");
            return (Criteria) this;
        }

        public Criteria andSmartScheduleMaxTimeNotEqualTo(Integer value) {
            addCriterion("smart_schedule_max_time <>", value, "smartScheduleMaxTime");
            return (Criteria) this;
        }

        public Criteria andSmartScheduleMaxTimeGreaterThan(Integer value) {
            addCriterion("smart_schedule_max_time >", value, "smartScheduleMaxTime");
            return (Criteria) this;
        }

        public Criteria andSmartScheduleMaxTimeGreaterThanOrEqualTo(Integer value) {
            addCriterion("smart_schedule_max_time >=", value, "smartScheduleMaxTime");
            return (Criteria) this;
        }

        public Criteria andSmartScheduleMaxTimeLessThan(Integer value) {
            addCriterion("smart_schedule_max_time <", value, "smartScheduleMaxTime");
            return (Criteria) this;
        }

        public Criteria andSmartScheduleMaxTimeLessThanOrEqualTo(Integer value) {
            addCriterion("smart_schedule_max_time <=", value, "smartScheduleMaxTime");
            return (Criteria) this;
        }

        public Criteria andSmartScheduleMaxTimeIn(List<Integer> values) {
            addCriterion("smart_schedule_max_time in", values, "smartScheduleMaxTime");
            return (Criteria) this;
        }

        public Criteria andSmartScheduleMaxTimeNotIn(List<Integer> values) {
            addCriterion("smart_schedule_max_time not in", values, "smartScheduleMaxTime");
            return (Criteria) this;
        }

        public Criteria andSmartScheduleMaxTimeBetween(Integer value1, Integer value2) {
            addCriterion("smart_schedule_max_time between", value1, value2, "smartScheduleMaxTime");
            return (Criteria) this;
        }

        public Criteria andSmartScheduleMaxTimeNotBetween(Integer value1, Integer value2) {
            addCriterion("smart_schedule_max_time not between", value1, value2, "smartScheduleMaxTime");
            return (Criteria) this;
        }

        public Criteria andServiceAreaEnableIsNull() {
            addCriterion("service_area_enable is null");
            return (Criteria) this;
        }

        public Criteria andServiceAreaEnableIsNotNull() {
            addCriterion("service_area_enable is not null");
            return (Criteria) this;
        }

        public Criteria andServiceAreaEnableEqualTo(Byte value) {
            addCriterion("service_area_enable =", value, "serviceAreaEnable");
            return (Criteria) this;
        }

        public Criteria andServiceAreaEnableNotEqualTo(Byte value) {
            addCriterion("service_area_enable <>", value, "serviceAreaEnable");
            return (Criteria) this;
        }

        public Criteria andServiceAreaEnableGreaterThan(Byte value) {
            addCriterion("service_area_enable >", value, "serviceAreaEnable");
            return (Criteria) this;
        }

        public Criteria andServiceAreaEnableGreaterThanOrEqualTo(Byte value) {
            addCriterion("service_area_enable >=", value, "serviceAreaEnable");
            return (Criteria) this;
        }

        public Criteria andServiceAreaEnableLessThan(Byte value) {
            addCriterion("service_area_enable <", value, "serviceAreaEnable");
            return (Criteria) this;
        }

        public Criteria andServiceAreaEnableLessThanOrEqualTo(Byte value) {
            addCriterion("service_area_enable <=", value, "serviceAreaEnable");
            return (Criteria) this;
        }

        public Criteria andServiceAreaEnableIn(List<Byte> values) {
            addCriterion("service_area_enable in", values, "serviceAreaEnable");
            return (Criteria) this;
        }

        public Criteria andServiceAreaEnableNotIn(List<Byte> values) {
            addCriterion("service_area_enable not in", values, "serviceAreaEnable");
            return (Criteria) this;
        }

        public Criteria andServiceAreaEnableBetween(Byte value1, Byte value2) {
            addCriterion("service_area_enable between", value1, value2, "serviceAreaEnable");
            return (Criteria) this;
        }

        public Criteria andServiceAreaEnableNotBetween(Byte value1, Byte value2) {
            addCriterion("service_area_enable not between", value1, value2, "serviceAreaEnable");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNull() {
            addCriterion("create_time is null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNotNull() {
            addCriterion("create_time is not null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeEqualTo(Long value) {
            addCriterion("create_time =", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotEqualTo(Long value) {
            addCriterion("create_time <>", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThan(Long value) {
            addCriterion("create_time >", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThanOrEqualTo(Long value) {
            addCriterion("create_time >=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThan(Long value) {
            addCriterion("create_time <", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThanOrEqualTo(Long value) {
            addCriterion("create_time <=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIn(List<Long> values) {
            addCriterion("create_time in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotIn(List<Long> values) {
            addCriterion("create_time not in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeBetween(Long value1, Long value2) {
            addCriterion("create_time between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotBetween(Long value1, Long value2) {
            addCriterion("create_time not between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNull() {
            addCriterion("update_time is null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNotNull() {
            addCriterion("update_time is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeEqualTo(Long value) {
            addCriterion("update_time =", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotEqualTo(Long value) {
            addCriterion("update_time <>", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThan(Long value) {
            addCriterion("update_time >", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThanOrEqualTo(Long value) {
            addCriterion("update_time >=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThan(Long value) {
            addCriterion("update_time <", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThanOrEqualTo(Long value) {
            addCriterion("update_time <=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIn(List<Long> values) {
            addCriterion("update_time in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotIn(List<Long> values) {
            addCriterion("update_time not in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeBetween(Long value1, Long value2) {
            addCriterion("update_time between", value1, value2, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotBetween(Long value1, Long value2) {
            addCriterion("update_time not between", value1, value2, "updateTime");
            return (Criteria) this;
        }

        public Criteria andFacebookIsNull() {
            addCriterion("facebook is null");
            return (Criteria) this;
        }

        public Criteria andFacebookIsNotNull() {
            addCriterion("facebook is not null");
            return (Criteria) this;
        }

        public Criteria andFacebookEqualTo(String value) {
            addCriterion("facebook =", value, "facebook");
            return (Criteria) this;
        }

        public Criteria andFacebookNotEqualTo(String value) {
            addCriterion("facebook <>", value, "facebook");
            return (Criteria) this;
        }

        public Criteria andFacebookGreaterThan(String value) {
            addCriterion("facebook >", value, "facebook");
            return (Criteria) this;
        }

        public Criteria andFacebookGreaterThanOrEqualTo(String value) {
            addCriterion("facebook >=", value, "facebook");
            return (Criteria) this;
        }

        public Criteria andFacebookLessThan(String value) {
            addCriterion("facebook <", value, "facebook");
            return (Criteria) this;
        }

        public Criteria andFacebookLessThanOrEqualTo(String value) {
            addCriterion("facebook <=", value, "facebook");
            return (Criteria) this;
        }

        public Criteria andFacebookLike(String value) {
            addCriterion("facebook like", value, "facebook");
            return (Criteria) this;
        }

        public Criteria andFacebookNotLike(String value) {
            addCriterion("facebook not like", value, "facebook");
            return (Criteria) this;
        }

        public Criteria andFacebookIn(List<String> values) {
            addCriterion("facebook in", values, "facebook");
            return (Criteria) this;
        }

        public Criteria andFacebookNotIn(List<String> values) {
            addCriterion("facebook not in", values, "facebook");
            return (Criteria) this;
        }

        public Criteria andFacebookBetween(String value1, String value2) {
            addCriterion("facebook between", value1, value2, "facebook");
            return (Criteria) this;
        }

        public Criteria andFacebookNotBetween(String value1, String value2) {
            addCriterion("facebook not between", value1, value2, "facebook");
            return (Criteria) this;
        }

        public Criteria andInstagramIsNull() {
            addCriterion("instagram is null");
            return (Criteria) this;
        }

        public Criteria andInstagramIsNotNull() {
            addCriterion("instagram is not null");
            return (Criteria) this;
        }

        public Criteria andInstagramEqualTo(String value) {
            addCriterion("instagram =", value, "instagram");
            return (Criteria) this;
        }

        public Criteria andInstagramNotEqualTo(String value) {
            addCriterion("instagram <>", value, "instagram");
            return (Criteria) this;
        }

        public Criteria andInstagramGreaterThan(String value) {
            addCriterion("instagram >", value, "instagram");
            return (Criteria) this;
        }

        public Criteria andInstagramGreaterThanOrEqualTo(String value) {
            addCriterion("instagram >=", value, "instagram");
            return (Criteria) this;
        }

        public Criteria andInstagramLessThan(String value) {
            addCriterion("instagram <", value, "instagram");
            return (Criteria) this;
        }

        public Criteria andInstagramLessThanOrEqualTo(String value) {
            addCriterion("instagram <=", value, "instagram");
            return (Criteria) this;
        }

        public Criteria andInstagramLike(String value) {
            addCriterion("instagram like", value, "instagram");
            return (Criteria) this;
        }

        public Criteria andInstagramNotLike(String value) {
            addCriterion("instagram not like", value, "instagram");
            return (Criteria) this;
        }

        public Criteria andInstagramIn(List<String> values) {
            addCriterion("instagram in", values, "instagram");
            return (Criteria) this;
        }

        public Criteria andInstagramNotIn(List<String> values) {
            addCriterion("instagram not in", values, "instagram");
            return (Criteria) this;
        }

        public Criteria andInstagramBetween(String value1, String value2) {
            addCriterion("instagram between", value1, value2, "instagram");
            return (Criteria) this;
        }

        public Criteria andInstagramNotBetween(String value1, String value2) {
            addCriterion("instagram not between", value1, value2, "instagram");
            return (Criteria) this;
        }

        public Criteria andGoogleIsNull() {
            addCriterion("google is null");
            return (Criteria) this;
        }

        public Criteria andGoogleIsNotNull() {
            addCriterion("google is not null");
            return (Criteria) this;
        }

        public Criteria andGoogleEqualTo(String value) {
            addCriterion("google =", value, "google");
            return (Criteria) this;
        }

        public Criteria andGoogleNotEqualTo(String value) {
            addCriterion("google <>", value, "google");
            return (Criteria) this;
        }

        public Criteria andGoogleGreaterThan(String value) {
            addCriterion("google >", value, "google");
            return (Criteria) this;
        }

        public Criteria andGoogleGreaterThanOrEqualTo(String value) {
            addCriterion("google >=", value, "google");
            return (Criteria) this;
        }

        public Criteria andGoogleLessThan(String value) {
            addCriterion("google <", value, "google");
            return (Criteria) this;
        }

        public Criteria andGoogleLessThanOrEqualTo(String value) {
            addCriterion("google <=", value, "google");
            return (Criteria) this;
        }

        public Criteria andGoogleLike(String value) {
            addCriterion("google like", value, "google");
            return (Criteria) this;
        }

        public Criteria andGoogleNotLike(String value) {
            addCriterion("google not like", value, "google");
            return (Criteria) this;
        }

        public Criteria andGoogleIn(List<String> values) {
            addCriterion("google in", values, "google");
            return (Criteria) this;
        }

        public Criteria andGoogleNotIn(List<String> values) {
            addCriterion("google not in", values, "google");
            return (Criteria) this;
        }

        public Criteria andGoogleBetween(String value1, String value2) {
            addCriterion("google between", value1, value2, "google");
            return (Criteria) this;
        }

        public Criteria andGoogleNotBetween(String value1, String value2) {
            addCriterion("google not between", value1, value2, "google");
            return (Criteria) this;
        }

        public Criteria andYelpIsNull() {
            addCriterion("yelp is null");
            return (Criteria) this;
        }

        public Criteria andYelpIsNotNull() {
            addCriterion("yelp is not null");
            return (Criteria) this;
        }

        public Criteria andYelpEqualTo(String value) {
            addCriterion("yelp =", value, "yelp");
            return (Criteria) this;
        }

        public Criteria andYelpNotEqualTo(String value) {
            addCriterion("yelp <>", value, "yelp");
            return (Criteria) this;
        }

        public Criteria andYelpGreaterThan(String value) {
            addCriterion("yelp >", value, "yelp");
            return (Criteria) this;
        }

        public Criteria andYelpGreaterThanOrEqualTo(String value) {
            addCriterion("yelp >=", value, "yelp");
            return (Criteria) this;
        }

        public Criteria andYelpLessThan(String value) {
            addCriterion("yelp <", value, "yelp");
            return (Criteria) this;
        }

        public Criteria andYelpLessThanOrEqualTo(String value) {
            addCriterion("yelp <=", value, "yelp");
            return (Criteria) this;
        }

        public Criteria andYelpLike(String value) {
            addCriterion("yelp like", value, "yelp");
            return (Criteria) this;
        }

        public Criteria andYelpNotLike(String value) {
            addCriterion("yelp not like", value, "yelp");
            return (Criteria) this;
        }

        public Criteria andYelpIn(List<String> values) {
            addCriterion("yelp in", values, "yelp");
            return (Criteria) this;
        }

        public Criteria andYelpNotIn(List<String> values) {
            addCriterion("yelp not in", values, "yelp");
            return (Criteria) this;
        }

        public Criteria andYelpBetween(String value1, String value2) {
            addCriterion("yelp between", value1, value2, "yelp");
            return (Criteria) this;
        }

        public Criteria andYelpNotBetween(String value1, String value2) {
            addCriterion("yelp not between", value1, value2, "yelp");
            return (Criteria) this;
        }

        public Criteria andSmartScheduleStartLatIsNull() {
            addCriterion("smart_schedule_start_lat is null");
            return (Criteria) this;
        }

        public Criteria andSmartScheduleStartLatIsNotNull() {
            addCriterion("smart_schedule_start_lat is not null");
            return (Criteria) this;
        }

        public Criteria andSmartScheduleStartLatEqualTo(String value) {
            addCriterion("smart_schedule_start_lat =", value, "smartScheduleStartLat");
            return (Criteria) this;
        }

        public Criteria andSmartScheduleStartLatNotEqualTo(String value) {
            addCriterion("smart_schedule_start_lat <>", value, "smartScheduleStartLat");
            return (Criteria) this;
        }

        public Criteria andSmartScheduleStartLatGreaterThan(String value) {
            addCriterion("smart_schedule_start_lat >", value, "smartScheduleStartLat");
            return (Criteria) this;
        }

        public Criteria andSmartScheduleStartLatGreaterThanOrEqualTo(String value) {
            addCriterion("smart_schedule_start_lat >=", value, "smartScheduleStartLat");
            return (Criteria) this;
        }

        public Criteria andSmartScheduleStartLatLessThan(String value) {
            addCriterion("smart_schedule_start_lat <", value, "smartScheduleStartLat");
            return (Criteria) this;
        }

        public Criteria andSmartScheduleStartLatLessThanOrEqualTo(String value) {
            addCriterion("smart_schedule_start_lat <=", value, "smartScheduleStartLat");
            return (Criteria) this;
        }

        public Criteria andSmartScheduleStartLatLike(String value) {
            addCriterion("smart_schedule_start_lat like", value, "smartScheduleStartLat");
            return (Criteria) this;
        }

        public Criteria andSmartScheduleStartLatNotLike(String value) {
            addCriterion("smart_schedule_start_lat not like", value, "smartScheduleStartLat");
            return (Criteria) this;
        }

        public Criteria andSmartScheduleStartLatIn(List<String> values) {
            addCriterion("smart_schedule_start_lat in", values, "smartScheduleStartLat");
            return (Criteria) this;
        }

        public Criteria andSmartScheduleStartLatNotIn(List<String> values) {
            addCriterion("smart_schedule_start_lat not in", values, "smartScheduleStartLat");
            return (Criteria) this;
        }

        public Criteria andSmartScheduleStartLatBetween(String value1, String value2) {
            addCriterion("smart_schedule_start_lat between", value1, value2, "smartScheduleStartLat");
            return (Criteria) this;
        }

        public Criteria andSmartScheduleStartLatNotBetween(String value1, String value2) {
            addCriterion("smart_schedule_start_lat not between", value1, value2, "smartScheduleStartLat");
            return (Criteria) this;
        }

        public Criteria andSmartScheduleStartLngIsNull() {
            addCriterion("smart_schedule_start_lng is null");
            return (Criteria) this;
        }

        public Criteria andSmartScheduleStartLngIsNotNull() {
            addCriterion("smart_schedule_start_lng is not null");
            return (Criteria) this;
        }

        public Criteria andSmartScheduleStartLngEqualTo(String value) {
            addCriterion("smart_schedule_start_lng =", value, "smartScheduleStartLng");
            return (Criteria) this;
        }

        public Criteria andSmartScheduleStartLngNotEqualTo(String value) {
            addCriterion("smart_schedule_start_lng <>", value, "smartScheduleStartLng");
            return (Criteria) this;
        }

        public Criteria andSmartScheduleStartLngGreaterThan(String value) {
            addCriterion("smart_schedule_start_lng >", value, "smartScheduleStartLng");
            return (Criteria) this;
        }

        public Criteria andSmartScheduleStartLngGreaterThanOrEqualTo(String value) {
            addCriterion("smart_schedule_start_lng >=", value, "smartScheduleStartLng");
            return (Criteria) this;
        }

        public Criteria andSmartScheduleStartLngLessThan(String value) {
            addCriterion("smart_schedule_start_lng <", value, "smartScheduleStartLng");
            return (Criteria) this;
        }

        public Criteria andSmartScheduleStartLngLessThanOrEqualTo(String value) {
            addCriterion("smart_schedule_start_lng <=", value, "smartScheduleStartLng");
            return (Criteria) this;
        }

        public Criteria andSmartScheduleStartLngLike(String value) {
            addCriterion("smart_schedule_start_lng like", value, "smartScheduleStartLng");
            return (Criteria) this;
        }

        public Criteria andSmartScheduleStartLngNotLike(String value) {
            addCriterion("smart_schedule_start_lng not like", value, "smartScheduleStartLng");
            return (Criteria) this;
        }

        public Criteria andSmartScheduleStartLngIn(List<String> values) {
            addCriterion("smart_schedule_start_lng in", values, "smartScheduleStartLng");
            return (Criteria) this;
        }

        public Criteria andSmartScheduleStartLngNotIn(List<String> values) {
            addCriterion("smart_schedule_start_lng not in", values, "smartScheduleStartLng");
            return (Criteria) this;
        }

        public Criteria andSmartScheduleStartLngBetween(String value1, String value2) {
            addCriterion("smart_schedule_start_lng between", value1, value2, "smartScheduleStartLng");
            return (Criteria) this;
        }

        public Criteria andSmartScheduleStartLngNotBetween(String value1, String value2) {
            addCriterion("smart_schedule_start_lng not between", value1, value2, "smartScheduleStartLng");
            return (Criteria) this;
        }

        public Criteria andSmartScheduleEndLatIsNull() {
            addCriterion("smart_schedule_end_lat is null");
            return (Criteria) this;
        }

        public Criteria andSmartScheduleEndLatIsNotNull() {
            addCriterion("smart_schedule_end_lat is not null");
            return (Criteria) this;
        }

        public Criteria andSmartScheduleEndLatEqualTo(String value) {
            addCriterion("smart_schedule_end_lat =", value, "smartScheduleEndLat");
            return (Criteria) this;
        }

        public Criteria andSmartScheduleEndLatNotEqualTo(String value) {
            addCriterion("smart_schedule_end_lat <>", value, "smartScheduleEndLat");
            return (Criteria) this;
        }

        public Criteria andSmartScheduleEndLatGreaterThan(String value) {
            addCriterion("smart_schedule_end_lat >", value, "smartScheduleEndLat");
            return (Criteria) this;
        }

        public Criteria andSmartScheduleEndLatGreaterThanOrEqualTo(String value) {
            addCriterion("smart_schedule_end_lat >=", value, "smartScheduleEndLat");
            return (Criteria) this;
        }

        public Criteria andSmartScheduleEndLatLessThan(String value) {
            addCriterion("smart_schedule_end_lat <", value, "smartScheduleEndLat");
            return (Criteria) this;
        }

        public Criteria andSmartScheduleEndLatLessThanOrEqualTo(String value) {
            addCriterion("smart_schedule_end_lat <=", value, "smartScheduleEndLat");
            return (Criteria) this;
        }

        public Criteria andSmartScheduleEndLatLike(String value) {
            addCriterion("smart_schedule_end_lat like", value, "smartScheduleEndLat");
            return (Criteria) this;
        }

        public Criteria andSmartScheduleEndLatNotLike(String value) {
            addCriterion("smart_schedule_end_lat not like", value, "smartScheduleEndLat");
            return (Criteria) this;
        }

        public Criteria andSmartScheduleEndLatIn(List<String> values) {
            addCriterion("smart_schedule_end_lat in", values, "smartScheduleEndLat");
            return (Criteria) this;
        }

        public Criteria andSmartScheduleEndLatNotIn(List<String> values) {
            addCriterion("smart_schedule_end_lat not in", values, "smartScheduleEndLat");
            return (Criteria) this;
        }

        public Criteria andSmartScheduleEndLatBetween(String value1, String value2) {
            addCriterion("smart_schedule_end_lat between", value1, value2, "smartScheduleEndLat");
            return (Criteria) this;
        }

        public Criteria andSmartScheduleEndLatNotBetween(String value1, String value2) {
            addCriterion("smart_schedule_end_lat not between", value1, value2, "smartScheduleEndLat");
            return (Criteria) this;
        }

        public Criteria andSmartScheduleEndLngIsNull() {
            addCriterion("smart_schedule_end_lng is null");
            return (Criteria) this;
        }

        public Criteria andSmartScheduleEndLngIsNotNull() {
            addCriterion("smart_schedule_end_lng is not null");
            return (Criteria) this;
        }

        public Criteria andSmartScheduleEndLngEqualTo(String value) {
            addCriterion("smart_schedule_end_lng =", value, "smartScheduleEndLng");
            return (Criteria) this;
        }

        public Criteria andSmartScheduleEndLngNotEqualTo(String value) {
            addCriterion("smart_schedule_end_lng <>", value, "smartScheduleEndLng");
            return (Criteria) this;
        }

        public Criteria andSmartScheduleEndLngGreaterThan(String value) {
            addCriterion("smart_schedule_end_lng >", value, "smartScheduleEndLng");
            return (Criteria) this;
        }

        public Criteria andSmartScheduleEndLngGreaterThanOrEqualTo(String value) {
            addCriterion("smart_schedule_end_lng >=", value, "smartScheduleEndLng");
            return (Criteria) this;
        }

        public Criteria andSmartScheduleEndLngLessThan(String value) {
            addCriterion("smart_schedule_end_lng <", value, "smartScheduleEndLng");
            return (Criteria) this;
        }

        public Criteria andSmartScheduleEndLngLessThanOrEqualTo(String value) {
            addCriterion("smart_schedule_end_lng <=", value, "smartScheduleEndLng");
            return (Criteria) this;
        }

        public Criteria andSmartScheduleEndLngLike(String value) {
            addCriterion("smart_schedule_end_lng like", value, "smartScheduleEndLng");
            return (Criteria) this;
        }

        public Criteria andSmartScheduleEndLngNotLike(String value) {
            addCriterion("smart_schedule_end_lng not like", value, "smartScheduleEndLng");
            return (Criteria) this;
        }

        public Criteria andSmartScheduleEndLngIn(List<String> values) {
            addCriterion("smart_schedule_end_lng in", values, "smartScheduleEndLng");
            return (Criteria) this;
        }

        public Criteria andSmartScheduleEndLngNotIn(List<String> values) {
            addCriterion("smart_schedule_end_lng not in", values, "smartScheduleEndLng");
            return (Criteria) this;
        }

        public Criteria andSmartScheduleEndLngBetween(String value1, String value2) {
            addCriterion("smart_schedule_end_lng between", value1, value2, "smartScheduleEndLng");
            return (Criteria) this;
        }

        public Criteria andSmartScheduleEndLngNotBetween(String value1, String value2) {
            addCriterion("smart_schedule_end_lng not between", value1, value2, "smartScheduleEndLng");
            return (Criteria) this;
        }

        public Criteria andServiceRangeMaxDistIsNull() {
            addCriterion("service_range_max_dist is null");
            return (Criteria) this;
        }

        public Criteria andServiceRangeMaxDistIsNotNull() {
            addCriterion("service_range_max_dist is not null");
            return (Criteria) this;
        }

        public Criteria andServiceRangeMaxDistEqualTo(Integer value) {
            addCriterion("service_range_max_dist =", value, "serviceRangeMaxDist");
            return (Criteria) this;
        }

        public Criteria andServiceRangeMaxDistNotEqualTo(Integer value) {
            addCriterion("service_range_max_dist <>", value, "serviceRangeMaxDist");
            return (Criteria) this;
        }

        public Criteria andServiceRangeMaxDistGreaterThan(Integer value) {
            addCriterion("service_range_max_dist >", value, "serviceRangeMaxDist");
            return (Criteria) this;
        }

        public Criteria andServiceRangeMaxDistGreaterThanOrEqualTo(Integer value) {
            addCriterion("service_range_max_dist >=", value, "serviceRangeMaxDist");
            return (Criteria) this;
        }

        public Criteria andServiceRangeMaxDistLessThan(Integer value) {
            addCriterion("service_range_max_dist <", value, "serviceRangeMaxDist");
            return (Criteria) this;
        }

        public Criteria andServiceRangeMaxDistLessThanOrEqualTo(Integer value) {
            addCriterion("service_range_max_dist <=", value, "serviceRangeMaxDist");
            return (Criteria) this;
        }

        public Criteria andServiceRangeMaxDistIn(List<Integer> values) {
            addCriterion("service_range_max_dist in", values, "serviceRangeMaxDist");
            return (Criteria) this;
        }

        public Criteria andServiceRangeMaxDistNotIn(List<Integer> values) {
            addCriterion("service_range_max_dist not in", values, "serviceRangeMaxDist");
            return (Criteria) this;
        }

        public Criteria andServiceRangeMaxDistBetween(Integer value1, Integer value2) {
            addCriterion("service_range_max_dist between", value1, value2, "serviceRangeMaxDist");
            return (Criteria) this;
        }

        public Criteria andServiceRangeMaxDistNotBetween(Integer value1, Integer value2) {
            addCriterion("service_range_max_dist not between", value1, value2, "serviceRangeMaxDist");
            return (Criteria) this;
        }

        public Criteria andServiceRangeMaxTimeIsNull() {
            addCriterion("service_range_max_time is null");
            return (Criteria) this;
        }

        public Criteria andServiceRangeMaxTimeIsNotNull() {
            addCriterion("service_range_max_time is not null");
            return (Criteria) this;
        }

        public Criteria andServiceRangeMaxTimeEqualTo(Integer value) {
            addCriterion("service_range_max_time =", value, "serviceRangeMaxTime");
            return (Criteria) this;
        }

        public Criteria andServiceRangeMaxTimeNotEqualTo(Integer value) {
            addCriterion("service_range_max_time <>", value, "serviceRangeMaxTime");
            return (Criteria) this;
        }

        public Criteria andServiceRangeMaxTimeGreaterThan(Integer value) {
            addCriterion("service_range_max_time >", value, "serviceRangeMaxTime");
            return (Criteria) this;
        }

        public Criteria andServiceRangeMaxTimeGreaterThanOrEqualTo(Integer value) {
            addCriterion("service_range_max_time >=", value, "serviceRangeMaxTime");
            return (Criteria) this;
        }

        public Criteria andServiceRangeMaxTimeLessThan(Integer value) {
            addCriterion("service_range_max_time <", value, "serviceRangeMaxTime");
            return (Criteria) this;
        }

        public Criteria andServiceRangeMaxTimeLessThanOrEqualTo(Integer value) {
            addCriterion("service_range_max_time <=", value, "serviceRangeMaxTime");
            return (Criteria) this;
        }

        public Criteria andServiceRangeMaxTimeIn(List<Integer> values) {
            addCriterion("service_range_max_time in", values, "serviceRangeMaxTime");
            return (Criteria) this;
        }

        public Criteria andServiceRangeMaxTimeNotIn(List<Integer> values) {
            addCriterion("service_range_max_time not in", values, "serviceRangeMaxTime");
            return (Criteria) this;
        }

        public Criteria andServiceRangeMaxTimeBetween(Integer value1, Integer value2) {
            addCriterion("service_range_max_time between", value1, value2, "serviceRangeMaxTime");
            return (Criteria) this;
        }

        public Criteria andServiceRangeMaxTimeNotBetween(Integer value1, Integer value2) {
            addCriterion("service_range_max_time not between", value1, value2, "serviceRangeMaxTime");
            return (Criteria) this;
        }

        public Criteria andSmartScheduleServiceRangeIsNull() {
            addCriterion("smart_schedule_service_range is null");
            return (Criteria) this;
        }

        public Criteria andSmartScheduleServiceRangeIsNotNull() {
            addCriterion("smart_schedule_service_range is not null");
            return (Criteria) this;
        }

        public Criteria andSmartScheduleServiceRangeEqualTo(Integer value) {
            addCriterion("smart_schedule_service_range =", value, "smartScheduleServiceRange");
            return (Criteria) this;
        }

        public Criteria andSmartScheduleServiceRangeNotEqualTo(Integer value) {
            addCriterion("smart_schedule_service_range <>", value, "smartScheduleServiceRange");
            return (Criteria) this;
        }

        public Criteria andSmartScheduleServiceRangeGreaterThan(Integer value) {
            addCriterion("smart_schedule_service_range >", value, "smartScheduleServiceRange");
            return (Criteria) this;
        }

        public Criteria andSmartScheduleServiceRangeGreaterThanOrEqualTo(Integer value) {
            addCriterion("smart_schedule_service_range >=", value, "smartScheduleServiceRange");
            return (Criteria) this;
        }

        public Criteria andSmartScheduleServiceRangeLessThan(Integer value) {
            addCriterion("smart_schedule_service_range <", value, "smartScheduleServiceRange");
            return (Criteria) this;
        }

        public Criteria andSmartScheduleServiceRangeLessThanOrEqualTo(Integer value) {
            addCriterion("smart_schedule_service_range <=", value, "smartScheduleServiceRange");
            return (Criteria) this;
        }

        public Criteria andSmartScheduleServiceRangeIn(List<Integer> values) {
            addCriterion("smart_schedule_service_range in", values, "smartScheduleServiceRange");
            return (Criteria) this;
        }

        public Criteria andSmartScheduleServiceRangeNotIn(List<Integer> values) {
            addCriterion("smart_schedule_service_range not in", values, "smartScheduleServiceRange");
            return (Criteria) this;
        }

        public Criteria andSmartScheduleServiceRangeBetween(Integer value1, Integer value2) {
            addCriterion("smart_schedule_service_range between", value1, value2, "smartScheduleServiceRange");
            return (Criteria) this;
        }

        public Criteria andSmartScheduleServiceRangeNotBetween(Integer value1, Integer value2) {
            addCriterion("smart_schedule_service_range not between", value1, value2, "smartScheduleServiceRange");
            return (Criteria) this;
        }

        public Criteria andSmartScheduleStartAddrIsNull() {
            addCriterion("smart_schedule_start_addr is null");
            return (Criteria) this;
        }

        public Criteria andSmartScheduleStartAddrIsNotNull() {
            addCriterion("smart_schedule_start_addr is not null");
            return (Criteria) this;
        }

        public Criteria andSmartScheduleStartAddrEqualTo(String value) {
            addCriterion("smart_schedule_start_addr =", value, "smartScheduleStartAddr");
            return (Criteria) this;
        }

        public Criteria andSmartScheduleStartAddrNotEqualTo(String value) {
            addCriterion("smart_schedule_start_addr <>", value, "smartScheduleStartAddr");
            return (Criteria) this;
        }

        public Criteria andSmartScheduleStartAddrGreaterThan(String value) {
            addCriterion("smart_schedule_start_addr >", value, "smartScheduleStartAddr");
            return (Criteria) this;
        }

        public Criteria andSmartScheduleStartAddrGreaterThanOrEqualTo(String value) {
            addCriterion("smart_schedule_start_addr >=", value, "smartScheduleStartAddr");
            return (Criteria) this;
        }

        public Criteria andSmartScheduleStartAddrLessThan(String value) {
            addCriterion("smart_schedule_start_addr <", value, "smartScheduleStartAddr");
            return (Criteria) this;
        }

        public Criteria andSmartScheduleStartAddrLessThanOrEqualTo(String value) {
            addCriterion("smart_schedule_start_addr <=", value, "smartScheduleStartAddr");
            return (Criteria) this;
        }

        public Criteria andSmartScheduleStartAddrLike(String value) {
            addCriterion("smart_schedule_start_addr like", value, "smartScheduleStartAddr");
            return (Criteria) this;
        }

        public Criteria andSmartScheduleStartAddrNotLike(String value) {
            addCriterion("smart_schedule_start_addr not like", value, "smartScheduleStartAddr");
            return (Criteria) this;
        }

        public Criteria andSmartScheduleStartAddrIn(List<String> values) {
            addCriterion("smart_schedule_start_addr in", values, "smartScheduleStartAddr");
            return (Criteria) this;
        }

        public Criteria andSmartScheduleStartAddrNotIn(List<String> values) {
            addCriterion("smart_schedule_start_addr not in", values, "smartScheduleStartAddr");
            return (Criteria) this;
        }

        public Criteria andSmartScheduleStartAddrBetween(String value1, String value2) {
            addCriterion("smart_schedule_start_addr between", value1, value2, "smartScheduleStartAddr");
            return (Criteria) this;
        }

        public Criteria andSmartScheduleStartAddrNotBetween(String value1, String value2) {
            addCriterion("smart_schedule_start_addr not between", value1, value2, "smartScheduleStartAddr");
            return (Criteria) this;
        }

        public Criteria andSmartScheduleEndAddrIsNull() {
            addCriterion("smart_schedule_end_addr is null");
            return (Criteria) this;
        }

        public Criteria andSmartScheduleEndAddrIsNotNull() {
            addCriterion("smart_schedule_end_addr is not null");
            return (Criteria) this;
        }

        public Criteria andSmartScheduleEndAddrEqualTo(String value) {
            addCriterion("smart_schedule_end_addr =", value, "smartScheduleEndAddr");
            return (Criteria) this;
        }

        public Criteria andSmartScheduleEndAddrNotEqualTo(String value) {
            addCriterion("smart_schedule_end_addr <>", value, "smartScheduleEndAddr");
            return (Criteria) this;
        }

        public Criteria andSmartScheduleEndAddrGreaterThan(String value) {
            addCriterion("smart_schedule_end_addr >", value, "smartScheduleEndAddr");
            return (Criteria) this;
        }

        public Criteria andSmartScheduleEndAddrGreaterThanOrEqualTo(String value) {
            addCriterion("smart_schedule_end_addr >=", value, "smartScheduleEndAddr");
            return (Criteria) this;
        }

        public Criteria andSmartScheduleEndAddrLessThan(String value) {
            addCriterion("smart_schedule_end_addr <", value, "smartScheduleEndAddr");
            return (Criteria) this;
        }

        public Criteria andSmartScheduleEndAddrLessThanOrEqualTo(String value) {
            addCriterion("smart_schedule_end_addr <=", value, "smartScheduleEndAddr");
            return (Criteria) this;
        }

        public Criteria andSmartScheduleEndAddrLike(String value) {
            addCriterion("smart_schedule_end_addr like", value, "smartScheduleEndAddr");
            return (Criteria) this;
        }

        public Criteria andSmartScheduleEndAddrNotLike(String value) {
            addCriterion("smart_schedule_end_addr not like", value, "smartScheduleEndAddr");
            return (Criteria) this;
        }

        public Criteria andSmartScheduleEndAddrIn(List<String> values) {
            addCriterion("smart_schedule_end_addr in", values, "smartScheduleEndAddr");
            return (Criteria) this;
        }

        public Criteria andSmartScheduleEndAddrNotIn(List<String> values) {
            addCriterion("smart_schedule_end_addr not in", values, "smartScheduleEndAddr");
            return (Criteria) this;
        }

        public Criteria andSmartScheduleEndAddrBetween(String value1, String value2) {
            addCriterion("smart_schedule_end_addr between", value1, value2, "smartScheduleEndAddr");
            return (Criteria) this;
        }

        public Criteria andSmartScheduleEndAddrNotBetween(String value1, String value2) {
            addCriterion("smart_schedule_end_addr not between", value1, value2, "smartScheduleEndAddr");
            return (Criteria) this;
        }

        public Criteria andServiceRangeCenterAddrIsNull() {
            addCriterion("service_range_center_addr is null");
            return (Criteria) this;
        }

        public Criteria andServiceRangeCenterAddrIsNotNull() {
            addCriterion("service_range_center_addr is not null");
            return (Criteria) this;
        }

        public Criteria andServiceRangeCenterAddrEqualTo(String value) {
            addCriterion("service_range_center_addr =", value, "serviceRangeCenterAddr");
            return (Criteria) this;
        }

        public Criteria andServiceRangeCenterAddrNotEqualTo(String value) {
            addCriterion("service_range_center_addr <>", value, "serviceRangeCenterAddr");
            return (Criteria) this;
        }

        public Criteria andServiceRangeCenterAddrGreaterThan(String value) {
            addCriterion("service_range_center_addr >", value, "serviceRangeCenterAddr");
            return (Criteria) this;
        }

        public Criteria andServiceRangeCenterAddrGreaterThanOrEqualTo(String value) {
            addCriterion("service_range_center_addr >=", value, "serviceRangeCenterAddr");
            return (Criteria) this;
        }

        public Criteria andServiceRangeCenterAddrLessThan(String value) {
            addCriterion("service_range_center_addr <", value, "serviceRangeCenterAddr");
            return (Criteria) this;
        }

        public Criteria andServiceRangeCenterAddrLessThanOrEqualTo(String value) {
            addCriterion("service_range_center_addr <=", value, "serviceRangeCenterAddr");
            return (Criteria) this;
        }

        public Criteria andServiceRangeCenterAddrLike(String value) {
            addCriterion("service_range_center_addr like", value, "serviceRangeCenterAddr");
            return (Criteria) this;
        }

        public Criteria andServiceRangeCenterAddrNotLike(String value) {
            addCriterion("service_range_center_addr not like", value, "serviceRangeCenterAddr");
            return (Criteria) this;
        }

        public Criteria andServiceRangeCenterAddrIn(List<String> values) {
            addCriterion("service_range_center_addr in", values, "serviceRangeCenterAddr");
            return (Criteria) this;
        }

        public Criteria andServiceRangeCenterAddrNotIn(List<String> values) {
            addCriterion("service_range_center_addr not in", values, "serviceRangeCenterAddr");
            return (Criteria) this;
        }

        public Criteria andServiceRangeCenterAddrBetween(String value1, String value2) {
            addCriterion("service_range_center_addr between", value1, value2, "serviceRangeCenterAddr");
            return (Criteria) this;
        }

        public Criteria andServiceRangeCenterAddrNotBetween(String value1, String value2) {
            addCriterion("service_range_center_addr not between", value1, value2, "serviceRangeCenterAddr");
            return (Criteria) this;
        }

        public Criteria andServiceRangeCenterLatIsNull() {
            addCriterion("service_range_center_lat is null");
            return (Criteria) this;
        }

        public Criteria andServiceRangeCenterLatIsNotNull() {
            addCriterion("service_range_center_lat is not null");
            return (Criteria) this;
        }

        public Criteria andServiceRangeCenterLatEqualTo(String value) {
            addCriterion("service_range_center_lat =", value, "serviceRangeCenterLat");
            return (Criteria) this;
        }

        public Criteria andServiceRangeCenterLatNotEqualTo(String value) {
            addCriterion("service_range_center_lat <>", value, "serviceRangeCenterLat");
            return (Criteria) this;
        }

        public Criteria andServiceRangeCenterLatGreaterThan(String value) {
            addCriterion("service_range_center_lat >", value, "serviceRangeCenterLat");
            return (Criteria) this;
        }

        public Criteria andServiceRangeCenterLatGreaterThanOrEqualTo(String value) {
            addCriterion("service_range_center_lat >=", value, "serviceRangeCenterLat");
            return (Criteria) this;
        }

        public Criteria andServiceRangeCenterLatLessThan(String value) {
            addCriterion("service_range_center_lat <", value, "serviceRangeCenterLat");
            return (Criteria) this;
        }

        public Criteria andServiceRangeCenterLatLessThanOrEqualTo(String value) {
            addCriterion("service_range_center_lat <=", value, "serviceRangeCenterLat");
            return (Criteria) this;
        }

        public Criteria andServiceRangeCenterLatLike(String value) {
            addCriterion("service_range_center_lat like", value, "serviceRangeCenterLat");
            return (Criteria) this;
        }

        public Criteria andServiceRangeCenterLatNotLike(String value) {
            addCriterion("service_range_center_lat not like", value, "serviceRangeCenterLat");
            return (Criteria) this;
        }

        public Criteria andServiceRangeCenterLatIn(List<String> values) {
            addCriterion("service_range_center_lat in", values, "serviceRangeCenterLat");
            return (Criteria) this;
        }

        public Criteria andServiceRangeCenterLatNotIn(List<String> values) {
            addCriterion("service_range_center_lat not in", values, "serviceRangeCenterLat");
            return (Criteria) this;
        }

        public Criteria andServiceRangeCenterLatBetween(String value1, String value2) {
            addCriterion("service_range_center_lat between", value1, value2, "serviceRangeCenterLat");
            return (Criteria) this;
        }

        public Criteria andServiceRangeCenterLatNotBetween(String value1, String value2) {
            addCriterion("service_range_center_lat not between", value1, value2, "serviceRangeCenterLat");
            return (Criteria) this;
        }

        public Criteria andServiceRangeCenterLngIsNull() {
            addCriterion("service_range_center_lng is null");
            return (Criteria) this;
        }

        public Criteria andServiceRangeCenterLngIsNotNull() {
            addCriterion("service_range_center_lng is not null");
            return (Criteria) this;
        }

        public Criteria andServiceRangeCenterLngEqualTo(String value) {
            addCriterion("service_range_center_lng =", value, "serviceRangeCenterLng");
            return (Criteria) this;
        }

        public Criteria andServiceRangeCenterLngNotEqualTo(String value) {
            addCriterion("service_range_center_lng <>", value, "serviceRangeCenterLng");
            return (Criteria) this;
        }

        public Criteria andServiceRangeCenterLngGreaterThan(String value) {
            addCriterion("service_range_center_lng >", value, "serviceRangeCenterLng");
            return (Criteria) this;
        }

        public Criteria andServiceRangeCenterLngGreaterThanOrEqualTo(String value) {
            addCriterion("service_range_center_lng >=", value, "serviceRangeCenterLng");
            return (Criteria) this;
        }

        public Criteria andServiceRangeCenterLngLessThan(String value) {
            addCriterion("service_range_center_lng <", value, "serviceRangeCenterLng");
            return (Criteria) this;
        }

        public Criteria andServiceRangeCenterLngLessThanOrEqualTo(String value) {
            addCriterion("service_range_center_lng <=", value, "serviceRangeCenterLng");
            return (Criteria) this;
        }

        public Criteria andServiceRangeCenterLngLike(String value) {
            addCriterion("service_range_center_lng like", value, "serviceRangeCenterLng");
            return (Criteria) this;
        }

        public Criteria andServiceRangeCenterLngNotLike(String value) {
            addCriterion("service_range_center_lng not like", value, "serviceRangeCenterLng");
            return (Criteria) this;
        }

        public Criteria andServiceRangeCenterLngIn(List<String> values) {
            addCriterion("service_range_center_lng in", values, "serviceRangeCenterLng");
            return (Criteria) this;
        }

        public Criteria andServiceRangeCenterLngNotIn(List<String> values) {
            addCriterion("service_range_center_lng not in", values, "serviceRangeCenterLng");
            return (Criteria) this;
        }

        public Criteria andServiceRangeCenterLngBetween(String value1, String value2) {
            addCriterion("service_range_center_lng between", value1, value2, "serviceRangeCenterLng");
            return (Criteria) this;
        }

        public Criteria andServiceRangeCenterLngNotBetween(String value1, String value2) {
            addCriterion("service_range_center_lng not between", value1, value2, "serviceRangeCenterLng");
            return (Criteria) this;
        }

        public Criteria andSourceFromIsNull() {
            addCriterion("source_from is null");
            return (Criteria) this;
        }

        public Criteria andSourceFromIsNotNull() {
            addCriterion("source_from is not null");
            return (Criteria) this;
        }

        public Criteria andSourceFromEqualTo(Byte value) {
            addCriterion("source_from =", value, "sourceFrom");
            return (Criteria) this;
        }

        public Criteria andSourceFromNotEqualTo(Byte value) {
            addCriterion("source_from <>", value, "sourceFrom");
            return (Criteria) this;
        }

        public Criteria andSourceFromGreaterThan(Byte value) {
            addCriterion("source_from >", value, "sourceFrom");
            return (Criteria) this;
        }

        public Criteria andSourceFromGreaterThanOrEqualTo(Byte value) {
            addCriterion("source_from >=", value, "sourceFrom");
            return (Criteria) this;
        }

        public Criteria andSourceFromLessThan(Byte value) {
            addCriterion("source_from <", value, "sourceFrom");
            return (Criteria) this;
        }

        public Criteria andSourceFromLessThanOrEqualTo(Byte value) {
            addCriterion("source_from <=", value, "sourceFrom");
            return (Criteria) this;
        }

        public Criteria andSourceFromIn(List<Byte> values) {
            addCriterion("source_from in", values, "sourceFrom");
            return (Criteria) this;
        }

        public Criteria andSourceFromNotIn(List<Byte> values) {
            addCriterion("source_from not in", values, "sourceFrom");
            return (Criteria) this;
        }

        public Criteria andSourceFromBetween(Byte value1, Byte value2) {
            addCriterion("source_from between", value1, value2, "sourceFrom");
            return (Criteria) this;
        }

        public Criteria andSourceFromNotBetween(Byte value1, Byte value2) {
            addCriterion("source_from not between", value1, value2, "sourceFrom");
            return (Criteria) this;
        }

        public Criteria andMessageSendByIsNull() {
            addCriterion("message_send_by is null");
            return (Criteria) this;
        }

        public Criteria andMessageSendByIsNotNull() {
            addCriterion("message_send_by is not null");
            return (Criteria) this;
        }

        public Criteria andMessageSendByEqualTo(Byte value) {
            addCriterion("message_send_by =", value, "messageSendBy");
            return (Criteria) this;
        }

        public Criteria andMessageSendByNotEqualTo(Byte value) {
            addCriterion("message_send_by <>", value, "messageSendBy");
            return (Criteria) this;
        }

        public Criteria andMessageSendByGreaterThan(Byte value) {
            addCriterion("message_send_by >", value, "messageSendBy");
            return (Criteria) this;
        }

        public Criteria andMessageSendByGreaterThanOrEqualTo(Byte value) {
            addCriterion("message_send_by >=", value, "messageSendBy");
            return (Criteria) this;
        }

        public Criteria andMessageSendByLessThan(Byte value) {
            addCriterion("message_send_by <", value, "messageSendBy");
            return (Criteria) this;
        }

        public Criteria andMessageSendByLessThanOrEqualTo(Byte value) {
            addCriterion("message_send_by <=", value, "messageSendBy");
            return (Criteria) this;
        }

        public Criteria andMessageSendByIn(List<Byte> values) {
            addCriterion("message_send_by in", values, "messageSendBy");
            return (Criteria) this;
        }

        public Criteria andMessageSendByNotIn(List<Byte> values) {
            addCriterion("message_send_by not in", values, "messageSendBy");
            return (Criteria) this;
        }

        public Criteria andMessageSendByBetween(Byte value1, Byte value2) {
            addCriterion("message_send_by between", value1, value2, "messageSendBy");
            return (Criteria) this;
        }

        public Criteria andMessageSendByNotBetween(Byte value1, Byte value2) {
            addCriterion("message_send_by not between", value1, value2, "messageSendBy");
            return (Criteria) this;
        }

        public Criteria andSendDailyIsNull() {
            addCriterion("send_daily is null");
            return (Criteria) this;
        }

        public Criteria andSendDailyIsNotNull() {
            addCriterion("send_daily is not null");
            return (Criteria) this;
        }

        public Criteria andSendDailyEqualTo(Byte value) {
            addCriterion("send_daily =", value, "sendDaily");
            return (Criteria) this;
        }

        public Criteria andSendDailyNotEqualTo(Byte value) {
            addCriterion("send_daily <>", value, "sendDaily");
            return (Criteria) this;
        }

        public Criteria andSendDailyGreaterThan(Byte value) {
            addCriterion("send_daily >", value, "sendDaily");
            return (Criteria) this;
        }

        public Criteria andSendDailyGreaterThanOrEqualTo(Byte value) {
            addCriterion("send_daily >=", value, "sendDaily");
            return (Criteria) this;
        }

        public Criteria andSendDailyLessThan(Byte value) {
            addCriterion("send_daily <", value, "sendDaily");
            return (Criteria) this;
        }

        public Criteria andSendDailyLessThanOrEqualTo(Byte value) {
            addCriterion("send_daily <=", value, "sendDaily");
            return (Criteria) this;
        }

        public Criteria andSendDailyIn(List<Byte> values) {
            addCriterion("send_daily in", values, "sendDaily");
            return (Criteria) this;
        }

        public Criteria andSendDailyNotIn(List<Byte> values) {
            addCriterion("send_daily not in", values, "sendDaily");
            return (Criteria) this;
        }

        public Criteria andSendDailyBetween(Byte value1, Byte value2) {
            addCriterion("send_daily between", value1, value2, "sendDaily");
            return (Criteria) this;
        }

        public Criteria andSendDailyNotBetween(Byte value1, Byte value2) {
            addCriterion("send_daily not between", value1, value2, "sendDaily");
            return (Criteria) this;
        }

        public Criteria andBusinessModeIsNull() {
            addCriterion("business_mode is null");
            return (Criteria) this;
        }

        public Criteria andBusinessModeIsNotNull() {
            addCriterion("business_mode is not null");
            return (Criteria) this;
        }

        public Criteria andBusinessModeEqualTo(Byte value) {
            addCriterion("business_mode =", value, "businessMode");
            return (Criteria) this;
        }

        public Criteria andBusinessModeNotEqualTo(Byte value) {
            addCriterion("business_mode <>", value, "businessMode");
            return (Criteria) this;
        }

        public Criteria andBusinessModeGreaterThan(Byte value) {
            addCriterion("business_mode >", value, "businessMode");
            return (Criteria) this;
        }

        public Criteria andBusinessModeGreaterThanOrEqualTo(Byte value) {
            addCriterion("business_mode >=", value, "businessMode");
            return (Criteria) this;
        }

        public Criteria andBusinessModeLessThan(Byte value) {
            addCriterion("business_mode <", value, "businessMode");
            return (Criteria) this;
        }

        public Criteria andBusinessModeLessThanOrEqualTo(Byte value) {
            addCriterion("business_mode <=", value, "businessMode");
            return (Criteria) this;
        }

        public Criteria andBusinessModeIn(List<Byte> values) {
            addCriterion("business_mode in", values, "businessMode");
            return (Criteria) this;
        }

        public Criteria andBusinessModeNotIn(List<Byte> values) {
            addCriterion("business_mode not in", values, "businessMode");
            return (Criteria) this;
        }

        public Criteria andBusinessModeBetween(Byte value1, Byte value2) {
            addCriterion("business_mode between", value1, value2, "businessMode");
            return (Criteria) this;
        }

        public Criteria andBusinessModeNotBetween(Byte value1, Byte value2) {
            addCriterion("business_mode not between", value1, value2, "businessMode");
            return (Criteria) this;
        }

        public Criteria andKnowAboutUsIsNull() {
            addCriterion("know_about_us is null");
            return (Criteria) this;
        }

        public Criteria andKnowAboutUsIsNotNull() {
            addCriterion("know_about_us is not null");
            return (Criteria) this;
        }

        public Criteria andKnowAboutUsEqualTo(String value) {
            addCriterion("know_about_us =", value, "knowAboutUs");
            return (Criteria) this;
        }

        public Criteria andKnowAboutUsNotEqualTo(String value) {
            addCriterion("know_about_us <>", value, "knowAboutUs");
            return (Criteria) this;
        }

        public Criteria andKnowAboutUsGreaterThan(String value) {
            addCriterion("know_about_us >", value, "knowAboutUs");
            return (Criteria) this;
        }

        public Criteria andKnowAboutUsGreaterThanOrEqualTo(String value) {
            addCriterion("know_about_us >=", value, "knowAboutUs");
            return (Criteria) this;
        }

        public Criteria andKnowAboutUsLessThan(String value) {
            addCriterion("know_about_us <", value, "knowAboutUs");
            return (Criteria) this;
        }

        public Criteria andKnowAboutUsLessThanOrEqualTo(String value) {
            addCriterion("know_about_us <=", value, "knowAboutUs");
            return (Criteria) this;
        }

        public Criteria andKnowAboutUsLike(String value) {
            addCriterion("know_about_us like", value, "knowAboutUs");
            return (Criteria) this;
        }

        public Criteria andKnowAboutUsNotLike(String value) {
            addCriterion("know_about_us not like", value, "knowAboutUs");
            return (Criteria) this;
        }

        public Criteria andKnowAboutUsIn(List<String> values) {
            addCriterion("know_about_us in", values, "knowAboutUs");
            return (Criteria) this;
        }

        public Criteria andKnowAboutUsNotIn(List<String> values) {
            addCriterion("know_about_us not in", values, "knowAboutUs");
            return (Criteria) this;
        }

        public Criteria andKnowAboutUsBetween(String value1, String value2) {
            addCriterion("know_about_us between", value1, value2, "knowAboutUs");
            return (Criteria) this;
        }

        public Criteria andKnowAboutUsNotBetween(String value1, String value2) {
            addCriterion("know_about_us not between", value1, value2, "knowAboutUs");
            return (Criteria) this;
        }

        public Criteria andApptPerWeekIsNull() {
            addCriterion("appt_per_week is null");
            return (Criteria) this;
        }

        public Criteria andApptPerWeekIsNotNull() {
            addCriterion("appt_per_week is not null");
            return (Criteria) this;
        }

        public Criteria andApptPerWeekEqualTo(Byte value) {
            addCriterion("appt_per_week =", value, "apptPerWeek");
            return (Criteria) this;
        }

        public Criteria andApptPerWeekNotEqualTo(Byte value) {
            addCriterion("appt_per_week <>", value, "apptPerWeek");
            return (Criteria) this;
        }

        public Criteria andApptPerWeekGreaterThan(Byte value) {
            addCriterion("appt_per_week >", value, "apptPerWeek");
            return (Criteria) this;
        }

        public Criteria andApptPerWeekGreaterThanOrEqualTo(Byte value) {
            addCriterion("appt_per_week >=", value, "apptPerWeek");
            return (Criteria) this;
        }

        public Criteria andApptPerWeekLessThan(Byte value) {
            addCriterion("appt_per_week <", value, "apptPerWeek");
            return (Criteria) this;
        }

        public Criteria andApptPerWeekLessThanOrEqualTo(Byte value) {
            addCriterion("appt_per_week <=", value, "apptPerWeek");
            return (Criteria) this;
        }

        public Criteria andApptPerWeekIn(List<Byte> values) {
            addCriterion("appt_per_week in", values, "apptPerWeek");
            return (Criteria) this;
        }

        public Criteria andApptPerWeekNotIn(List<Byte> values) {
            addCriterion("appt_per_week not in", values, "apptPerWeek");
            return (Criteria) this;
        }

        public Criteria andApptPerWeekBetween(Byte value1, Byte value2) {
            addCriterion("appt_per_week between", value1, value2, "apptPerWeek");
            return (Criteria) this;
        }

        public Criteria andApptPerWeekNotBetween(Byte value1, Byte value2) {
            addCriterion("appt_per_week not between", value1, value2, "apptPerWeek");
            return (Criteria) this;
        }

        public Criteria andBusinessYearsIsNull() {
            addCriterion("business_years is null");
            return (Criteria) this;
        }

        public Criteria andBusinessYearsIsNotNull() {
            addCriterion("business_years is not null");
            return (Criteria) this;
        }

        public Criteria andBusinessYearsEqualTo(Byte value) {
            addCriterion("business_years =", value, "businessYears");
            return (Criteria) this;
        }

        public Criteria andBusinessYearsNotEqualTo(Byte value) {
            addCriterion("business_years <>", value, "businessYears");
            return (Criteria) this;
        }

        public Criteria andBusinessYearsGreaterThan(Byte value) {
            addCriterion("business_years >", value, "businessYears");
            return (Criteria) this;
        }

        public Criteria andBusinessYearsGreaterThanOrEqualTo(Byte value) {
            addCriterion("business_years >=", value, "businessYears");
            return (Criteria) this;
        }

        public Criteria andBusinessYearsLessThan(Byte value) {
            addCriterion("business_years <", value, "businessYears");
            return (Criteria) this;
        }

        public Criteria andBusinessYearsLessThanOrEqualTo(Byte value) {
            addCriterion("business_years <=", value, "businessYears");
            return (Criteria) this;
        }

        public Criteria andBusinessYearsIn(List<Byte> values) {
            addCriterion("business_years in", values, "businessYears");
            return (Criteria) this;
        }

        public Criteria andBusinessYearsNotIn(List<Byte> values) {
            addCriterion("business_years not in", values, "businessYears");
            return (Criteria) this;
        }

        public Criteria andBusinessYearsBetween(Byte value1, Byte value2) {
            addCriterion("business_years between", value1, value2, "businessYears");
            return (Criteria) this;
        }

        public Criteria andBusinessYearsNotBetween(Byte value1, Byte value2) {
            addCriterion("business_years not between", value1, value2, "businessYears");
            return (Criteria) this;
        }

        public Criteria andMoveFromIsNull() {
            addCriterion("move_from is null");
            return (Criteria) this;
        }

        public Criteria andMoveFromIsNotNull() {
            addCriterion("move_from is not null");
            return (Criteria) this;
        }

        public Criteria andMoveFromEqualTo(Byte value) {
            addCriterion("move_from =", value, "moveFrom");
            return (Criteria) this;
        }

        public Criteria andMoveFromNotEqualTo(Byte value) {
            addCriterion("move_from <>", value, "moveFrom");
            return (Criteria) this;
        }

        public Criteria andMoveFromGreaterThan(Byte value) {
            addCriterion("move_from >", value, "moveFrom");
            return (Criteria) this;
        }

        public Criteria andMoveFromGreaterThanOrEqualTo(Byte value) {
            addCriterion("move_from >=", value, "moveFrom");
            return (Criteria) this;
        }

        public Criteria andMoveFromLessThan(Byte value) {
            addCriterion("move_from <", value, "moveFrom");
            return (Criteria) this;
        }

        public Criteria andMoveFromLessThanOrEqualTo(Byte value) {
            addCriterion("move_from <=", value, "moveFrom");
            return (Criteria) this;
        }

        public Criteria andMoveFromIn(List<Byte> values) {
            addCriterion("move_from in", values, "moveFrom");
            return (Criteria) this;
        }

        public Criteria andMoveFromNotIn(List<Byte> values) {
            addCriterion("move_from not in", values, "moveFrom");
            return (Criteria) this;
        }

        public Criteria andMoveFromBetween(Byte value1, Byte value2) {
            addCriterion("move_from between", value1, value2, "moveFrom");
            return (Criteria) this;
        }

        public Criteria andMoveFromNotBetween(Byte value1, Byte value2) {
            addCriterion("move_from not between", value1, value2, "moveFrom");
            return (Criteria) this;
        }

        public Criteria andRetailEnableIsNull() {
            addCriterion("retail_enable is null");
            return (Criteria) this;
        }

        public Criteria andRetailEnableIsNotNull() {
            addCriterion("retail_enable is not null");
            return (Criteria) this;
        }

        public Criteria andRetailEnableEqualTo(Byte value) {
            addCriterion("retail_enable =", value, "retailEnable");
            return (Criteria) this;
        }

        public Criteria andRetailEnableNotEqualTo(Byte value) {
            addCriterion("retail_enable <>", value, "retailEnable");
            return (Criteria) this;
        }

        public Criteria andRetailEnableGreaterThan(Byte value) {
            addCriterion("retail_enable >", value, "retailEnable");
            return (Criteria) this;
        }

        public Criteria andRetailEnableGreaterThanOrEqualTo(Byte value) {
            addCriterion("retail_enable >=", value, "retailEnable");
            return (Criteria) this;
        }

        public Criteria andRetailEnableLessThan(Byte value) {
            addCriterion("retail_enable <", value, "retailEnable");
            return (Criteria) this;
        }

        public Criteria andRetailEnableLessThanOrEqualTo(Byte value) {
            addCriterion("retail_enable <=", value, "retailEnable");
            return (Criteria) this;
        }

        public Criteria andRetailEnableIn(List<Byte> values) {
            addCriterion("retail_enable in", values, "retailEnable");
            return (Criteria) this;
        }

        public Criteria andRetailEnableNotIn(List<Byte> values) {
            addCriterion("retail_enable not in", values, "retailEnable");
            return (Criteria) this;
        }

        public Criteria andRetailEnableBetween(Byte value1, Byte value2) {
            addCriterion("retail_enable between", value1, value2, "retailEnable");
            return (Criteria) this;
        }

        public Criteria andRetailEnableNotBetween(Byte value1, Byte value2) {
            addCriterion("retail_enable not between", value1, value2, "retailEnable");
            return (Criteria) this;
        }

        public Criteria andNotificationSoundEnableIsNull() {
            addCriterion("notification_sound_enable is null");
            return (Criteria) this;
        }

        public Criteria andNotificationSoundEnableIsNotNull() {
            addCriterion("notification_sound_enable is not null");
            return (Criteria) this;
        }

        public Criteria andNotificationSoundEnableEqualTo(Byte value) {
            addCriterion("notification_sound_enable =", value, "notificationSoundEnable");
            return (Criteria) this;
        }

        public Criteria andNotificationSoundEnableNotEqualTo(Byte value) {
            addCriterion("notification_sound_enable <>", value, "notificationSoundEnable");
            return (Criteria) this;
        }

        public Criteria andNotificationSoundEnableGreaterThan(Byte value) {
            addCriterion("notification_sound_enable >", value, "notificationSoundEnable");
            return (Criteria) this;
        }

        public Criteria andNotificationSoundEnableGreaterThanOrEqualTo(Byte value) {
            addCriterion("notification_sound_enable >=", value, "notificationSoundEnable");
            return (Criteria) this;
        }

        public Criteria andNotificationSoundEnableLessThan(Byte value) {
            addCriterion("notification_sound_enable <", value, "notificationSoundEnable");
            return (Criteria) this;
        }

        public Criteria andNotificationSoundEnableLessThanOrEqualTo(Byte value) {
            addCriterion("notification_sound_enable <=", value, "notificationSoundEnable");
            return (Criteria) this;
        }

        public Criteria andNotificationSoundEnableIn(List<Byte> values) {
            addCriterion("notification_sound_enable in", values, "notificationSoundEnable");
            return (Criteria) this;
        }

        public Criteria andNotificationSoundEnableNotIn(List<Byte> values) {
            addCriterion("notification_sound_enable not in", values, "notificationSoundEnable");
            return (Criteria) this;
        }

        public Criteria andNotificationSoundEnableBetween(Byte value1, Byte value2) {
            addCriterion("notification_sound_enable between", value1, value2, "notificationSoundEnable");
            return (Criteria) this;
        }

        public Criteria andNotificationSoundEnableNotBetween(Byte value1, Byte value2) {
            addCriterion("notification_sound_enable not between", value1, value2, "notificationSoundEnable");
            return (Criteria) this;
        }

        public Criteria andContactEmailIsNull() {
            addCriterion("contact_email is null");
            return (Criteria) this;
        }

        public Criteria andContactEmailIsNotNull() {
            addCriterion("contact_email is not null");
            return (Criteria) this;
        }

        public Criteria andContactEmailEqualTo(String value) {
            addCriterion("contact_email =", value, "contactEmail");
            return (Criteria) this;
        }

        public Criteria andContactEmailNotEqualTo(String value) {
            addCriterion("contact_email <>", value, "contactEmail");
            return (Criteria) this;
        }

        public Criteria andContactEmailGreaterThan(String value) {
            addCriterion("contact_email >", value, "contactEmail");
            return (Criteria) this;
        }

        public Criteria andContactEmailGreaterThanOrEqualTo(String value) {
            addCriterion("contact_email >=", value, "contactEmail");
            return (Criteria) this;
        }

        public Criteria andContactEmailLessThan(String value) {
            addCriterion("contact_email <", value, "contactEmail");
            return (Criteria) this;
        }

        public Criteria andContactEmailLessThanOrEqualTo(String value) {
            addCriterion("contact_email <=", value, "contactEmail");
            return (Criteria) this;
        }

        public Criteria andContactEmailLike(String value) {
            addCriterion("contact_email like", value, "contactEmail");
            return (Criteria) this;
        }

        public Criteria andContactEmailNotLike(String value) {
            addCriterion("contact_email not like", value, "contactEmail");
            return (Criteria) this;
        }

        public Criteria andContactEmailIn(List<String> values) {
            addCriterion("contact_email in", values, "contactEmail");
            return (Criteria) this;
        }

        public Criteria andContactEmailNotIn(List<String> values) {
            addCriterion("contact_email not in", values, "contactEmail");
            return (Criteria) this;
        }

        public Criteria andContactEmailBetween(String value1, String value2) {
            addCriterion("contact_email between", value1, value2, "contactEmail");
            return (Criteria) this;
        }

        public Criteria andContactEmailNotBetween(String value1, String value2) {
            addCriterion("contact_email not between", value1, value2, "contactEmail");
            return (Criteria) this;
        }

        public Criteria andInvitationCodeIsNull() {
            addCriterion("invitation_code is null");
            return (Criteria) this;
        }

        public Criteria andInvitationCodeIsNotNull() {
            addCriterion("invitation_code is not null");
            return (Criteria) this;
        }

        public Criteria andInvitationCodeEqualTo(String value) {
            addCriterion("invitation_code =", value, "invitationCode");
            return (Criteria) this;
        }

        public Criteria andInvitationCodeNotEqualTo(String value) {
            addCriterion("invitation_code <>", value, "invitationCode");
            return (Criteria) this;
        }

        public Criteria andInvitationCodeGreaterThan(String value) {
            addCriterion("invitation_code >", value, "invitationCode");
            return (Criteria) this;
        }

        public Criteria andInvitationCodeGreaterThanOrEqualTo(String value) {
            addCriterion("invitation_code >=", value, "invitationCode");
            return (Criteria) this;
        }

        public Criteria andInvitationCodeLessThan(String value) {
            addCriterion("invitation_code <", value, "invitationCode");
            return (Criteria) this;
        }

        public Criteria andInvitationCodeLessThanOrEqualTo(String value) {
            addCriterion("invitation_code <=", value, "invitationCode");
            return (Criteria) this;
        }

        public Criteria andInvitationCodeLike(String value) {
            addCriterion("invitation_code like", value, "invitationCode");
            return (Criteria) this;
        }

        public Criteria andInvitationCodeNotLike(String value) {
            addCriterion("invitation_code not like", value, "invitationCode");
            return (Criteria) this;
        }

        public Criteria andInvitationCodeIn(List<String> values) {
            addCriterion("invitation_code in", values, "invitationCode");
            return (Criteria) this;
        }

        public Criteria andInvitationCodeNotIn(List<String> values) {
            addCriterion("invitation_code not in", values, "invitationCode");
            return (Criteria) this;
        }

        public Criteria andInvitationCodeBetween(String value1, String value2) {
            addCriterion("invitation_code between", value1, value2, "invitationCode");
            return (Criteria) this;
        }

        public Criteria andInvitationCodeNotBetween(String value1, String value2) {
            addCriterion("invitation_code not between", value1, value2, "invitationCode");
            return (Criteria) this;
        }

        public Criteria andTiktokIsNull() {
            addCriterion("tiktok is null");
            return (Criteria) this;
        }

        public Criteria andTiktokIsNotNull() {
            addCriterion("tiktok is not null");
            return (Criteria) this;
        }

        public Criteria andTiktokEqualTo(String value) {
            addCriterion("tiktok =", value, "tiktok");
            return (Criteria) this;
        }

        public Criteria andTiktokNotEqualTo(String value) {
            addCriterion("tiktok <>", value, "tiktok");
            return (Criteria) this;
        }

        public Criteria andTiktokGreaterThan(String value) {
            addCriterion("tiktok >", value, "tiktok");
            return (Criteria) this;
        }

        public Criteria andTiktokGreaterThanOrEqualTo(String value) {
            addCriterion("tiktok >=", value, "tiktok");
            return (Criteria) this;
        }

        public Criteria andTiktokLessThan(String value) {
            addCriterion("tiktok <", value, "tiktok");
            return (Criteria) this;
        }

        public Criteria andTiktokLessThanOrEqualTo(String value) {
            addCriterion("tiktok <=", value, "tiktok");
            return (Criteria) this;
        }

        public Criteria andTiktokLike(String value) {
            addCriterion("tiktok like", value, "tiktok");
            return (Criteria) this;
        }

        public Criteria andTiktokNotLike(String value) {
            addCriterion("tiktok not like", value, "tiktok");
            return (Criteria) this;
        }

        public Criteria andTiktokIn(List<String> values) {
            addCriterion("tiktok in", values, "tiktok");
            return (Criteria) this;
        }

        public Criteria andTiktokNotIn(List<String> values) {
            addCriterion("tiktok not in", values, "tiktok");
            return (Criteria) this;
        }

        public Criteria andTiktokBetween(String value1, String value2) {
            addCriterion("tiktok between", value1, value2, "tiktok");
            return (Criteria) this;
        }

        public Criteria andTiktokNotBetween(String value1, String value2) {
            addCriterion("tiktok not between", value1, value2, "tiktok");
            return (Criteria) this;
        }

        public Criteria andStaffAvailabilityTypeIsNull() {
            addCriterion("staff_availability_type is null");
            return (Criteria) this;
        }

        public Criteria andStaffAvailabilityTypeIsNotNull() {
            addCriterion("staff_availability_type is not null");
            return (Criteria) this;
        }

        public Criteria andStaffAvailabilityTypeEqualTo(Integer value) {
            addCriterion("staff_availability_type =", value, "staffAvailabilityType");
            return (Criteria) this;
        }

        public Criteria andStaffAvailabilityTypeNotEqualTo(Integer value) {
            addCriterion("staff_availability_type <>", value, "staffAvailabilityType");
            return (Criteria) this;
        }

        public Criteria andStaffAvailabilityTypeGreaterThan(Integer value) {
            addCriterion("staff_availability_type >", value, "staffAvailabilityType");
            return (Criteria) this;
        }

        public Criteria andStaffAvailabilityTypeGreaterThanOrEqualTo(Integer value) {
            addCriterion("staff_availability_type >=", value, "staffAvailabilityType");
            return (Criteria) this;
        }

        public Criteria andStaffAvailabilityTypeLessThan(Integer value) {
            addCriterion("staff_availability_type <", value, "staffAvailabilityType");
            return (Criteria) this;
        }

        public Criteria andStaffAvailabilityTypeLessThanOrEqualTo(Integer value) {
            addCriterion("staff_availability_type <=", value, "staffAvailabilityType");
            return (Criteria) this;
        }

        public Criteria andStaffAvailabilityTypeIn(List<Integer> values) {
            addCriterion("staff_availability_type in", values, "staffAvailabilityType");
            return (Criteria) this;
        }

        public Criteria andStaffAvailabilityTypeNotIn(List<Integer> values) {
            addCriterion("staff_availability_type not in", values, "staffAvailabilityType");
            return (Criteria) this;
        }

        public Criteria andStaffAvailabilityTypeBetween(Integer value1, Integer value2) {
            addCriterion("staff_availability_type between", value1, value2, "staffAvailabilityType");
            return (Criteria) this;
        }

        public Criteria andStaffAvailabilityTypeNotBetween(Integer value1, Integer value2) {
            addCriterion("staff_availability_type not between", value1, value2, "staffAvailabilityType");
            return (Criteria) this;
        }
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table moe_business
     *
     * @mbg.generated do_not_delete_during_merge
     */
    public static class Criteria extends GeneratedCriteria {
        protected Criteria() {
            super();
        }
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table moe_business
     *
     * @mbg.generated
     */
    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}
