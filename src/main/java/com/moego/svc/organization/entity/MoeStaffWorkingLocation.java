package com.moego.svc.organization.entity;

import java.time.LocalDateTime;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder(toBuilder = true)
@AllArgsConstructor(staticName = "")
@NoArgsConstructor(staticName = "")
public class MoeStaffWorkingLocation {
    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_staff_working_location.id
     *
     * @mbg.generated
     */
    private Long id;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_staff_working_location.staff_id
     *
     * @mbg.generated
     */
    private Long staffId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_staff_working_location.company_id
     *
     * @mbg.generated
     */
    private Long companyId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_staff_working_location.working_location_id
     *
     * @mbg.generated
     */
    private Long workingLocationId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_staff_working_location.created_at
     *
     * @mbg.generated
     */
    private LocalDateTime createdAt;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_staff_working_location.created_by
     *
     * @mbg.generated
     */
    private Long createdBy;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_staff_working_location.deleted_at
     *
     * @mbg.generated
     */
    private LocalDateTime deletedAt;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_staff_working_location.deleted_by
     *
     * @mbg.generated
     */
    private Long deletedBy;
}
