package com.moego.svc.organization.entity;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

public class MoeClockInOutSettingExample {
    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table moe_clock_in_out_setting
     *
     * @mbg.generated
     */
    protected String orderByClause;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table moe_clock_in_out_setting
     *
     * @mbg.generated
     */
    protected boolean distinct;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table moe_clock_in_out_setting
     *
     * @mbg.generated
     */
    protected List<Criteria> oredCriteria;

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_clock_in_out_setting
     *
     * @mbg.generated
     */
    public MoeClockInOutSettingExample() {
        oredCriteria = new ArrayList<>();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_clock_in_out_setting
     *
     * @mbg.generated
     */
    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_clock_in_out_setting
     *
     * @mbg.generated
     */
    public String getOrderByClause() {
        return orderByClause;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_clock_in_out_setting
     *
     * @mbg.generated
     */
    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_clock_in_out_setting
     *
     * @mbg.generated
     */
    public boolean isDistinct() {
        return distinct;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_clock_in_out_setting
     *
     * @mbg.generated
     */
    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_clock_in_out_setting
     *
     * @mbg.generated
     */
    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_clock_in_out_setting
     *
     * @mbg.generated
     */
    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_clock_in_out_setting
     *
     * @mbg.generated
     */
    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_clock_in_out_setting
     *
     * @mbg.generated
     */
    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_clock_in_out_setting
     *
     * @mbg.generated
     */
    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table moe_clock_in_out_setting
     *
     * @mbg.generated
     */
    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andCompanyIdIsNull() {
            addCriterion("company_id is null");
            return (Criteria) this;
        }

        public Criteria andCompanyIdIsNotNull() {
            addCriterion("company_id is not null");
            return (Criteria) this;
        }

        public Criteria andCompanyIdEqualTo(Long value) {
            addCriterion("company_id =", value, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdNotEqualTo(Long value) {
            addCriterion("company_id <>", value, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdGreaterThan(Long value) {
            addCriterion("company_id >", value, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdGreaterThanOrEqualTo(Long value) {
            addCriterion("company_id >=", value, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdLessThan(Long value) {
            addCriterion("company_id <", value, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdLessThanOrEqualTo(Long value) {
            addCriterion("company_id <=", value, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdIn(List<Long> values) {
            addCriterion("company_id in", values, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdNotIn(List<Long> values) {
            addCriterion("company_id not in", values, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdBetween(Long value1, Long value2) {
            addCriterion("company_id between", value1, value2, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdNotBetween(Long value1, Long value2) {
            addCriterion("company_id not between", value1, value2, "companyId");
            return (Criteria) this;
        }

        public Criteria andEnableClockInOutIsNull() {
            addCriterion("enable_clock_in_out is null");
            return (Criteria) this;
        }

        public Criteria andEnableClockInOutIsNotNull() {
            addCriterion("enable_clock_in_out is not null");
            return (Criteria) this;
        }

        public Criteria andEnableClockInOutEqualTo(Boolean value) {
            addCriterion("enable_clock_in_out =", value, "enableClockInOut");
            return (Criteria) this;
        }

        public Criteria andEnableClockInOutNotEqualTo(Boolean value) {
            addCriterion("enable_clock_in_out <>", value, "enableClockInOut");
            return (Criteria) this;
        }

        public Criteria andEnableClockInOutGreaterThan(Boolean value) {
            addCriterion("enable_clock_in_out >", value, "enableClockInOut");
            return (Criteria) this;
        }

        public Criteria andEnableClockInOutGreaterThanOrEqualTo(Boolean value) {
            addCriterion("enable_clock_in_out >=", value, "enableClockInOut");
            return (Criteria) this;
        }

        public Criteria andEnableClockInOutLessThan(Boolean value) {
            addCriterion("enable_clock_in_out <", value, "enableClockInOut");
            return (Criteria) this;
        }

        public Criteria andEnableClockInOutLessThanOrEqualTo(Boolean value) {
            addCriterion("enable_clock_in_out <=", value, "enableClockInOut");
            return (Criteria) this;
        }

        public Criteria andEnableClockInOutIn(List<Boolean> values) {
            addCriterion("enable_clock_in_out in", values, "enableClockInOut");
            return (Criteria) this;
        }

        public Criteria andEnableClockInOutNotIn(List<Boolean> values) {
            addCriterion("enable_clock_in_out not in", values, "enableClockInOut");
            return (Criteria) this;
        }

        public Criteria andEnableClockInOutBetween(Boolean value1, Boolean value2) {
            addCriterion("enable_clock_in_out between", value1, value2, "enableClockInOut");
            return (Criteria) this;
        }

        public Criteria andEnableClockInOutNotBetween(Boolean value1, Boolean value2) {
            addCriterion("enable_clock_in_out not between", value1, value2, "enableClockInOut");
            return (Criteria) this;
        }

        public Criteria andClockInOutNotifyIsNull() {
            addCriterion("clock_in_out_notify is null");
            return (Criteria) this;
        }

        public Criteria andClockInOutNotifyIsNotNull() {
            addCriterion("clock_in_out_notify is not null");
            return (Criteria) this;
        }

        public Criteria andClockInOutNotifyEqualTo(Boolean value) {
            addCriterion("clock_in_out_notify =", value, "clockInOutNotify");
            return (Criteria) this;
        }

        public Criteria andClockInOutNotifyNotEqualTo(Boolean value) {
            addCriterion("clock_in_out_notify <>", value, "clockInOutNotify");
            return (Criteria) this;
        }

        public Criteria andClockInOutNotifyGreaterThan(Boolean value) {
            addCriterion("clock_in_out_notify >", value, "clockInOutNotify");
            return (Criteria) this;
        }

        public Criteria andClockInOutNotifyGreaterThanOrEqualTo(Boolean value) {
            addCriterion("clock_in_out_notify >=", value, "clockInOutNotify");
            return (Criteria) this;
        }

        public Criteria andClockInOutNotifyLessThan(Boolean value) {
            addCriterion("clock_in_out_notify <", value, "clockInOutNotify");
            return (Criteria) this;
        }

        public Criteria andClockInOutNotifyLessThanOrEqualTo(Boolean value) {
            addCriterion("clock_in_out_notify <=", value, "clockInOutNotify");
            return (Criteria) this;
        }

        public Criteria andClockInOutNotifyIn(List<Boolean> values) {
            addCriterion("clock_in_out_notify in", values, "clockInOutNotify");
            return (Criteria) this;
        }

        public Criteria andClockInOutNotifyNotIn(List<Boolean> values) {
            addCriterion("clock_in_out_notify not in", values, "clockInOutNotify");
            return (Criteria) this;
        }

        public Criteria andClockInOutNotifyBetween(Boolean value1, Boolean value2) {
            addCriterion("clock_in_out_notify between", value1, value2, "clockInOutNotify");
            return (Criteria) this;
        }

        public Criteria andClockInOutNotifyNotBetween(Boolean value1, Boolean value2) {
            addCriterion("clock_in_out_notify not between", value1, value2, "clockInOutNotify");
            return (Criteria) this;
        }

        public Criteria andCreatedAtIsNull() {
            addCriterion("created_at is null");
            return (Criteria) this;
        }

        public Criteria andCreatedAtIsNotNull() {
            addCriterion("created_at is not null");
            return (Criteria) this;
        }

        public Criteria andCreatedAtEqualTo(LocalDateTime value) {
            addCriterion("created_at =", value, "createdAt");
            return (Criteria) this;
        }

        public Criteria andCreatedAtNotEqualTo(LocalDateTime value) {
            addCriterion("created_at <>", value, "createdAt");
            return (Criteria) this;
        }

        public Criteria andCreatedAtGreaterThan(LocalDateTime value) {
            addCriterion("created_at >", value, "createdAt");
            return (Criteria) this;
        }

        public Criteria andCreatedAtGreaterThanOrEqualTo(LocalDateTime value) {
            addCriterion("created_at >=", value, "createdAt");
            return (Criteria) this;
        }

        public Criteria andCreatedAtLessThan(LocalDateTime value) {
            addCriterion("created_at <", value, "createdAt");
            return (Criteria) this;
        }

        public Criteria andCreatedAtLessThanOrEqualTo(LocalDateTime value) {
            addCriterion("created_at <=", value, "createdAt");
            return (Criteria) this;
        }

        public Criteria andCreatedAtIn(List<LocalDateTime> values) {
            addCriterion("created_at in", values, "createdAt");
            return (Criteria) this;
        }

        public Criteria andCreatedAtNotIn(List<LocalDateTime> values) {
            addCriterion("created_at not in", values, "createdAt");
            return (Criteria) this;
        }

        public Criteria andCreatedAtBetween(LocalDateTime value1, LocalDateTime value2) {
            addCriterion("created_at between", value1, value2, "createdAt");
            return (Criteria) this;
        }

        public Criteria andCreatedAtNotBetween(LocalDateTime value1, LocalDateTime value2) {
            addCriterion("created_at not between", value1, value2, "createdAt");
            return (Criteria) this;
        }

        public Criteria andUpdatedAtIsNull() {
            addCriterion("updated_at is null");
            return (Criteria) this;
        }

        public Criteria andUpdatedAtIsNotNull() {
            addCriterion("updated_at is not null");
            return (Criteria) this;
        }

        public Criteria andUpdatedAtEqualTo(LocalDateTime value) {
            addCriterion("updated_at =", value, "updatedAt");
            return (Criteria) this;
        }

        public Criteria andUpdatedAtNotEqualTo(LocalDateTime value) {
            addCriterion("updated_at <>", value, "updatedAt");
            return (Criteria) this;
        }

        public Criteria andUpdatedAtGreaterThan(LocalDateTime value) {
            addCriterion("updated_at >", value, "updatedAt");
            return (Criteria) this;
        }

        public Criteria andUpdatedAtGreaterThanOrEqualTo(LocalDateTime value) {
            addCriterion("updated_at >=", value, "updatedAt");
            return (Criteria) this;
        }

        public Criteria andUpdatedAtLessThan(LocalDateTime value) {
            addCriterion("updated_at <", value, "updatedAt");
            return (Criteria) this;
        }

        public Criteria andUpdatedAtLessThanOrEqualTo(LocalDateTime value) {
            addCriterion("updated_at <=", value, "updatedAt");
            return (Criteria) this;
        }

        public Criteria andUpdatedAtIn(List<LocalDateTime> values) {
            addCriterion("updated_at in", values, "updatedAt");
            return (Criteria) this;
        }

        public Criteria andUpdatedAtNotIn(List<LocalDateTime> values) {
            addCriterion("updated_at not in", values, "updatedAt");
            return (Criteria) this;
        }

        public Criteria andUpdatedAtBetween(LocalDateTime value1, LocalDateTime value2) {
            addCriterion("updated_at between", value1, value2, "updatedAt");
            return (Criteria) this;
        }

        public Criteria andUpdatedAtNotBetween(LocalDateTime value1, LocalDateTime value2) {
            addCriterion("updated_at not between", value1, value2, "updatedAt");
            return (Criteria) this;
        }

        public Criteria andUpdatedByIsNull() {
            addCriterion("updated_by is null");
            return (Criteria) this;
        }

        public Criteria andUpdatedByIsNotNull() {
            addCriterion("updated_by is not null");
            return (Criteria) this;
        }

        public Criteria andUpdatedByEqualTo(Long value) {
            addCriterion("updated_by =", value, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByNotEqualTo(Long value) {
            addCriterion("updated_by <>", value, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByGreaterThan(Long value) {
            addCriterion("updated_by >", value, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByGreaterThanOrEqualTo(Long value) {
            addCriterion("updated_by >=", value, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByLessThan(Long value) {
            addCriterion("updated_by <", value, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByLessThanOrEqualTo(Long value) {
            addCriterion("updated_by <=", value, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByIn(List<Long> values) {
            addCriterion("updated_by in", values, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByNotIn(List<Long> values) {
            addCriterion("updated_by not in", values, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByBetween(Long value1, Long value2) {
            addCriterion("updated_by between", value1, value2, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByNotBetween(Long value1, Long value2) {
            addCriterion("updated_by not between", value1, value2, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andEnableClockInOutOvernightIsNull() {
            addCriterion("enable_clock_in_out_overnight is null");
            return (Criteria) this;
        }

        public Criteria andEnableClockInOutOvernightIsNotNull() {
            addCriterion("enable_clock_in_out_overnight is not null");
            return (Criteria) this;
        }

        public Criteria andEnableClockInOutOvernightEqualTo(Boolean value) {
            addCriterion("enable_clock_in_out_overnight =", value, "enableClockInOutOvernight");
            return (Criteria) this;
        }

        public Criteria andEnableClockInOutOvernightNotEqualTo(Boolean value) {
            addCriterion("enable_clock_in_out_overnight <>", value, "enableClockInOutOvernight");
            return (Criteria) this;
        }

        public Criteria andEnableClockInOutOvernightGreaterThan(Boolean value) {
            addCriterion("enable_clock_in_out_overnight >", value, "enableClockInOutOvernight");
            return (Criteria) this;
        }

        public Criteria andEnableClockInOutOvernightGreaterThanOrEqualTo(Boolean value) {
            addCriterion("enable_clock_in_out_overnight >=", value, "enableClockInOutOvernight");
            return (Criteria) this;
        }

        public Criteria andEnableClockInOutOvernightLessThan(Boolean value) {
            addCriterion("enable_clock_in_out_overnight <", value, "enableClockInOutOvernight");
            return (Criteria) this;
        }

        public Criteria andEnableClockInOutOvernightLessThanOrEqualTo(Boolean value) {
            addCriterion("enable_clock_in_out_overnight <=", value, "enableClockInOutOvernight");
            return (Criteria) this;
        }

        public Criteria andEnableClockInOutOvernightIn(List<Boolean> values) {
            addCriterion("enable_clock_in_out_overnight in", values, "enableClockInOutOvernight");
            return (Criteria) this;
        }

        public Criteria andEnableClockInOutOvernightNotIn(List<Boolean> values) {
            addCriterion("enable_clock_in_out_overnight not in", values, "enableClockInOutOvernight");
            return (Criteria) this;
        }

        public Criteria andEnableClockInOutOvernightBetween(Boolean value1, Boolean value2) {
            addCriterion("enable_clock_in_out_overnight between", value1, value2, "enableClockInOutOvernight");
            return (Criteria) this;
        }

        public Criteria andEnableClockInOutOvernightNotBetween(Boolean value1, Boolean value2) {
            addCriterion("enable_clock_in_out_overnight not between", value1, value2, "enableClockInOutOvernight");
            return (Criteria) this;
        }
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table moe_clock_in_out_setting
     *
     * @mbg.generated do_not_delete_during_merge
     */
    public static class Criteria extends GeneratedCriteria {
        protected Criteria() {
            super();
        }
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table moe_clock_in_out_setting
     *
     * @mbg.generated
     */
    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}
