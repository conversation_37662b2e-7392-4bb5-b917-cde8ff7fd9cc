package com.moego.svc.organization.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder(toBuilder = true)
@AllArgsConstructor(staticName = "")
@NoArgsConstructor(staticName = "")
public class MoeVanStaff {
    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_van_staff.id
     *
     * @mbg.generated
     */
    private Integer id;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_van_staff.business_id
     *
     * @mbg.generated
     */
    private Integer businessId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_van_staff.van_id
     *
     * @mbg.generated
     */
    private Integer vanId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_van_staff.staff_id
     *
     * @mbg.generated
     */
    private Integer staffId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_van_staff.status
     *
     * @mbg.generated
     */
    private Byte status;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_van_staff.start_time
     *
     * @mbg.generated
     */
    private Long startTime;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_van_staff.end_time
     *
     * @mbg.generated
     */
    private Long endTime;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_van_staff.create_time
     *
     * @mbg.generated
     */
    private Long createTime;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_van_staff.update_time
     *
     * @mbg.generated
     */
    private Long updateTime;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_van_staff.company_id
     *
     * @mbg.generated
     */
    private Long companyId;
}
