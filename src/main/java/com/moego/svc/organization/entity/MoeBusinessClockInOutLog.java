package com.moego.svc.organization.entity;

import java.time.LocalDate;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder(toBuilder = true)
@AllArgsConstructor(staticName = "")
@NoArgsConstructor(staticName = "")
public class MoeBusinessClockInOutLog {
    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_business_clock_in_out_log.id
     *
     * @mbg.generated
     */
    private Integer id;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_business_clock_in_out_log.business_id
     *
     * @mbg.generated
     */
    private Integer businessId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_business_clock_in_out_log.account_id
     *
     * @mbg.generated
     */
    private Integer accountId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_business_clock_in_out_log.staff_id
     *
     * @mbg.generated
     */
    private Integer staffId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_business_clock_in_out_log.clock_date
     *
     * @mbg.generated
     */
    private LocalDate clockDate;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_business_clock_in_out_log.clock_in_time
     *
     * @mbg.generated
     */
    private Long clockInTime;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_business_clock_in_out_log.clock_out_time
     *
     * @mbg.generated
     */
    private Long clockOutTime;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_business_clock_in_out_log.update_time
     *
     * @mbg.generated
     */
    private Long updateTime;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_business_clock_in_out_log.status
     *
     * @mbg.generated
     */
    private Byte status;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_business_clock_in_out_log.company_id
     *
     * @mbg.generated
     */
    private Long companyId;
}
