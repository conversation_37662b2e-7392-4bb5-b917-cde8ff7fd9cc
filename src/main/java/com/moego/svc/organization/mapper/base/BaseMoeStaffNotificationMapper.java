package com.moego.svc.organization.mapper.base;

import com.moego.svc.organization.entity.MoeStaffNotification;
import com.moego.svc.organization.entity.MoeStaffNotificationExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface BaseMoeStaffNotificationMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_staff_notification
     *
     * @mbg.generated
     */
    long countByExample(MoeStaffNotificationExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_staff_notification
     *
     * @mbg.generated
     */
    int deleteByExample(MoeStaffNotificationExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_staff_notification
     *
     * @mbg.generated
     */
    int deleteByPrimaryKey(Integer id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_staff_notification
     *
     * @mbg.generated
     */
    int insert(MoeStaffNotification row);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_staff_notification
     *
     * @mbg.generated
     */
    int insertSelective(MoeStaffNotification row);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_staff_notification
     *
     * @mbg.generated
     */
    List<MoeStaffNotification> selectByExample(MoeStaffNotificationExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_staff_notification
     *
     * @mbg.generated
     */
    MoeStaffNotification selectByPrimaryKey(Integer id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_staff_notification
     *
     * @mbg.generated
     */
    int updateByExampleSelective(
            @Param("row") MoeStaffNotification row, @Param("example") MoeStaffNotificationExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_staff_notification
     *
     * @mbg.generated
     */
    int updateByExample(@Param("row") MoeStaffNotification row, @Param("example") MoeStaffNotificationExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_staff_notification
     *
     * @mbg.generated
     */
    int updateByPrimaryKeySelective(MoeStaffNotification row);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_staff_notification
     *
     * @mbg.generated
     */
    int updateByPrimaryKey(MoeStaffNotification row);
}
