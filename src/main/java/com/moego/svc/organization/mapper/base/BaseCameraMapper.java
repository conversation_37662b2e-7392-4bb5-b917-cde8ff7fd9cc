package com.moego.svc.organization.mapper.base;

import com.moego.svc.organization.entity.Camera;
import com.moego.svc.organization.entity.CameraExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface BaseCameraMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table camera
     *
     * @mbg.generated
     */
    long countByExample(CameraExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table camera
     *
     * @mbg.generated
     */
    int deleteByExample(CameraExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table camera
     *
     * @mbg.generated
     */
    int deleteByPrimaryKey(Long id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table camera
     *
     * @mbg.generated
     */
    int insert(Camera row);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table camera
     *
     * @mbg.generated
     */
    int insertSelective(Camera row);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table camera
     *
     * @mbg.generated
     */
    List<Camera> selectByExample(CameraExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table camera
     *
     * @mbg.generated
     */
    Camera selectByPrimaryKey(Long id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table camera
     *
     * @mbg.generated
     */
    int updateByExampleSelective(@Param("row") Camera row, @Param("example") CameraExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table camera
     *
     * @mbg.generated
     */
    int updateByExample(@Param("row") Camera row, @Param("example") CameraExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table camera
     *
     * @mbg.generated
     */
    int updateByPrimaryKeySelective(Camera row);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table camera
     *
     * @mbg.generated
     */
    int updateByPrimaryKey(Camera row);
}
