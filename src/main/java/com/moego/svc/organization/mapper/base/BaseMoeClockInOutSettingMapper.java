package com.moego.svc.organization.mapper.base;

import com.moego.svc.organization.entity.MoeClockInOutSetting;
import com.moego.svc.organization.entity.MoeClockInOutSettingExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface BaseMoeClockInOutSettingMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_clock_in_out_setting
     *
     * @mbg.generated
     */
    long countByExample(MoeClockInOutSettingExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_clock_in_out_setting
     *
     * @mbg.generated
     */
    int deleteByExample(MoeClockInOutSettingExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_clock_in_out_setting
     *
     * @mbg.generated
     */
    int deleteByPrimaryKey(Long id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_clock_in_out_setting
     *
     * @mbg.generated
     */
    int insert(MoeClockInOutSetting row);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_clock_in_out_setting
     *
     * @mbg.generated
     */
    int insertSelective(MoeClockInOutSetting row);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_clock_in_out_setting
     *
     * @mbg.generated
     */
    List<MoeClockInOutSetting> selectByExample(MoeClockInOutSettingExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_clock_in_out_setting
     *
     * @mbg.generated
     */
    MoeClockInOutSetting selectByPrimaryKey(Long id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_clock_in_out_setting
     *
     * @mbg.generated
     */
    int updateByExampleSelective(
            @Param("row") MoeClockInOutSetting row, @Param("example") MoeClockInOutSettingExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_clock_in_out_setting
     *
     * @mbg.generated
     */
    int updateByExample(@Param("row") MoeClockInOutSetting row, @Param("example") MoeClockInOutSettingExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_clock_in_out_setting
     *
     * @mbg.generated
     */
    int updateByPrimaryKeySelective(MoeClockInOutSetting row);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_clock_in_out_setting
     *
     * @mbg.generated
     */
    int updateByPrimaryKey(MoeClockInOutSetting row);
}
