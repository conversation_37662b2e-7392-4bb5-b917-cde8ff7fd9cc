package com.moego.svc.organization.mapper.base;

import com.moego.svc.organization.entity.StaffSlotFreeService;
import com.moego.svc.organization.entity.StaffSlotFreeServiceExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface BaseStaffSlotFreeServiceMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table staff_slot_free_service
     *
     * @mbg.generated
     */
    long countByExample(StaffSlotFreeServiceExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table staff_slot_free_service
     *
     * @mbg.generated
     */
    int deleteByExample(StaffSlotFreeServiceExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table staff_slot_free_service
     *
     * @mbg.generated
     */
    int deleteByPrimaryKey(Long id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table staff_slot_free_service
     *
     * @mbg.generated
     */
    int insert(StaffSlotFreeService row);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table staff_slot_free_service
     *
     * @mbg.generated
     */
    int insertSelective(StaffSlotFreeService row);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table staff_slot_free_service
     *
     * @mbg.generated
     */
    List<StaffSlotFreeService> selectByExample(StaffSlotFreeServiceExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table staff_slot_free_service
     *
     * @mbg.generated
     */
    StaffSlotFreeService selectByPrimaryKey(Long id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table staff_slot_free_service
     *
     * @mbg.generated
     */
    int updateByExampleSelective(
            @Param("row") StaffSlotFreeService row, @Param("example") StaffSlotFreeServiceExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table staff_slot_free_service
     *
     * @mbg.generated
     */
    int updateByExample(@Param("row") StaffSlotFreeService row, @Param("example") StaffSlotFreeServiceExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table staff_slot_free_service
     *
     * @mbg.generated
     */
    int updateByPrimaryKeySelective(StaffSlotFreeService row);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table staff_slot_free_service
     *
     * @mbg.generated
     */
    int updateByPrimaryKey(StaffSlotFreeService row);
}
