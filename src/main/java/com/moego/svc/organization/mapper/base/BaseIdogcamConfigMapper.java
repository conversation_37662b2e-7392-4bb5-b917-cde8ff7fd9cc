package com.moego.svc.organization.mapper.base;

import com.moego.svc.organization.entity.IdogcamConfig;
import com.moego.svc.organization.entity.IdogcamConfigExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface BaseIdogcamConfigMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table idogcam_config
     *
     * @mbg.generated
     */
    long countByExample(IdogcamConfigExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table idogcam_config
     *
     * @mbg.generated
     */
    int deleteByExample(IdogcamConfigExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table idogcam_config
     *
     * @mbg.generated
     */
    int deleteByPrimaryKey(Long id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table idogcam_config
     *
     * @mbg.generated
     */
    int insert(IdogcamConfig row);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table idogcam_config
     *
     * @mbg.generated
     */
    int insertSelective(IdogcamConfig row);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table idogcam_config
     *
     * @mbg.generated
     */
    List<IdogcamConfig> selectByExample(IdogcamConfigExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table idogcam_config
     *
     * @mbg.generated
     */
    IdogcamConfig selectByPrimaryKey(Long id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table idogcam_config
     *
     * @mbg.generated
     */
    int updateByExampleSelective(@Param("row") IdogcamConfig row, @Param("example") IdogcamConfigExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table idogcam_config
     *
     * @mbg.generated
     */
    int updateByExample(@Param("row") IdogcamConfig row, @Param("example") IdogcamConfigExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table idogcam_config
     *
     * @mbg.generated
     */
    int updateByPrimaryKeySelective(IdogcamConfig row);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table idogcam_config
     *
     * @mbg.generated
     */
    int updateByPrimaryKey(IdogcamConfig row);
}
