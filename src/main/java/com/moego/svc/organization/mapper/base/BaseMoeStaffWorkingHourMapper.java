package com.moego.svc.organization.mapper.base;

import com.moego.svc.organization.entity.MoeStaffWorkingHour;
import com.moego.svc.organization.entity.MoeStaffWorkingHourExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface BaseMoeStaffWorkingHourMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_staff_working_hour
     *
     * @mbg.generated
     */
    long countByExample(MoeStaffWorkingHourExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_staff_working_hour
     *
     * @mbg.generated
     */
    int deleteByExample(MoeStaffWorkingHourExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_staff_working_hour
     *
     * @mbg.generated
     */
    int deleteByPrimaryKey(Integer id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_staff_working_hour
     *
     * @mbg.generated
     */
    int insert(MoeStaffWorkingHour row);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_staff_working_hour
     *
     * @mbg.generated
     */
    int insertSelective(MoeStaffWorkingHour row);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_staff_working_hour
     *
     * @mbg.generated
     */
    List<MoeStaffWorkingHour> selectByExampleWithBLOBs(MoeStaffWorkingHourExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_staff_working_hour
     *
     * @mbg.generated
     */
    List<MoeStaffWorkingHour> selectByExample(MoeStaffWorkingHourExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_staff_working_hour
     *
     * @mbg.generated
     */
    MoeStaffWorkingHour selectByPrimaryKey(Integer id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_staff_working_hour
     *
     * @mbg.generated
     */
    int updateByExampleSelective(
            @Param("row") MoeStaffWorkingHour row, @Param("example") MoeStaffWorkingHourExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_staff_working_hour
     *
     * @mbg.generated
     */
    int updateByExampleWithBLOBs(
            @Param("row") MoeStaffWorkingHour row, @Param("example") MoeStaffWorkingHourExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_staff_working_hour
     *
     * @mbg.generated
     */
    int updateByExample(@Param("row") MoeStaffWorkingHour row, @Param("example") MoeStaffWorkingHourExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_staff_working_hour
     *
     * @mbg.generated
     */
    int updateByPrimaryKeySelective(MoeStaffWorkingHour row);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_staff_working_hour
     *
     * @mbg.generated
     */
    int updateByPrimaryKeyWithBLOBs(MoeStaffWorkingHour row);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_staff_working_hour
     *
     * @mbg.generated
     */
    int updateByPrimaryKey(MoeStaffWorkingHour row);
}
