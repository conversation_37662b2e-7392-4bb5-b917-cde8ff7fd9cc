package com.moego.svc.organization.mapper.base;

import com.moego.svc.organization.entity.MoeBusinessWorkingHour;
import com.moego.svc.organization.entity.MoeBusinessWorkingHourExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface BaseMoeBusinessWorkingHourMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_business_working_hour
     *
     * @mbg.generated
     */
    long countByExample(MoeBusinessWorkingHourExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_business_working_hour
     *
     * @mbg.generated
     */
    int deleteByExample(MoeBusinessWorkingHourExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_business_working_hour
     *
     * @mbg.generated
     */
    int deleteByPrimaryKey(Integer id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_business_working_hour
     *
     * @mbg.generated
     */
    int insert(MoeBusinessWorkingHour row);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_business_working_hour
     *
     * @mbg.generated
     */
    int insertSelective(MoeBusinessWorkingHour row);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_business_working_hour
     *
     * @mbg.generated
     */
    List<MoeBusinessWorkingHour> selectByExampleWithBLOBs(MoeBusinessWorkingHourExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_business_working_hour
     *
     * @mbg.generated
     */
    List<MoeBusinessWorkingHour> selectByExample(MoeBusinessWorkingHourExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_business_working_hour
     *
     * @mbg.generated
     */
    MoeBusinessWorkingHour selectByPrimaryKey(Integer id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_business_working_hour
     *
     * @mbg.generated
     */
    int updateByExampleSelective(
            @Param("row") MoeBusinessWorkingHour row, @Param("example") MoeBusinessWorkingHourExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_business_working_hour
     *
     * @mbg.generated
     */
    int updateByExampleWithBLOBs(
            @Param("row") MoeBusinessWorkingHour row, @Param("example") MoeBusinessWorkingHourExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_business_working_hour
     *
     * @mbg.generated
     */
    int updateByExample(
            @Param("row") MoeBusinessWorkingHour row, @Param("example") MoeBusinessWorkingHourExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_business_working_hour
     *
     * @mbg.generated
     */
    int updateByPrimaryKeySelective(MoeBusinessWorkingHour row);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_business_working_hour
     *
     * @mbg.generated
     */
    int updateByPrimaryKeyWithBLOBs(MoeBusinessWorkingHour row);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_business_working_hour
     *
     * @mbg.generated
     */
    int updateByPrimaryKey(MoeBusinessWorkingHour row);
}
