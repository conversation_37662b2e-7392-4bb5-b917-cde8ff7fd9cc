package com.moego.svc.organization.mapper.base;

import com.moego.svc.organization.entity.StaffAvailability;
import com.moego.svc.organization.entity.StaffAvailabilityExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface BaseStaffAvailability {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table staff_availability
     *
     * @mbg.generated
     */
    long countByExample(StaffAvailabilityExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table staff_availability
     *
     * @mbg.generated
     */
    int deleteByExample(StaffAvailabilityExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table staff_availability
     *
     * @mbg.generated
     */
    int deleteByPrimaryKey(Long id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table staff_availability
     *
     * @mbg.generated
     */
    int insert(StaffAvailability row);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table staff_availability
     *
     * @mbg.generated
     */
    int insertSelective(StaffAvailability row);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table staff_availability
     *
     * @mbg.generated
     */
    List<StaffAvailability> selectByExample(StaffAvailabilityExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table staff_availability
     *
     * @mbg.generated
     */
    StaffAvailability selectByPrimaryKey(Long id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table staff_availability
     *
     * @mbg.generated
     */
    int updateByExampleSelective(
            @Param("row") StaffAvailability row, @Param("example") StaffAvailabilityExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table staff_availability
     *
     * @mbg.generated
     */
    int updateByExample(@Param("row") StaffAvailability row, @Param("example") StaffAvailabilityExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table staff_availability
     *
     * @mbg.generated
     */
    int updateByPrimaryKeySelective(StaffAvailability row);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table staff_availability
     *
     * @mbg.generated
     */
    int updateByPrimaryKey(StaffAvailability row);
}
