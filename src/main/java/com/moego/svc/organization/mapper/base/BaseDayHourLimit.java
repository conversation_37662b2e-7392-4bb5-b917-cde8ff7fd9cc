package com.moego.svc.organization.mapper.base;

import com.moego.svc.organization.entity.DayHourLimit;
import com.moego.svc.organization.entity.DayHourLimitExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface BaseDayHourLimit {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table day_hour_limit
     *
     * @mbg.generated
     */
    long countByExample(DayHourLimitExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table day_hour_limit
     *
     * @mbg.generated
     */
    int deleteByExample(DayHourLimitExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table day_hour_limit
     *
     * @mbg.generated
     */
    int deleteByPrimaryKey(Long id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table day_hour_limit
     *
     * @mbg.generated
     */
    int insert(DayHourLimit row);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table day_hour_limit
     *
     * @mbg.generated
     */
    int insertSelective(DayHourLimit row);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table day_hour_limit
     *
     * @mbg.generated
     */
    List<DayHourLimit> selectByExampleWithBLOBs(DayHourLimitExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table day_hour_limit
     *
     * @mbg.generated
     */
    List<DayHourLimit> selectByExample(DayHourLimitExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table day_hour_limit
     *
     * @mbg.generated
     */
    DayHourLimit selectByPrimaryKey(Long id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table day_hour_limit
     *
     * @mbg.generated
     */
    int updateByExampleSelective(@Param("row") DayHourLimit row, @Param("example") DayHourLimitExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table day_hour_limit
     *
     * @mbg.generated
     */
    int updateByExampleWithBLOBs(@Param("row") DayHourLimit row, @Param("example") DayHourLimitExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table day_hour_limit
     *
     * @mbg.generated
     */
    int updateByExample(@Param("row") DayHourLimit row, @Param("example") DayHourLimitExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table day_hour_limit
     *
     * @mbg.generated
     */
    int updateByPrimaryKeySelective(DayHourLimit row);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table day_hour_limit
     *
     * @mbg.generated
     */
    int updateByPrimaryKeyWithBLOBs(DayHourLimit row);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table day_hour_limit
     *
     * @mbg.generated
     */
    int updateByPrimaryKey(DayHourLimit row);
}
