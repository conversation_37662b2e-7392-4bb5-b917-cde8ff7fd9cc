package com.moego.svc.organization.mapper.base;

import com.moego.svc.organization.entity.MoeStaff;
import com.moego.svc.organization.entity.MoeStaffExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface BaseMoeStaffMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_staff
     *
     * @mbg.generated
     */
    long countByExample(MoeStaffExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_staff
     *
     * @mbg.generated
     */
    int deleteByExample(MoeStaffExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_staff
     *
     * @mbg.generated
     */
    int deleteByPrimaryKey(Integer id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_staff
     *
     * @mbg.generated
     */
    int insert(Moe<PERSON>taff row);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_staff
     *
     * @mbg.generated
     */
    int insertSelective(<PERSON><PERSON>ta<PERSON> row);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_staff
     *
     * @mbg.generated
     */
    List<MoeStaff> selectByExample(MoeStaffExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_staff
     *
     * @mbg.generated
     */
    MoeStaff selectByPrimaryKey(Integer id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_staff
     *
     * @mbg.generated
     */
    int updateByExampleSelective(@Param("row") MoeStaff row, @Param("example") MoeStaffExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_staff
     *
     * @mbg.generated
     */
    int updateByExample(@Param("row") MoeStaff row, @Param("example") MoeStaffExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_staff
     *
     * @mbg.generated
     */
    int updateByPrimaryKeySelective(MoeStaff row);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_staff
     *
     * @mbg.generated
     */
    int updateByPrimaryKey(MoeStaff row);
}
