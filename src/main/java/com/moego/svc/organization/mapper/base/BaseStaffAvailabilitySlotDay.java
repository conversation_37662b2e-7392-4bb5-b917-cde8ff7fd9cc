package com.moego.svc.organization.mapper.base;

import com.moego.svc.organization.entity.StaffAvailabilitySlotDay;
import com.moego.svc.organization.entity.StaffAvailabilitySlotDayExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface BaseStaffAvailabilitySlotDay {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table staff_availability_slot_day
     *
     * @mbg.generated
     */
    long countByExample(StaffAvailabilitySlotDayExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table staff_availability_slot_day
     *
     * @mbg.generated
     */
    int deleteByExample(StaffAvailabilitySlotDayExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table staff_availability_slot_day
     *
     * @mbg.generated
     */
    int deleteByPrimaryKey(Long id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table staff_availability_slot_day
     *
     * @mbg.generated
     */
    int insert(StaffAvailabilitySlotDay row);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table staff_availability_slot_day
     *
     * @mbg.generated
     */
    int insertSelective(StaffAvailabilitySlotDay row);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table staff_availability_slot_day
     *
     * @mbg.generated
     */
    List<StaffAvailabilitySlotDay> selectByExampleWithBLOBs(StaffAvailabilitySlotDayExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table staff_availability_slot_day
     *
     * @mbg.generated
     */
    List<StaffAvailabilitySlotDay> selectByExample(StaffAvailabilitySlotDayExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table staff_availability_slot_day
     *
     * @mbg.generated
     */
    StaffAvailabilitySlotDay selectByPrimaryKey(Long id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table staff_availability_slot_day
     *
     * @mbg.generated
     */
    int updateByExampleSelective(
            @Param("row") StaffAvailabilitySlotDay row, @Param("example") StaffAvailabilitySlotDayExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table staff_availability_slot_day
     *
     * @mbg.generated
     */
    int updateByExampleWithBLOBs(
            @Param("row") StaffAvailabilitySlotDay row, @Param("example") StaffAvailabilitySlotDayExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table staff_availability_slot_day
     *
     * @mbg.generated
     */
    int updateByExample(
            @Param("row") StaffAvailabilitySlotDay row, @Param("example") StaffAvailabilitySlotDayExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table staff_availability_slot_day
     *
     * @mbg.generated
     */
    int updateByPrimaryKeySelective(StaffAvailabilitySlotDay row);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table staff_availability_slot_day
     *
     * @mbg.generated
     */
    int updateByPrimaryKeyWithBLOBs(StaffAvailabilitySlotDay row);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table staff_availability_slot_day
     *
     * @mbg.generated
     */
    int updateByPrimaryKey(StaffAvailabilitySlotDay row);
}
