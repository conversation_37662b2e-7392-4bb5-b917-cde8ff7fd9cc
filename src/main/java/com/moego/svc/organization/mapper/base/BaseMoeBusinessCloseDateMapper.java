package com.moego.svc.organization.mapper.base;

import com.moego.svc.organization.entity.MoeBusinessCloseDate;
import com.moego.svc.organization.entity.MoeBusinessCloseDateExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface BaseMoeBusinessCloseDateMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_business_close_date
     *
     * @mbg.generated
     */
    long countByExample(MoeBusinessCloseDateExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_business_close_date
     *
     * @mbg.generated
     */
    int deleteByExample(MoeBusinessCloseDateExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_business_close_date
     *
     * @mbg.generated
     */
    int deleteByPrimaryKey(Integer id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_business_close_date
     *
     * @mbg.generated
     */
    int insert(MoeBusinessCloseDate row);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_business_close_date
     *
     * @mbg.generated
     */
    int insertSelective(MoeBusinessCloseDate row);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_business_close_date
     *
     * @mbg.generated
     */
    List<MoeBusinessCloseDate> selectByExampleWithBLOBs(MoeBusinessCloseDateExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_business_close_date
     *
     * @mbg.generated
     */
    List<MoeBusinessCloseDate> selectByExample(MoeBusinessCloseDateExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_business_close_date
     *
     * @mbg.generated
     */
    MoeBusinessCloseDate selectByPrimaryKey(Integer id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_business_close_date
     *
     * @mbg.generated
     */
    int updateByExampleSelective(
            @Param("row") MoeBusinessCloseDate row, @Param("example") MoeBusinessCloseDateExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_business_close_date
     *
     * @mbg.generated
     */
    int updateByExampleWithBLOBs(
            @Param("row") MoeBusinessCloseDate row, @Param("example") MoeBusinessCloseDateExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_business_close_date
     *
     * @mbg.generated
     */
    int updateByExample(@Param("row") MoeBusinessCloseDate row, @Param("example") MoeBusinessCloseDateExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_business_close_date
     *
     * @mbg.generated
     */
    int updateByPrimaryKeySelective(MoeBusinessCloseDate row);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_business_close_date
     *
     * @mbg.generated
     */
    int updateByPrimaryKeyWithBLOBs(MoeBusinessCloseDate row);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_business_close_date
     *
     * @mbg.generated
     */
    int updateByPrimaryKey(MoeBusinessCloseDate row);
}
