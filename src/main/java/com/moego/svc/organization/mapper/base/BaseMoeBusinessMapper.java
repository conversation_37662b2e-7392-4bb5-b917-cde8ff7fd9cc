package com.moego.svc.organization.mapper.base;

import com.moego.svc.organization.entity.MoeBusiness;
import com.moego.svc.organization.entity.MoeBusinessExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface BaseMoeBusinessMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_business
     *
     * @mbg.generated
     */
    long countByExample(MoeBusinessExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_business
     *
     * @mbg.generated
     */
    int deleteByExample(MoeBusinessExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_business
     *
     * @mbg.generated
     */
    int deleteByPrimaryKey(Integer id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_business
     *
     * @mbg.generated
     */
    int insert(MoeBusiness row);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_business
     *
     * @mbg.generated
     */
    int insertSelective(MoeBusiness row);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_business
     *
     * @mbg.generated
     */
    List<MoeBusiness> selectByExample(MoeBusinessExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_business
     *
     * @mbg.generated
     */
    MoeBusiness selectByPrimaryKey(Integer id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_business
     *
     * @mbg.generated
     */
    int updateByExampleSelective(@Param("row") MoeBusiness row, @Param("example") MoeBusinessExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_business
     *
     * @mbg.generated
     */
    int updateByExample(@Param("row") MoeBusiness row, @Param("example") MoeBusinessExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_business
     *
     * @mbg.generated
     */
    int updateByPrimaryKeySelective(MoeBusiness row);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_business
     *
     * @mbg.generated
     */
    int updateByPrimaryKey(MoeBusiness row);
}
