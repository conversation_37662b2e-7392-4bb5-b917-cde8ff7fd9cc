package com.moego.svc.organization.mapper.base;

import com.moego.svc.organization.entity.StaffAvailabilityTimeDay;
import com.moego.svc.organization.entity.StaffAvailabilityTimeDayExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface BaseStaffAvailabilityTimeDay {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table staff_availability_time_day
     *
     * @mbg.generated
     */
    long countByExample(StaffAvailabilityTimeDayExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table staff_availability_time_day
     *
     * @mbg.generated
     */
    int deleteByExample(StaffAvailabilityTimeDayExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table staff_availability_time_day
     *
     * @mbg.generated
     */
    int deleteByPrimaryKey(Long id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table staff_availability_time_day
     *
     * @mbg.generated
     */
    int insert(StaffAvailabilityTimeDay row);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table staff_availability_time_day
     *
     * @mbg.generated
     */
    int insertSelective(StaffAvailabilityTimeDay row);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table staff_availability_time_day
     *
     * @mbg.generated
     */
    List<StaffAvailabilityTimeDay> selectByExampleWithBLOBs(StaffAvailabilityTimeDayExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table staff_availability_time_day
     *
     * @mbg.generated
     */
    List<StaffAvailabilityTimeDay> selectByExample(StaffAvailabilityTimeDayExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table staff_availability_time_day
     *
     * @mbg.generated
     */
    StaffAvailabilityTimeDay selectByPrimaryKey(Long id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table staff_availability_time_day
     *
     * @mbg.generated
     */
    int updateByExampleSelective(
            @Param("row") StaffAvailabilityTimeDay row, @Param("example") StaffAvailabilityTimeDayExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table staff_availability_time_day
     *
     * @mbg.generated
     */
    int updateByExampleWithBLOBs(
            @Param("row") StaffAvailabilityTimeDay row, @Param("example") StaffAvailabilityTimeDayExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table staff_availability_time_day
     *
     * @mbg.generated
     */
    int updateByExample(
            @Param("row") StaffAvailabilityTimeDay row, @Param("example") StaffAvailabilityTimeDayExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table staff_availability_time_day
     *
     * @mbg.generated
     */
    int updateByPrimaryKeySelective(StaffAvailabilityTimeDay row);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table staff_availability_time_day
     *
     * @mbg.generated
     */
    int updateByPrimaryKeyWithBLOBs(StaffAvailabilityTimeDay row);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table staff_availability_time_day
     *
     * @mbg.generated
     */
    int updateByPrimaryKey(StaffAvailabilityTimeDay row);
}
