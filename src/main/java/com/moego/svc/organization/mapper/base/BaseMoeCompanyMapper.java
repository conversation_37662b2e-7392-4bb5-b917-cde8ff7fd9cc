package com.moego.svc.organization.mapper.base;

import com.moego.svc.organization.entity.MoeCompany;
import com.moego.svc.organization.entity.MoeCompanyExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface BaseMoeCompanyMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_company
     *
     * @mbg.generated
     */
    long countByExample(MoeCompanyExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_company
     *
     * @mbg.generated
     */
    int deleteByExample(MoeCompanyExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_company
     *
     * @mbg.generated
     */
    int deleteByPrimaryKey(Integer id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_company
     *
     * @mbg.generated
     */
    int insert(MoeCompany row);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_company
     *
     * @mbg.generated
     */
    int insertSelective(MoeCompany row);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_company
     *
     * @mbg.generated
     */
    List<MoeCompany> selectByExample(MoeCompanyExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_company
     *
     * @mbg.generated
     */
    MoeCompany selectByPrimaryKey(Integer id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_company
     *
     * @mbg.generated
     */
    int updateByExampleSelective(@Param("row") MoeCompany row, @Param("example") MoeCompanyExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_company
     *
     * @mbg.generated
     */
    int updateByExample(@Param("row") MoeCompany row, @Param("example") MoeCompanyExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_company
     *
     * @mbg.generated
     */
    int updateByPrimaryKeySelective(MoeCompany row);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_company
     *
     * @mbg.generated
     */
    int updateByPrimaryKey(MoeCompany row);
}
