package com.moego.svc.organization.mapper.base;

import com.moego.svc.organization.entity.MoeBusinessClockInOutLog;
import com.moego.svc.organization.entity.MoeBusinessClockInOutLogExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface BaseMoeBusinessClockInOutLogMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_business_clock_in_out_log
     *
     * @mbg.generated
     */
    long countByExample(MoeBusinessClockInOutLogExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_business_clock_in_out_log
     *
     * @mbg.generated
     */
    int deleteByExample(MoeBusinessClockInOutLogExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_business_clock_in_out_log
     *
     * @mbg.generated
     */
    int deleteByPrimaryKey(Integer id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_business_clock_in_out_log
     *
     * @mbg.generated
     */
    int insert(MoeBusinessClockInOutLog row);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_business_clock_in_out_log
     *
     * @mbg.generated
     */
    int insertSelective(MoeBusinessClockInOutLog row);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_business_clock_in_out_log
     *
     * @mbg.generated
     */
    List<MoeBusinessClockInOutLog> selectByExample(MoeBusinessClockInOutLogExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_business_clock_in_out_log
     *
     * @mbg.generated
     */
    MoeBusinessClockInOutLog selectByPrimaryKey(Integer id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_business_clock_in_out_log
     *
     * @mbg.generated
     */
    int updateByExampleSelective(
            @Param("row") MoeBusinessClockInOutLog row, @Param("example") MoeBusinessClockInOutLogExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_business_clock_in_out_log
     *
     * @mbg.generated
     */
    int updateByExample(
            @Param("row") MoeBusinessClockInOutLog row, @Param("example") MoeBusinessClockInOutLogExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_business_clock_in_out_log
     *
     * @mbg.generated
     */
    int updateByPrimaryKeySelective(MoeBusinessClockInOutLog row);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_business_clock_in_out_log
     *
     * @mbg.generated
     */
    int updateByPrimaryKey(MoeBusinessClockInOutLog row);
}
