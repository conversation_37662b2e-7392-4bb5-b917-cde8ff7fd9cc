package com.moego.svc.organization.mapper.base;

import com.moego.svc.organization.entity.MoeAccountCompanyPreference;
import com.moego.svc.organization.entity.MoeAccountCompanyPreferenceExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface BaseMoeAccountCompanyPreferenceMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_account_company_preference
     *
     * @mbg.generated
     */
    long countByExample(MoeAccountCompanyPreferenceExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_account_company_preference
     *
     * @mbg.generated
     */
    int deleteByExample(MoeAccountCompanyPreferenceExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_account_company_preference
     *
     * @mbg.generated
     */
    int deleteByPrimaryKey(Long id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_account_company_preference
     *
     * @mbg.generated
     */
    int insert(MoeAccountCompanyPreference row);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_account_company_preference
     *
     * @mbg.generated
     */
    int insertSelective(MoeAccountCompanyPreference row);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_account_company_preference
     *
     * @mbg.generated
     */
    List<MoeAccountCompanyPreference> selectByExample(MoeAccountCompanyPreferenceExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_account_company_preference
     *
     * @mbg.generated
     */
    MoeAccountCompanyPreference selectByPrimaryKey(Long id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_account_company_preference
     *
     * @mbg.generated
     */
    int updateByExampleSelective(
            @Param("row") MoeAccountCompanyPreference row,
            @Param("example") MoeAccountCompanyPreferenceExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_account_company_preference
     *
     * @mbg.generated
     */
    int updateByExample(
            @Param("row") MoeAccountCompanyPreference row,
            @Param("example") MoeAccountCompanyPreferenceExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_account_company_preference
     *
     * @mbg.generated
     */
    int updateByPrimaryKeySelective(MoeAccountCompanyPreference row);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_account_company_preference
     *
     * @mbg.generated
     */
    int updateByPrimaryKey(MoeAccountCompanyPreference row);
}
