package com.moego.svc.organization.mapper.base;

import com.moego.svc.organization.entity.AbckamConfig;
import com.moego.svc.organization.entity.AbckamConfigExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface BaseAbckamConfigMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table abckam_config
     *
     * @mbg.generated
     */
    long countByExample(AbckamConfigExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table abckam_config
     *
     * @mbg.generated
     */
    int deleteByExample(AbckamConfigExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table abckam_config
     *
     * @mbg.generated
     */
    int deleteByPrimaryKey(Long id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table abckam_config
     *
     * @mbg.generated
     */
    int insert(AbckamConfig row);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table abckam_config
     *
     * @mbg.generated
     */
    int insertSelective(AbckamConfig row);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table abckam_config
     *
     * @mbg.generated
     */
    List<AbckamConfig> selectByExample(AbckamConfigExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table abckam_config
     *
     * @mbg.generated
     */
    AbckamConfig selectByPrimaryKey(Long id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table abckam_config
     *
     * @mbg.generated
     */
    int updateByExampleSelective(@Param("row") AbckamConfig row, @Param("example") AbckamConfigExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table abckam_config
     *
     * @mbg.generated
     */
    int updateByExample(@Param("row") AbckamConfig row, @Param("example") AbckamConfigExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table abckam_config
     *
     * @mbg.generated
     */
    int updateByPrimaryKeySelective(AbckamConfig row);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table abckam_config
     *
     * @mbg.generated
     */
    int updateByPrimaryKey(AbckamConfig row);
}
