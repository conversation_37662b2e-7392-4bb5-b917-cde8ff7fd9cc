package com.moego.svc.organization.mapper.base;

import com.moego.svc.organization.entity.MoeStaffWorkingLocation;
import com.moego.svc.organization.entity.MoeStaffWorkingLocationExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface BaseMoeStaffWorkingLocationMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_staff_working_location
     *
     * @mbg.generated
     */
    long countByExample(MoeStaffWorkingLocationExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_staff_working_location
     *
     * @mbg.generated
     */
    int deleteByExample(MoeStaffWorkingLocationExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_staff_working_location
     *
     * @mbg.generated
     */
    int deleteByPrimaryKey(Long id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_staff_working_location
     *
     * @mbg.generated
     */
    int insert(MoeStaffWorkingLocation row);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_staff_working_location
     *
     * @mbg.generated
     */
    int insertSelective(MoeStaffWorkingLocation row);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_staff_working_location
     *
     * @mbg.generated
     */
    List<MoeStaffWorkingLocation> selectByExample(MoeStaffWorkingLocationExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_staff_working_location
     *
     * @mbg.generated
     */
    MoeStaffWorkingLocation selectByPrimaryKey(Long id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_staff_working_location
     *
     * @mbg.generated
     */
    int updateByExampleSelective(
            @Param("row") MoeStaffWorkingLocation row, @Param("example") MoeStaffWorkingLocationExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_staff_working_location
     *
     * @mbg.generated
     */
    int updateByExample(
            @Param("row") MoeStaffWorkingLocation row, @Param("example") MoeStaffWorkingLocationExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_staff_working_location
     *
     * @mbg.generated
     */
    int updateByPrimaryKeySelective(MoeStaffWorkingLocation row);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_staff_working_location
     *
     * @mbg.generated
     */
    int updateByPrimaryKey(MoeStaffWorkingLocation row);
}
