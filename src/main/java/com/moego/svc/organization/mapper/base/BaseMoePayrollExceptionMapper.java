package com.moego.svc.organization.mapper.base;

import com.moego.svc.organization.entity.MoePayrollException;
import com.moego.svc.organization.entity.MoePayrollExceptionExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface BaseMoePayrollExceptionMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_payroll_exception
     *
     * @mbg.generated
     */
    long countByExample(MoePayrollExceptionExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_payroll_exception
     *
     * @mbg.generated
     */
    int deleteByExample(MoePayrollExceptionExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_payroll_exception
     *
     * @mbg.generated
     */
    int deleteByPrimaryKey(Integer id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_payroll_exception
     *
     * @mbg.generated
     */
    int insert(MoePayrollException row);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_payroll_exception
     *
     * @mbg.generated
     */
    int insertSelective(MoePayrollException row);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_payroll_exception
     *
     * @mbg.generated
     */
    List<MoePayrollException> selectByExampleWithBLOBs(MoePayrollExceptionExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_payroll_exception
     *
     * @mbg.generated
     */
    List<MoePayrollException> selectByExample(MoePayrollExceptionExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_payroll_exception
     *
     * @mbg.generated
     */
    MoePayrollException selectByPrimaryKey(Integer id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_payroll_exception
     *
     * @mbg.generated
     */
    int updateByExampleSelective(
            @Param("row") MoePayrollException row, @Param("example") MoePayrollExceptionExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_payroll_exception
     *
     * @mbg.generated
     */
    int updateByExampleWithBLOBs(
            @Param("row") MoePayrollException row, @Param("example") MoePayrollExceptionExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_payroll_exception
     *
     * @mbg.generated
     */
    int updateByExample(@Param("row") MoePayrollException row, @Param("example") MoePayrollExceptionExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_payroll_exception
     *
     * @mbg.generated
     */
    int updateByPrimaryKeySelective(MoePayrollException row);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_payroll_exception
     *
     * @mbg.generated
     */
    int updateByPrimaryKeyWithBLOBs(MoePayrollException row);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_payroll_exception
     *
     * @mbg.generated
     */
    int updateByPrimaryKey(MoePayrollException row);
}
