package com.moego.svc.organization.mapper.base;

import com.moego.svc.organization.entity.MoeStaffTracking;
import com.moego.svc.organization.entity.MoeStaffTrackingExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface BaseMoeStaffTrackingMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_staff_tracking
     *
     * @mbg.generated
     */
    long countByExample(MoeStaffTrackingExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_staff_tracking
     *
     * @mbg.generated
     */
    int deleteByExample(MoeStaffTrackingExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_staff_tracking
     *
     * @mbg.generated
     */
    int deleteByPrimaryKey(Long id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_staff_tracking
     *
     * @mbg.generated
     */
    int insert(MoeStaffTracking row);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_staff_tracking
     *
     * @mbg.generated
     */
    int insertSelective(MoeStaffTracking row);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_staff_tracking
     *
     * @mbg.generated
     */
    List<MoeStaffTracking> selectByExample(MoeStaffTrackingExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_staff_tracking
     *
     * @mbg.generated
     */
    MoeStaffTracking selectByPrimaryKey(Long id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_staff_tracking
     *
     * @mbg.generated
     */
    int updateByExampleSelective(@Param("row") MoeStaffTracking row, @Param("example") MoeStaffTrackingExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_staff_tracking
     *
     * @mbg.generated
     */
    int updateByExample(@Param("row") MoeStaffTracking row, @Param("example") MoeStaffTrackingExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_staff_tracking
     *
     * @mbg.generated
     */
    int updateByPrimaryKeySelective(MoeStaffTracking row);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_staff_tracking
     *
     * @mbg.generated
     */
    int updateByPrimaryKey(MoeStaffTracking row);
}
