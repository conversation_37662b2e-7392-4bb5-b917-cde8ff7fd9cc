package com.moego.svc.organization.mapper.base;

import com.moego.svc.organization.entity.MoeBusinessTax;
import com.moego.svc.organization.entity.MoeBusinessTaxExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface BaseMoeBusinessTaxMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_business_tax
     *
     * @mbg.generated
     */
    long countByExample(MoeBusinessTaxExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_business_tax
     *
     * @mbg.generated
     */
    int deleteByExample(MoeBusinessTaxExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_business_tax
     *
     * @mbg.generated
     */
    int deleteByPrimaryKey(Integer id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_business_tax
     *
     * @mbg.generated
     */
    int insert(MoeBusinessTax row);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_business_tax
     *
     * @mbg.generated
     */
    int insertSelective(MoeBusinessTax row);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_business_tax
     *
     * @mbg.generated
     */
    List<MoeBusinessTax> selectByExample(MoeBusinessTaxExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_business_tax
     *
     * @mbg.generated
     */
    MoeBusinessTax selectByPrimaryKey(Integer id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_business_tax
     *
     * @mbg.generated
     */
    int updateByExampleSelective(@Param("row") MoeBusinessTax row, @Param("example") MoeBusinessTaxExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_business_tax
     *
     * @mbg.generated
     */
    int updateByExample(@Param("row") MoeBusinessTax row, @Param("example") MoeBusinessTaxExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_business_tax
     *
     * @mbg.generated
     */
    int updateByPrimaryKeySelective(MoeBusinessTax row);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_business_tax
     *
     * @mbg.generated
     */
    int updateByPrimaryKey(MoeBusinessTax row);
}
