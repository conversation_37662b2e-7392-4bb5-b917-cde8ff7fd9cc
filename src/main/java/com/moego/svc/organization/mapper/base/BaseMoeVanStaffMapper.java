package com.moego.svc.organization.mapper.base;

import com.moego.svc.organization.entity.MoeVanStaff;
import com.moego.svc.organization.entity.MoeVanStaffExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface BaseMoeVanStaffMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_van_staff
     *
     * @mbg.generated
     */
    long countByExample(MoeVanStaffExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_van_staff
     *
     * @mbg.generated
     */
    int deleteByExample(MoeVanStaffExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_van_staff
     *
     * @mbg.generated
     */
    int deleteByPrimaryKey(Integer id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_van_staff
     *
     * @mbg.generated
     */
    int insert(MoeVanStaff row);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_van_staff
     *
     * @mbg.generated
     */
    int insertSelective(MoeVanStaff row);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_van_staff
     *
     * @mbg.generated
     */
    List<MoeVanStaff> selectByExample(MoeVanStaffExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_van_staff
     *
     * @mbg.generated
     */
    MoeVanStaff selectByPrimaryKey(Integer id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_van_staff
     *
     * @mbg.generated
     */
    int updateByExampleSelective(@Param("row") MoeVanStaff row, @Param("example") MoeVanStaffExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_van_staff
     *
     * @mbg.generated
     */
    int updateByExample(@Param("row") MoeVanStaff row, @Param("example") MoeVanStaffExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_van_staff
     *
     * @mbg.generated
     */
    int updateByPrimaryKeySelective(MoeVanStaff row);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_van_staff
     *
     * @mbg.generated
     */
    int updateByPrimaryKey(MoeVanStaff row);
}
