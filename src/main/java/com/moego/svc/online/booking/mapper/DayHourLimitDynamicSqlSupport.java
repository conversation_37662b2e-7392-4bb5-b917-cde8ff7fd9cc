package com.moego.svc.online.booking.mapper;

import jakarta.annotation.Generated;
import java.sql.JDBCType;
import java.util.Date;
import java.util.List;
import org.mybatis.dynamic.sql.AliasableSqlTable;
import org.mybatis.dynamic.sql.SqlColumn;

public final class DayHourLimitDynamicSqlSupport {
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: day_hour_limit")
    public static final DayHourLimit dayHourLimit = new DayHourLimit();

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: day_hour_limit.id")
    public static final SqlColumn<Long> id = dayHourLimit.id;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: day_hour_limit.type")
    public static final SqlColumn<Integer> type = dayHourLimit.type;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: day_hour_limit.pet_size_ids")
    public static final SqlColumn<List<Long>> petSizeIds = dayHourLimit.petSizeIds;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: day_hour_limit.pet_type_id")
    public static final SqlColumn<Long> petTypeId = dayHourLimit.petTypeId;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: day_hour_limit.is_all_breed")
    public static final SqlColumn<Boolean> isAllBreed = dayHourLimit.isAllBreed;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: day_hour_limit.breed_ids")
    public static final SqlColumn<List<Long>> breedIds = dayHourLimit.breedIds;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: day_hour_limit.service_ids")
    public static final SqlColumn<List<Long>> serviceIds = dayHourLimit.serviceIds;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: day_hour_limit.is_all_service")
    public static final SqlColumn<Boolean> isAllService = dayHourLimit.isAllService;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: day_hour_limit.capacity")
    public static final SqlColumn<Integer> capacity = dayHourLimit.capacity;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: day_hour_limit.created_at")
    public static final SqlColumn<Date> createdAt = dayHourLimit.createdAt;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: day_hour_limit.updated_at")
    public static final SqlColumn<Date> updatedAt = dayHourLimit.updatedAt;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: day_hour_limit")
    public static final class DayHourLimit extends AliasableSqlTable<DayHourLimit> {
        public final SqlColumn<Long> id = column("id", JDBCType.BIGINT);

        public final SqlColumn<Integer> type = column("\"type\"", JDBCType.INTEGER);

        public final SqlColumn<List<Long>> petSizeIds = column("pet_size_ids", JDBCType.OTHER, "com.moego.svc.online.booking.typehandler.JsonArrayTypeHandler");

        public final SqlColumn<Long> petTypeId = column("pet_type_id", JDBCType.BIGINT);

        public final SqlColumn<Boolean> isAllBreed = column("is_all_breed", JDBCType.BIT);

        public final SqlColumn<List<Long>> breedIds = column("breed_ids", JDBCType.OTHER, "com.moego.svc.online.booking.typehandler.JsonArrayTypeHandler");

        public final SqlColumn<List<Long>> serviceIds = column("service_ids", JDBCType.OTHER, "com.moego.svc.online.booking.typehandler.JsonArrayTypeHandler");

        public final SqlColumn<Boolean> isAllService = column("is_all_service", JDBCType.BIT);

        public final SqlColumn<Integer> capacity = column("capacity", JDBCType.INTEGER);

        public final SqlColumn<Date> createdAt = column("created_at", JDBCType.TIMESTAMP);

        public final SqlColumn<Date> updatedAt = column("updated_at", JDBCType.TIMESTAMP);

        public DayHourLimit() {
            super("day_hour_limit", DayHourLimit::new);
        }
    }
}