package com.moego.svc.online.booking.entity;

import jakarta.annotation.Generated;
import java.util.Date;
import java.util.List;

/**
 * Database Table Remarks:
 *   slot/time limit setting
 *
 * This class was generated by MyBatis Generator.
 * This class corresponds to the database table day_hour_limit
 */
public class DayHourLimit {
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: day_hour_limit.id")
    private Long id;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: day_hour_limit.type")
    private Integer type;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: day_hour_limit.pet_size_ids")
    private List<Long> petSizeIds;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: day_hour_limit.pet_type_id")
    private Long petTypeId;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: day_hour_limit.is_all_breed")
    private Boolean isAllBreed;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: day_hour_limit.breed_ids")
    private List<Long> breedIds;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: day_hour_limit.service_ids")
    private List<Long> serviceIds;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: day_hour_limit.is_all_service")
    private Boolean isAllService;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: day_hour_limit.capacity")
    private Integer capacity;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: day_hour_limit.created_at")
    private Date createdAt;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: day_hour_limit.updated_at")
    private Date updatedAt;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: day_hour_limit.id")
    public Long getId() {
        return id;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: day_hour_limit.id")
    public void setId(Long id) {
        this.id = id;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: day_hour_limit.type")
    public Integer getType() {
        return type;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: day_hour_limit.type")
    public void setType(Integer type) {
        this.type = type;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: day_hour_limit.pet_size_ids")
    public List<Long> getPetSizeIds() {
        return petSizeIds;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: day_hour_limit.pet_size_ids")
    public void setPetSizeIds(List<Long> petSizeIds) {
        this.petSizeIds = petSizeIds;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: day_hour_limit.pet_type_id")
    public Long getPetTypeId() {
        return petTypeId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: day_hour_limit.pet_type_id")
    public void setPetTypeId(Long petTypeId) {
        this.petTypeId = petTypeId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: day_hour_limit.is_all_breed")
    public Boolean getIsAllBreed() {
        return isAllBreed;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: day_hour_limit.is_all_breed")
    public void setIsAllBreed(Boolean isAllBreed) {
        this.isAllBreed = isAllBreed;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: day_hour_limit.breed_ids")
    public List<Long> getBreedIds() {
        return breedIds;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: day_hour_limit.breed_ids")
    public void setBreedIds(List<Long> breedIds) {
        this.breedIds = breedIds;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: day_hour_limit.service_ids")
    public List<Long> getServiceIds() {
        return serviceIds;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: day_hour_limit.service_ids")
    public void setServiceIds(List<Long> serviceIds) {
        this.serviceIds = serviceIds;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: day_hour_limit.is_all_service")
    public Boolean getIsAllService() {
        return isAllService;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: day_hour_limit.is_all_service")
    public void setIsAllService(Boolean isAllService) {
        this.isAllService = isAllService;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: day_hour_limit.capacity")
    public Integer getCapacity() {
        return capacity;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: day_hour_limit.capacity")
    public void setCapacity(Integer capacity) {
        this.capacity = capacity;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: day_hour_limit.created_at")
    public Date getCreatedAt() {
        return createdAt;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: day_hour_limit.created_at")
    public void setCreatedAt(Date createdAt) {
        this.createdAt = createdAt;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: day_hour_limit.updated_at")
    public Date getUpdatedAt() {
        return updatedAt;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: day_hour_limit.updated_at")
    public void setUpdatedAt(Date updatedAt) {
        this.updatedAt = updatedAt;
    }

    @Override
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: day_hour_limit")
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", type=").append(type);
        sb.append(", petSizeIds=").append(petSizeIds);
        sb.append(", petTypeId=").append(petTypeId);
        sb.append(", isAllBreed=").append(isAllBreed);
        sb.append(", breedIds=").append(breedIds);
        sb.append(", serviceIds=").append(serviceIds);
        sb.append(", isAllService=").append(isAllService);
        sb.append(", capacity=").append(capacity);
        sb.append(", createdAt=").append(createdAt);
        sb.append(", updatedAt=").append(updatedAt);
        sb.append("]");
        return sb.toString();
    }

    @Override
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: day_hour_limit")
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        DayHourLimit other = (DayHourLimit) that;
        return (this.getId() == null ? other.getId() == null : this.getId().equals(other.getId()))
            && (this.getType() == null ? other.getType() == null : this.getType().equals(other.getType()))
            && (this.getPetSizeIds() == null ? other.getPetSizeIds() == null : this.getPetSizeIds().equals(other.getPetSizeIds()))
            && (this.getPetTypeId() == null ? other.getPetTypeId() == null : this.getPetTypeId().equals(other.getPetTypeId()))
            && (this.getIsAllBreed() == null ? other.getIsAllBreed() == null : this.getIsAllBreed().equals(other.getIsAllBreed()))
            && (this.getBreedIds() == null ? other.getBreedIds() == null : this.getBreedIds().equals(other.getBreedIds()))
            && (this.getServiceIds() == null ? other.getServiceIds() == null : this.getServiceIds().equals(other.getServiceIds()))
            && (this.getIsAllService() == null ? other.getIsAllService() == null : this.getIsAllService().equals(other.getIsAllService()))
            && (this.getCapacity() == null ? other.getCapacity() == null : this.getCapacity().equals(other.getCapacity()))
            && (this.getCreatedAt() == null ? other.getCreatedAt() == null : this.getCreatedAt().equals(other.getCreatedAt()))
            && (this.getUpdatedAt() == null ? other.getUpdatedAt() == null : this.getUpdatedAt().equals(other.getUpdatedAt()));
    }

    @Override
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: day_hour_limit")
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getId() == null) ? 0 : getId().hashCode());
        result = prime * result + ((getType() == null) ? 0 : getType().hashCode());
        result = prime * result + ((getPetSizeIds() == null) ? 0 : getPetSizeIds().hashCode());
        result = prime * result + ((getPetTypeId() == null) ? 0 : getPetTypeId().hashCode());
        result = prime * result + ((getIsAllBreed() == null) ? 0 : getIsAllBreed().hashCode());
        result = prime * result + ((getBreedIds() == null) ? 0 : getBreedIds().hashCode());
        result = prime * result + ((getServiceIds() == null) ? 0 : getServiceIds().hashCode());
        result = prime * result + ((getIsAllService() == null) ? 0 : getIsAllService().hashCode());
        result = prime * result + ((getCapacity() == null) ? 0 : getCapacity().hashCode());
        result = prime * result + ((getCreatedAt() == null) ? 0 : getCreatedAt().hashCode());
        result = prime * result + ((getUpdatedAt() == null) ? 0 : getUpdatedAt().hashCode());
        return result;
    }
}