package com.moego.svc.appointment.mapper.pg;

import jakarta.annotation.Generated;
import java.sql.JDBCType;
import java.util.Date;
import org.mybatis.dynamic.sql.AliasableSqlTable;
import org.mybatis.dynamic.sql.SqlColumn;

public final class AppointmentTrackingNotificationLogDynamicSqlSupport {
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: appointment_tracking_notification_log")
    public static final AppointmentTrackingNotificationLog appointmentTrackingNotificationLog = new AppointmentTrackingNotificationLog();

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: appointment_tracking_notification_log.id")
    public static final SqlColumn<Long> id = appointmentTrackingNotificationLog.id;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: appointment_tracking_notification_log.company_id")
    public static final SqlColumn<Long> companyId = appointmentTrackingNotificationLog.companyId;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: appointment_tracking_notification_log.appointment_id")
    public static final SqlColumn<Long> appointmentId = appointmentTrackingNotificationLog.appointmentId;

    /**
     * Database Column Remarks:
     *   see api/moego/models/notification/v1/notification_enums.proto.NotificationType
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: appointment_tracking_notification_log.notification_type")
    public static final SqlColumn<Integer> notificationType = appointmentTrackingNotificationLog.notificationType;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: appointment_tracking_notification_log.notification_id")
    public static final SqlColumn<Long> notificationId = appointmentTrackingNotificationLog.notificationId;

    /**
     * Database Column Remarks:
     *   1-notification 2-sms
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: appointment_tracking_notification_log.channel")
    public static final SqlColumn<Integer> channel = appointmentTrackingNotificationLog.channel;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: appointment_tracking_notification_log.created_at")
    public static final SqlColumn<Date> createdAt = appointmentTrackingNotificationLog.createdAt;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: appointment_tracking_notification_log.updated_at")
    public static final SqlColumn<Date> updatedAt = appointmentTrackingNotificationLog.updatedAt;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: appointment_tracking_notification_log.deleted_at")
    public static final SqlColumn<Date> deletedAt = appointmentTrackingNotificationLog.deletedAt;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: appointment_tracking_notification_log")
    public static final class AppointmentTrackingNotificationLog extends AliasableSqlTable<AppointmentTrackingNotificationLog> {
        public final SqlColumn<Long> id = column("id", JDBCType.BIGINT);

        public final SqlColumn<Long> companyId = column("company_id", JDBCType.BIGINT);

        public final SqlColumn<Long> appointmentId = column("appointment_id", JDBCType.BIGINT);

        public final SqlColumn<Integer> notificationType = column("notification_type", JDBCType.INTEGER);

        public final SqlColumn<Long> notificationId = column("notification_id", JDBCType.BIGINT);

        public final SqlColumn<Integer> channel = column("channel", JDBCType.INTEGER);

        public final SqlColumn<Date> createdAt = column("created_at", JDBCType.TIMESTAMP);

        public final SqlColumn<Date> updatedAt = column("updated_at", JDBCType.TIMESTAMP);

        public final SqlColumn<Date> deletedAt = column("deleted_at", JDBCType.TIMESTAMP);

        public AppointmentTrackingNotificationLog() {
            super("appointment_tracking_notification_log", AppointmentTrackingNotificationLog::new);
        }
    }
}