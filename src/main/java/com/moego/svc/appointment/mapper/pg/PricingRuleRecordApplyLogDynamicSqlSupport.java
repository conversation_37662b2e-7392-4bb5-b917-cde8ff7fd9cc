package com.moego.svc.appointment.mapper.pg;

import com.moego.idl.models.offering.v2.PricingRule;
import jakarta.annotation.Generated;
import java.math.BigDecimal;
import java.sql.JDBCType;
import java.util.Date;
import org.mybatis.dynamic.sql.AliasableSqlTable;
import org.mybatis.dynamic.sql.SqlColumn;

public final class PricingRuleRecordApplyLogDynamicSqlSupport {
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: pricing_rule_record_apply_log")
    public static final PricingRuleRecordApplyLog pricingRuleRecordApplyLog = new PricingRuleRecordApplyLog();

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: pricing_rule_record_apply_log.id")
    public static final SqlColumn<Long> id = pricingRuleRecordApplyLog.id;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: pricing_rule_record_apply_log.business_id")
    public static final SqlColumn<Long> businessId = pricingRuleRecordApplyLog.businessId;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: pricing_rule_record_apply_log.company_id")
    public static final SqlColumn<Long> companyId = pricingRuleRecordApplyLog.companyId;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: pricing_rule_record_apply_log.source_id")
    public static final SqlColumn<Long> sourceId = pricingRuleRecordApplyLog.sourceId;

    /**
     * Database Column Remarks:
     *   source type, 1-appointment, 2-booking request
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: pricing_rule_record_apply_log.source_type")
    public static final SqlColumn<Integer> sourceType = pricingRuleRecordApplyLog.sourceType;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: pricing_rule_record_apply_log.pet_id")
    public static final SqlColumn<Long> petId = pricingRuleRecordApplyLog.petId;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: pricing_rule_record_apply_log.service_id")
    public static final SqlColumn<Long> serviceId = pricingRuleRecordApplyLog.serviceId;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: pricing_rule_record_apply_log.service_date")
    public static final SqlColumn<String> serviceDate = pricingRuleRecordApplyLog.serviceDate;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: pricing_rule_record_apply_log.original_price")
    public static final SqlColumn<BigDecimal> originalPrice = pricingRuleRecordApplyLog.originalPrice;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: pricing_rule_record_apply_log.adjusted_price")
    public static final SqlColumn<BigDecimal> adjustedPrice = pricingRuleRecordApplyLog.adjustedPrice;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: pricing_rule_record_apply_log.is_using_rule")
    public static final SqlColumn<Boolean> isUsingRule = pricingRuleRecordApplyLog.isUsingRule;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: pricing_rule_record_apply_log.pricing_rule")
    public static final SqlColumn<PricingRule> pricingRule = pricingRuleRecordApplyLog.pricingRule;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: pricing_rule_record_apply_log.created_at")
    public static final SqlColumn<Date> createdAt = pricingRuleRecordApplyLog.createdAt;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: pricing_rule_record_apply_log.deleted_at")
    public static final SqlColumn<Date> deletedAt = pricingRuleRecordApplyLog.deletedAt;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: pricing_rule_record_apply_log")
    public static final class PricingRuleRecordApplyLog extends AliasableSqlTable<PricingRuleRecordApplyLog> {
        public final SqlColumn<Long> id = column("id", JDBCType.BIGINT);

        public final SqlColumn<Long> businessId = column("business_id", JDBCType.BIGINT);

        public final SqlColumn<Long> companyId = column("company_id", JDBCType.BIGINT);

        public final SqlColumn<Long> sourceId = column("source_id", JDBCType.BIGINT);

        public final SqlColumn<Integer> sourceType = column("source_type", JDBCType.INTEGER);

        public final SqlColumn<Long> petId = column("pet_id", JDBCType.BIGINT);

        public final SqlColumn<Long> serviceId = column("service_id", JDBCType.BIGINT);

        public final SqlColumn<String> serviceDate = column("service_date", JDBCType.VARCHAR);

        public final SqlColumn<BigDecimal> originalPrice = column("original_price", JDBCType.NUMERIC);

        public final SqlColumn<BigDecimal> adjustedPrice = column("adjusted_price", JDBCType.NUMERIC);

        public final SqlColumn<Boolean> isUsingRule = column("is_using_rule", JDBCType.BIT);

        public final SqlColumn<PricingRule> pricingRule = column("pricing_rule", JDBCType.OTHER, "com.moego.svc.appointment.mapper.typehandler.PricingRuleRecordHandler");

        public final SqlColumn<Date> createdAt = column("created_at", JDBCType.TIMESTAMP);

        public final SqlColumn<Date> deletedAt = column("deleted_at", JDBCType.TIMESTAMP);

        public PricingRuleRecordApplyLog() {
            super("pricing_rule_record_apply_log", PricingRuleRecordApplyLog::new);
        }
    }
}