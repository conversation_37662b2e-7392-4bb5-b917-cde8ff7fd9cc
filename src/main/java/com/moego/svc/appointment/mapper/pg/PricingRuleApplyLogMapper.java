package com.moego.svc.appointment.mapper.pg;

import static com.moego.svc.appointment.mapper.pg.PricingRuleApplyLogDynamicSqlSupport.*;
import static org.mybatis.dynamic.sql.SqlBuilder.isEqualTo;

import com.moego.lib.common.autoconfigure.datasource.DynamicDataSource;
import com.moego.svc.appointment.domain.PricingRuleApplyLog;
import com.moego.svc.appointment.mapper.typehandler.PricingRuleHandler;
import jakarta.annotation.Generated;
import java.util.Collection;
import java.util.List;
import java.util.Optional;
import org.apache.ibatis.annotations.InsertProvider;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Options;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Result;
import org.apache.ibatis.annotations.ResultMap;
import org.apache.ibatis.annotations.Results;
import org.apache.ibatis.annotations.SelectProvider;
import org.apache.ibatis.type.JdbcType;
import org.mybatis.dynamic.sql.BasicColumn;
import org.mybatis.dynamic.sql.delete.DeleteDSLCompleter;
import org.mybatis.dynamic.sql.insert.render.InsertStatementProvider;
import org.mybatis.dynamic.sql.select.CountDSLCompleter;
import org.mybatis.dynamic.sql.select.SelectDSLCompleter;
import org.mybatis.dynamic.sql.select.render.SelectStatementProvider;
import org.mybatis.dynamic.sql.update.UpdateDSL;
import org.mybatis.dynamic.sql.update.UpdateDSLCompleter;
import org.mybatis.dynamic.sql.update.UpdateModel;
import org.mybatis.dynamic.sql.util.SqlProviderAdapter;
import org.mybatis.dynamic.sql.util.mybatis3.CommonCountMapper;
import org.mybatis.dynamic.sql.util.mybatis3.CommonDeleteMapper;
import org.mybatis.dynamic.sql.util.mybatis3.CommonSelectMapper;
import org.mybatis.dynamic.sql.util.mybatis3.CommonUpdateMapper;
import org.mybatis.dynamic.sql.util.mybatis3.MyBatis3Utils;

@Mapper
public interface PricingRuleApplyLogMapper extends CommonSelectMapper, CommonCountMapper, CommonDeleteMapper, CommonUpdateMapper, DynamicDataSource<PricingRuleApplyLogMapper> {
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: pricing_rule_apply_log")
    BasicColumn[] selectList = BasicColumn.columnList(id, businessId, companyId, appointmentId, petId, serviceId, originalPrice, servicePrice, pricingRule, createdAt, deletedAt, serviceDate, ruleGroupType, isUsingRule, sourceId, sourceType);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: pricing_rule_apply_log")
    @InsertProvider(type=SqlProviderAdapter.class, method="insert")
    @Options(useGeneratedKeys=true,keyProperty="row.id")
    int insert(InsertStatementProvider<PricingRuleApplyLog> insertStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: pricing_rule_apply_log")
    @InsertProvider(type=SqlProviderAdapter.class, method="insertMultipleWithGeneratedKeys")
    @Options(useGeneratedKeys=true,keyProperty="records.id")
    int insertMultiple(@Param("insertStatement") String insertStatement, @Param("records") List<PricingRuleApplyLog> records);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: pricing_rule_apply_log")
    @SelectProvider(type=SqlProviderAdapter.class, method="select")
    @Results(id="PricingRuleApplyLogResult", value = {
        @Result(column="id", property="id", jdbcType=JdbcType.BIGINT, id=true),
        @Result(column="business_id", property="businessId", jdbcType=JdbcType.BIGINT),
        @Result(column="company_id", property="companyId", jdbcType=JdbcType.BIGINT),
        @Result(column="appointment_id", property="appointmentId", jdbcType=JdbcType.BIGINT),
        @Result(column="pet_id", property="petId", jdbcType=JdbcType.BIGINT),
        @Result(column="service_id", property="serviceId", jdbcType=JdbcType.BIGINT),
        @Result(column="original_price", property="originalPrice", jdbcType=JdbcType.NUMERIC),
        @Result(column="service_price", property="servicePrice", jdbcType=JdbcType.NUMERIC),
        @Result(column="pricing_rule", property="pricingRule", typeHandler=PricingRuleHandler.class, jdbcType=JdbcType.OTHER),
        @Result(column="created_at", property="createdAt", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="deleted_at", property="deletedAt", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="service_date", property="serviceDate", jdbcType=JdbcType.VARCHAR),
        @Result(column="rule_group_type", property="ruleGroupType", jdbcType=JdbcType.INTEGER),
        @Result(column="is_using_rule", property="isUsingRule", jdbcType=JdbcType.BIT),
        @Result(column="source_id", property="sourceId", jdbcType=JdbcType.BIGINT),
        @Result(column="source_type", property="sourceType", jdbcType=JdbcType.INTEGER)
    })
    List<PricingRuleApplyLog> selectMany(SelectStatementProvider selectStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: pricing_rule_apply_log")
    @SelectProvider(type=SqlProviderAdapter.class, method="select")
    @ResultMap("PricingRuleApplyLogResult")
    Optional<PricingRuleApplyLog> selectOne(SelectStatementProvider selectStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: pricing_rule_apply_log")
    default long count(CountDSLCompleter completer) {
        return MyBatis3Utils.countFrom(this::count, pricingRuleApplyLog, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: pricing_rule_apply_log")
    default int delete(DeleteDSLCompleter completer) {
        return MyBatis3Utils.deleteFrom(this::delete, pricingRuleApplyLog, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: pricing_rule_apply_log")
    default int deleteByPrimaryKey(Long id_) {
        return delete(c -> 
            c.where(id, isEqualTo(id_))
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: pricing_rule_apply_log")
    default int insertMultiple(Collection<PricingRuleApplyLog> records) {
        return MyBatis3Utils.insertMultipleWithGeneratedKeys(this::insertMultiple, records, pricingRuleApplyLog, c ->
            c.map(businessId).toProperty("businessId")
            .map(companyId).toProperty("companyId")
            .map(appointmentId).toProperty("appointmentId")
            .map(petId).toProperty("petId")
            .map(serviceId).toProperty("serviceId")
            .map(originalPrice).toProperty("originalPrice")
            .map(servicePrice).toProperty("servicePrice")
            .map(pricingRule).toProperty("pricingRule")
            .map(createdAt).toProperty("createdAt")
            .map(deletedAt).toProperty("deletedAt")
            .map(serviceDate).toProperty("serviceDate")
            .map(ruleGroupType).toProperty("ruleGroupType")
            .map(isUsingRule).toProperty("isUsingRule")
            .map(sourceId).toProperty("sourceId")
            .map(sourceType).toProperty("sourceType")
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: pricing_rule_apply_log")
    default int insertSelective(PricingRuleApplyLog row) {
        return MyBatis3Utils.insert(this::insert, row, pricingRuleApplyLog, c ->
            c.map(businessId).toPropertyWhenPresent("businessId", row::getBusinessId)
            .map(companyId).toPropertyWhenPresent("companyId", row::getCompanyId)
            .map(appointmentId).toPropertyWhenPresent("appointmentId", row::getAppointmentId)
            .map(petId).toPropertyWhenPresent("petId", row::getPetId)
            .map(serviceId).toPropertyWhenPresent("serviceId", row::getServiceId)
            .map(originalPrice).toPropertyWhenPresent("originalPrice", row::getOriginalPrice)
            .map(servicePrice).toPropertyWhenPresent("servicePrice", row::getServicePrice)
            .map(pricingRule).toPropertyWhenPresent("pricingRule", row::getPricingRule)
            .map(createdAt).toPropertyWhenPresent("createdAt", row::getCreatedAt)
            .map(deletedAt).toPropertyWhenPresent("deletedAt", row::getDeletedAt)
            .map(serviceDate).toPropertyWhenPresent("serviceDate", row::getServiceDate)
            .map(ruleGroupType).toPropertyWhenPresent("ruleGroupType", row::getRuleGroupType)
            .map(isUsingRule).toPropertyWhenPresent("isUsingRule", row::getIsUsingRule)
            .map(sourceId).toPropertyWhenPresent("sourceId", row::getSourceId)
            .map(sourceType).toPropertyWhenPresent("sourceType", row::getSourceType)
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: pricing_rule_apply_log")
    default Optional<PricingRuleApplyLog> selectOne(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectOne(this::selectOne, selectList, pricingRuleApplyLog, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: pricing_rule_apply_log")
    default List<PricingRuleApplyLog> select(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectList(this::selectMany, selectList, pricingRuleApplyLog, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: pricing_rule_apply_log")
    default List<PricingRuleApplyLog> selectDistinct(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectDistinct(this::selectMany, selectList, pricingRuleApplyLog, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: pricing_rule_apply_log")
    default Optional<PricingRuleApplyLog> selectByPrimaryKey(Long id_) {
        return selectOne(c ->
            c.where(id, isEqualTo(id_))
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: pricing_rule_apply_log")
    default int update(UpdateDSLCompleter completer) {
        return MyBatis3Utils.update(this::update, pricingRuleApplyLog, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: pricing_rule_apply_log")
    static UpdateDSL<UpdateModel> updateAllColumns(PricingRuleApplyLog row, UpdateDSL<UpdateModel> dsl) {
        return dsl.set(businessId).equalTo(row::getBusinessId)
                .set(companyId).equalTo(row::getCompanyId)
                .set(appointmentId).equalTo(row::getAppointmentId)
                .set(petId).equalTo(row::getPetId)
                .set(serviceId).equalTo(row::getServiceId)
                .set(originalPrice).equalTo(row::getOriginalPrice)
                .set(servicePrice).equalTo(row::getServicePrice)
                .set(pricingRule).equalTo(row::getPricingRule)
                .set(createdAt).equalTo(row::getCreatedAt)
                .set(deletedAt).equalTo(row::getDeletedAt)
                .set(serviceDate).equalTo(row::getServiceDate)
                .set(ruleGroupType).equalTo(row::getRuleGroupType)
                .set(isUsingRule).equalTo(row::getIsUsingRule)
                .set(sourceId).equalTo(row::getSourceId)
                .set(sourceType).equalTo(row::getSourceType);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: pricing_rule_apply_log")
    static UpdateDSL<UpdateModel> updateSelectiveColumns(PricingRuleApplyLog row, UpdateDSL<UpdateModel> dsl) {
        return dsl.set(businessId).equalToWhenPresent(row::getBusinessId)
                .set(companyId).equalToWhenPresent(row::getCompanyId)
                .set(appointmentId).equalToWhenPresent(row::getAppointmentId)
                .set(petId).equalToWhenPresent(row::getPetId)
                .set(serviceId).equalToWhenPresent(row::getServiceId)
                .set(originalPrice).equalToWhenPresent(row::getOriginalPrice)
                .set(servicePrice).equalToWhenPresent(row::getServicePrice)
                .set(pricingRule).equalToWhenPresent(row::getPricingRule)
                .set(createdAt).equalToWhenPresent(row::getCreatedAt)
                .set(deletedAt).equalToWhenPresent(row::getDeletedAt)
                .set(serviceDate).equalToWhenPresent(row::getServiceDate)
                .set(ruleGroupType).equalToWhenPresent(row::getRuleGroupType)
                .set(isUsingRule).equalToWhenPresent(row::getIsUsingRule)
                .set(sourceId).equalToWhenPresent(row::getSourceId)
                .set(sourceType).equalToWhenPresent(row::getSourceType);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: pricing_rule_apply_log")
    default int updateByPrimaryKeySelective(PricingRuleApplyLog row) {
        return update(c ->
            c.set(businessId).equalToWhenPresent(row::getBusinessId)
            .set(companyId).equalToWhenPresent(row::getCompanyId)
            .set(appointmentId).equalToWhenPresent(row::getAppointmentId)
            .set(petId).equalToWhenPresent(row::getPetId)
            .set(serviceId).equalToWhenPresent(row::getServiceId)
            .set(originalPrice).equalToWhenPresent(row::getOriginalPrice)
            .set(servicePrice).equalToWhenPresent(row::getServicePrice)
            .set(pricingRule).equalToWhenPresent(row::getPricingRule)
            .set(createdAt).equalToWhenPresent(row::getCreatedAt)
            .set(deletedAt).equalToWhenPresent(row::getDeletedAt)
            .set(serviceDate).equalToWhenPresent(row::getServiceDate)
            .set(ruleGroupType).equalToWhenPresent(row::getRuleGroupType)
            .set(isUsingRule).equalToWhenPresent(row::getIsUsingRule)
            .set(sourceId).equalToWhenPresent(row::getSourceId)
            .set(sourceType).equalToWhenPresent(row::getSourceType)
            .where(id, isEqualTo(row::getId))
        );
    }
}