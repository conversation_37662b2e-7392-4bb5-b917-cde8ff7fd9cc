package com.moego.svc.appointment.mapper.pg;

import com.moego.svc.appointment.dto.DailyReportContentDTO;
import jakarta.annotation.Generated;
import java.sql.JDBCType;
import java.util.Date;
import org.mybatis.dynamic.sql.AliasableSqlTable;
import org.mybatis.dynamic.sql.SqlColumn;

public final class DailyReportConfigDynamicSqlSupport {
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: daily_report_config")
    public static final DailyReportConfig dailyReportConfig = new DailyReportConfig();

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: daily_report_config.id")
    public static final SqlColumn<Long> id = dailyReportConfig.id;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: daily_report_config.business_id")
    public static final SqlColumn<Long> businessId = dailyReportConfig.businessId;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: daily_report_config.company_id")
    public static final SqlColumn<Long> companyId = dailyReportConfig.companyId;

    /**
     * Database Column Remarks:
     *   customer id
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: daily_report_config.customer_id")
    public static final SqlColumn<Long> customerId = dailyReportConfig.customerId;

    /**
     * Database Column Remarks:
     *   appointment id
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: daily_report_config.appointment_id")
    public static final SqlColumn<Long> appointmentId = dailyReportConfig.appointmentId;

    /**
     * Database Column Remarks:
     *   pet id
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: daily_report_config.pet_id")
    public static final SqlColumn<Long> petId = dailyReportConfig.petId;

    /**
     * Database Column Remarks:
     *   uuid
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: daily_report_config.uuid")
    public static final SqlColumn<String> uuid = dailyReportConfig.uuid;

    /**
     * Database Column Remarks:
     *   report template
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: daily_report_config.template_json")
    public static final SqlColumn<DailyReportContentDTO> templateJson = dailyReportConfig.templateJson;

    /**
     * Database Column Remarks:
     *   last update staff id
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: daily_report_config.update_by")
    public static final SqlColumn<Long> updateBy = dailyReportConfig.updateBy;

    /**
     * Database Column Remarks:
     *   create time
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: daily_report_config.create_time")
    public static final SqlColumn<Date> createTime = dailyReportConfig.createTime;

    /**
     * Database Column Remarks:
     *   update time
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: daily_report_config.update_time")
    public static final SqlColumn<Date> updateTime = dailyReportConfig.updateTime;

    /**
     * Database Column Remarks:
     *   service date
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: daily_report_config.service_date")
    public static final SqlColumn<Date> serviceDate = dailyReportConfig.serviceDate;

    /**
     * Database Column Remarks:
     *   report status: created/draft/ready/sent
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: daily_report_config.status")
    public static final SqlColumn<String> status = dailyReportConfig.status;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: daily_report_config")
    public static final class DailyReportConfig extends AliasableSqlTable<DailyReportConfig> {
        public final SqlColumn<Long> id = column("id", JDBCType.BIGINT);

        public final SqlColumn<Long> businessId = column("business_id", JDBCType.BIGINT);

        public final SqlColumn<Long> companyId = column("company_id", JDBCType.BIGINT);

        public final SqlColumn<Long> customerId = column("customer_id", JDBCType.BIGINT);

        public final SqlColumn<Long> appointmentId = column("appointment_id", JDBCType.BIGINT);

        public final SqlColumn<Long> petId = column("pet_id", JDBCType.BIGINT);

        public final SqlColumn<String> uuid = column("uuid", JDBCType.VARCHAR);

        public final SqlColumn<DailyReportContentDTO> templateJson = column("template_json", JDBCType.OTHER, "com.moego.svc.appointment.mapper.typehandler.DailyReportContentHandler");

        public final SqlColumn<Long> updateBy = column("update_by", JDBCType.BIGINT);

        public final SqlColumn<Date> createTime = column("create_time", JDBCType.TIMESTAMP);

        public final SqlColumn<Date> updateTime = column("update_time", JDBCType.TIMESTAMP);

        public final SqlColumn<Date> serviceDate = column("service_date", JDBCType.DATE);

        public final SqlColumn<String> status = column("status", JDBCType.VARCHAR);

        public DailyReportConfig() {
            super("daily_report_config", DailyReportConfig::new);
        }
    }
}