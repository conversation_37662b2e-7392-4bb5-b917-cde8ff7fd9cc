package com.moego.svc.appointment.mapper.pg;

import static com.moego.svc.appointment.mapper.pg.PricingRuleRecordApplyLogDynamicSqlSupport.*;
import static org.mybatis.dynamic.sql.SqlBuilder.isEqualTo;

import com.moego.lib.common.autoconfigure.datasource.DynamicDataSource;
import com.moego.svc.appointment.domain.PricingRuleRecordApplyLog;
import com.moego.svc.appointment.mapper.typehandler.PricingRuleRecordHandler;
import jakarta.annotation.Generated;
import java.util.Collection;
import java.util.List;
import java.util.Optional;
import org.apache.ibatis.annotations.InsertProvider;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Options;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Result;
import org.apache.ibatis.annotations.ResultMap;
import org.apache.ibatis.annotations.Results;
import org.apache.ibatis.annotations.SelectProvider;
import org.apache.ibatis.type.JdbcType;
import org.mybatis.dynamic.sql.BasicColumn;
import org.mybatis.dynamic.sql.delete.DeleteDSLCompleter;
import org.mybatis.dynamic.sql.insert.render.InsertStatementProvider;
import org.mybatis.dynamic.sql.select.CountDSLCompleter;
import org.mybatis.dynamic.sql.select.SelectDSLCompleter;
import org.mybatis.dynamic.sql.select.render.SelectStatementProvider;
import org.mybatis.dynamic.sql.update.UpdateDSL;
import org.mybatis.dynamic.sql.update.UpdateDSLCompleter;
import org.mybatis.dynamic.sql.update.UpdateModel;
import org.mybatis.dynamic.sql.util.SqlProviderAdapter;
import org.mybatis.dynamic.sql.util.mybatis3.CommonCountMapper;
import org.mybatis.dynamic.sql.util.mybatis3.CommonDeleteMapper;
import org.mybatis.dynamic.sql.util.mybatis3.CommonSelectMapper;
import org.mybatis.dynamic.sql.util.mybatis3.CommonUpdateMapper;
import org.mybatis.dynamic.sql.util.mybatis3.MyBatis3Utils;

@Mapper
public interface PricingRuleRecordApplyLogMapper extends CommonSelectMapper, CommonCountMapper, CommonDeleteMapper, CommonUpdateMapper, DynamicDataSource<PricingRuleRecordApplyLogMapper> {
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: pricing_rule_record_apply_log")
    BasicColumn[] selectList = BasicColumn.columnList(id, businessId, companyId, sourceId, sourceType, petId, serviceId, serviceDate, originalPrice, adjustedPrice, isUsingRule, pricingRule, createdAt, deletedAt);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: pricing_rule_record_apply_log")
    @InsertProvider(type=SqlProviderAdapter.class, method="insert")
    @Options(useGeneratedKeys=true,keyProperty="row.id")
    int insert(InsertStatementProvider<PricingRuleRecordApplyLog> insertStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: pricing_rule_record_apply_log")
    @InsertProvider(type=SqlProviderAdapter.class, method="insertMultipleWithGeneratedKeys")
    @Options(useGeneratedKeys=true,keyProperty="records.id")
    int insertMultiple(@Param("insertStatement") String insertStatement, @Param("records") List<PricingRuleRecordApplyLog> records);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: pricing_rule_record_apply_log")
    @SelectProvider(type=SqlProviderAdapter.class, method="select")
    @Results(id="PricingRuleRecordApplyLogResult", value = {
        @Result(column="id", property="id", jdbcType=JdbcType.BIGINT, id=true),
        @Result(column="business_id", property="businessId", jdbcType=JdbcType.BIGINT),
        @Result(column="company_id", property="companyId", jdbcType=JdbcType.BIGINT),
        @Result(column="source_id", property="sourceId", jdbcType=JdbcType.BIGINT),
        @Result(column="source_type", property="sourceType", jdbcType=JdbcType.INTEGER),
        @Result(column="pet_id", property="petId", jdbcType=JdbcType.BIGINT),
        @Result(column="service_id", property="serviceId", jdbcType=JdbcType.BIGINT),
        @Result(column="service_date", property="serviceDate", jdbcType=JdbcType.VARCHAR),
        @Result(column="original_price", property="originalPrice", jdbcType=JdbcType.NUMERIC),
        @Result(column="adjusted_price", property="adjustedPrice", jdbcType=JdbcType.NUMERIC),
        @Result(column="is_using_rule", property="isUsingRule", jdbcType=JdbcType.BIT),
        @Result(column="pricing_rule", property="pricingRule", typeHandler=PricingRuleRecordHandler.class, jdbcType=JdbcType.OTHER),
        @Result(column="created_at", property="createdAt", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="deleted_at", property="deletedAt", jdbcType=JdbcType.TIMESTAMP)
    })
    List<PricingRuleRecordApplyLog> selectMany(SelectStatementProvider selectStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: pricing_rule_record_apply_log")
    @SelectProvider(type=SqlProviderAdapter.class, method="select")
    @ResultMap("PricingRuleRecordApplyLogResult")
    Optional<PricingRuleRecordApplyLog> selectOne(SelectStatementProvider selectStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: pricing_rule_record_apply_log")
    default long count(CountDSLCompleter completer) {
        return MyBatis3Utils.countFrom(this::count, pricingRuleRecordApplyLog, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: pricing_rule_record_apply_log")
    default int delete(DeleteDSLCompleter completer) {
        return MyBatis3Utils.deleteFrom(this::delete, pricingRuleRecordApplyLog, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: pricing_rule_record_apply_log")
    default int deleteByPrimaryKey(Long id_) {
        return delete(c -> 
            c.where(id, isEqualTo(id_))
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: pricing_rule_record_apply_log")
    default int insertMultiple(Collection<PricingRuleRecordApplyLog> records) {
        return MyBatis3Utils.insertMultipleWithGeneratedKeys(this::insertMultiple, records, pricingRuleRecordApplyLog, c ->
            c.map(businessId).toProperty("businessId")
            .map(companyId).toProperty("companyId")
            .map(sourceId).toProperty("sourceId")
            .map(sourceType).toProperty("sourceType")
            .map(petId).toProperty("petId")
            .map(serviceId).toProperty("serviceId")
            .map(serviceDate).toProperty("serviceDate")
            .map(originalPrice).toProperty("originalPrice")
            .map(adjustedPrice).toProperty("adjustedPrice")
            .map(isUsingRule).toProperty("isUsingRule")
            .map(pricingRule).toProperty("pricingRule")
            .map(createdAt).toProperty("createdAt")
            .map(deletedAt).toProperty("deletedAt")
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: pricing_rule_record_apply_log")
    default int insertSelective(PricingRuleRecordApplyLog row) {
        return MyBatis3Utils.insert(this::insert, row, pricingRuleRecordApplyLog, c ->
            c.map(businessId).toPropertyWhenPresent("businessId", row::getBusinessId)
            .map(companyId).toPropertyWhenPresent("companyId", row::getCompanyId)
            .map(sourceId).toPropertyWhenPresent("sourceId", row::getSourceId)
            .map(sourceType).toPropertyWhenPresent("sourceType", row::getSourceType)
            .map(petId).toPropertyWhenPresent("petId", row::getPetId)
            .map(serviceId).toPropertyWhenPresent("serviceId", row::getServiceId)
            .map(serviceDate).toPropertyWhenPresent("serviceDate", row::getServiceDate)
            .map(originalPrice).toPropertyWhenPresent("originalPrice", row::getOriginalPrice)
            .map(adjustedPrice).toPropertyWhenPresent("adjustedPrice", row::getAdjustedPrice)
            .map(isUsingRule).toPropertyWhenPresent("isUsingRule", row::getIsUsingRule)
            .map(pricingRule).toPropertyWhenPresent("pricingRule", row::getPricingRule)
            .map(createdAt).toPropertyWhenPresent("createdAt", row::getCreatedAt)
            .map(deletedAt).toPropertyWhenPresent("deletedAt", row::getDeletedAt)
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: pricing_rule_record_apply_log")
    default Optional<PricingRuleRecordApplyLog> selectOne(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectOne(this::selectOne, selectList, pricingRuleRecordApplyLog, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: pricing_rule_record_apply_log")
    default List<PricingRuleRecordApplyLog> select(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectList(this::selectMany, selectList, pricingRuleRecordApplyLog, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: pricing_rule_record_apply_log")
    default List<PricingRuleRecordApplyLog> selectDistinct(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectDistinct(this::selectMany, selectList, pricingRuleRecordApplyLog, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: pricing_rule_record_apply_log")
    default Optional<PricingRuleRecordApplyLog> selectByPrimaryKey(Long id_) {
        return selectOne(c ->
            c.where(id, isEqualTo(id_))
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: pricing_rule_record_apply_log")
    default int update(UpdateDSLCompleter completer) {
        return MyBatis3Utils.update(this::update, pricingRuleRecordApplyLog, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: pricing_rule_record_apply_log")
    static UpdateDSL<UpdateModel> updateAllColumns(PricingRuleRecordApplyLog row, UpdateDSL<UpdateModel> dsl) {
        return dsl.set(businessId).equalTo(row::getBusinessId)
                .set(companyId).equalTo(row::getCompanyId)
                .set(sourceId).equalTo(row::getSourceId)
                .set(sourceType).equalTo(row::getSourceType)
                .set(petId).equalTo(row::getPetId)
                .set(serviceId).equalTo(row::getServiceId)
                .set(serviceDate).equalTo(row::getServiceDate)
                .set(originalPrice).equalTo(row::getOriginalPrice)
                .set(adjustedPrice).equalTo(row::getAdjustedPrice)
                .set(isUsingRule).equalTo(row::getIsUsingRule)
                .set(pricingRule).equalTo(row::getPricingRule)
                .set(createdAt).equalTo(row::getCreatedAt)
                .set(deletedAt).equalTo(row::getDeletedAt);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: pricing_rule_record_apply_log")
    static UpdateDSL<UpdateModel> updateSelectiveColumns(PricingRuleRecordApplyLog row, UpdateDSL<UpdateModel> dsl) {
        return dsl.set(businessId).equalToWhenPresent(row::getBusinessId)
                .set(companyId).equalToWhenPresent(row::getCompanyId)
                .set(sourceId).equalToWhenPresent(row::getSourceId)
                .set(sourceType).equalToWhenPresent(row::getSourceType)
                .set(petId).equalToWhenPresent(row::getPetId)
                .set(serviceId).equalToWhenPresent(row::getServiceId)
                .set(serviceDate).equalToWhenPresent(row::getServiceDate)
                .set(originalPrice).equalToWhenPresent(row::getOriginalPrice)
                .set(adjustedPrice).equalToWhenPresent(row::getAdjustedPrice)
                .set(isUsingRule).equalToWhenPresent(row::getIsUsingRule)
                .set(pricingRule).equalToWhenPresent(row::getPricingRule)
                .set(createdAt).equalToWhenPresent(row::getCreatedAt)
                .set(deletedAt).equalToWhenPresent(row::getDeletedAt);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: pricing_rule_record_apply_log")
    default int updateByPrimaryKeySelective(PricingRuleRecordApplyLog row) {
        return update(c ->
            c.set(businessId).equalToWhenPresent(row::getBusinessId)
            .set(companyId).equalToWhenPresent(row::getCompanyId)
            .set(sourceId).equalToWhenPresent(row::getSourceId)
            .set(sourceType).equalToWhenPresent(row::getSourceType)
            .set(petId).equalToWhenPresent(row::getPetId)
            .set(serviceId).equalToWhenPresent(row::getServiceId)
            .set(serviceDate).equalToWhenPresent(row::getServiceDate)
            .set(originalPrice).equalToWhenPresent(row::getOriginalPrice)
            .set(adjustedPrice).equalToWhenPresent(row::getAdjustedPrice)
            .set(isUsingRule).equalToWhenPresent(row::getIsUsingRule)
            .set(pricingRule).equalToWhenPresent(row::getPricingRule)
            .set(createdAt).equalToWhenPresent(row::getCreatedAt)
            .set(deletedAt).equalToWhenPresent(row::getDeletedAt)
            .where(id, isEqualTo(row::getId))
        );
    }
}