package com.moego.svc.appointment.mapper.pg;

import static com.moego.svc.appointment.mapper.pg.BoardingSplitLodgingDynamicSqlSupport.*;
import static org.mybatis.dynamic.sql.SqlBuilder.isEqualTo;

import com.moego.lib.common.autoconfigure.datasource.DynamicDataSource;
import com.moego.svc.appointment.domain.BoardingSplitLodging;
import jakarta.annotation.Generated;
import java.util.Collection;
import java.util.List;
import java.util.Optional;
import org.apache.ibatis.annotations.InsertProvider;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Options;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Result;
import org.apache.ibatis.annotations.ResultMap;
import org.apache.ibatis.annotations.Results;
import org.apache.ibatis.annotations.SelectProvider;
import org.apache.ibatis.type.JdbcType;
import org.mybatis.dynamic.sql.BasicColumn;
import org.mybatis.dynamic.sql.delete.DeleteDSLCompleter;
import org.mybatis.dynamic.sql.insert.render.InsertStatementProvider;
import org.mybatis.dynamic.sql.select.CountDSLCompleter;
import org.mybatis.dynamic.sql.select.SelectDSLCompleter;
import org.mybatis.dynamic.sql.select.render.SelectStatementProvider;
import org.mybatis.dynamic.sql.update.UpdateDSL;
import org.mybatis.dynamic.sql.update.UpdateDSLCompleter;
import org.mybatis.dynamic.sql.update.UpdateModel;
import org.mybatis.dynamic.sql.util.SqlProviderAdapter;
import org.mybatis.dynamic.sql.util.mybatis3.CommonCountMapper;
import org.mybatis.dynamic.sql.util.mybatis3.CommonDeleteMapper;
import org.mybatis.dynamic.sql.util.mybatis3.CommonSelectMapper;
import org.mybatis.dynamic.sql.util.mybatis3.CommonUpdateMapper;
import org.mybatis.dynamic.sql.util.mybatis3.MyBatis3Utils;

@Mapper
public interface BoardingSplitLodgingMapper extends CommonSelectMapper, CommonCountMapper, CommonDeleteMapper, CommonUpdateMapper, DynamicDataSource<BoardingSplitLodgingMapper> {
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: boarding_split_lodging")
    BasicColumn[] selectList = BasicColumn.columnList(id, appointmentId, petDetailId, petId, startDateTime, endDateTime, lodgingId, price, currency, createdAt, isApplicable);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: boarding_split_lodging")
    @InsertProvider(type=SqlProviderAdapter.class, method="insert")
    @Options(useGeneratedKeys=true,keyProperty="row.id")
    int insert(InsertStatementProvider<BoardingSplitLodging> insertStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: boarding_split_lodging")
    @InsertProvider(type=SqlProviderAdapter.class, method="insertMultipleWithGeneratedKeys")
    @Options(useGeneratedKeys=true,keyProperty="records.id")
    int insertMultiple(@Param("insertStatement") String insertStatement, @Param("records") List<BoardingSplitLodging> records);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: boarding_split_lodging")
    @SelectProvider(type=SqlProviderAdapter.class, method="select")
    @Results(id="BoardingSplitLodgingResult", value = {
        @Result(column="id", property="id", jdbcType=JdbcType.BIGINT, id=true),
        @Result(column="appointment_id", property="appointmentId", jdbcType=JdbcType.BIGINT),
        @Result(column="pet_detail_id", property="petDetailId", jdbcType=JdbcType.BIGINT),
        @Result(column="pet_id", property="petId", jdbcType=JdbcType.BIGINT),
        @Result(column="start_date_time", property="startDateTime", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="end_date_time", property="endDateTime", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="lodging_id", property="lodgingId", jdbcType=JdbcType.BIGINT),
        @Result(column="price", property="price", jdbcType=JdbcType.NUMERIC),
        @Result(column="currency", property="currency", jdbcType=JdbcType.CHAR),
        @Result(column="created_at", property="createdAt", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="is_applicable", property="isApplicable", jdbcType=JdbcType.BIT)
    })
    List<BoardingSplitLodging> selectMany(SelectStatementProvider selectStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: boarding_split_lodging")
    @SelectProvider(type=SqlProviderAdapter.class, method="select")
    @ResultMap("BoardingSplitLodgingResult")
    Optional<BoardingSplitLodging> selectOne(SelectStatementProvider selectStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: boarding_split_lodging")
    default long count(CountDSLCompleter completer) {
        return MyBatis3Utils.countFrom(this::count, boardingSplitLodging, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: boarding_split_lodging")
    default int delete(DeleteDSLCompleter completer) {
        return MyBatis3Utils.deleteFrom(this::delete, boardingSplitLodging, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: boarding_split_lodging")
    default int deleteByPrimaryKey(Long id_) {
        return delete(c -> 
            c.where(id, isEqualTo(id_))
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: boarding_split_lodging")
    default int insertMultiple(Collection<BoardingSplitLodging> records) {
        return MyBatis3Utils.insertMultipleWithGeneratedKeys(this::insertMultiple, records, boardingSplitLodging, c ->
            c.map(appointmentId).toProperty("appointmentId")
            .map(petDetailId).toProperty("petDetailId")
            .map(petId).toProperty("petId")
            .map(startDateTime).toProperty("startDateTime")
            .map(endDateTime).toProperty("endDateTime")
            .map(lodgingId).toProperty("lodgingId")
            .map(price).toProperty("price")
            .map(currency).toProperty("currency")
            .map(createdAt).toProperty("createdAt")
            .map(isApplicable).toProperty("isApplicable")
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: boarding_split_lodging")
    default int insertSelective(BoardingSplitLodging row) {
        return MyBatis3Utils.insert(this::insert, row, boardingSplitLodging, c ->
            c.map(appointmentId).toPropertyWhenPresent("appointmentId", row::getAppointmentId)
            .map(petDetailId).toPropertyWhenPresent("petDetailId", row::getPetDetailId)
            .map(petId).toPropertyWhenPresent("petId", row::getPetId)
            .map(startDateTime).toPropertyWhenPresent("startDateTime", row::getStartDateTime)
            .map(endDateTime).toPropertyWhenPresent("endDateTime", row::getEndDateTime)
            .map(lodgingId).toPropertyWhenPresent("lodgingId", row::getLodgingId)
            .map(price).toPropertyWhenPresent("price", row::getPrice)
            .map(currency).toPropertyWhenPresent("currency", row::getCurrency)
            .map(createdAt).toPropertyWhenPresent("createdAt", row::getCreatedAt)
            .map(isApplicable).toPropertyWhenPresent("isApplicable", row::getIsApplicable)
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: boarding_split_lodging")
    default Optional<BoardingSplitLodging> selectOne(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectOne(this::selectOne, selectList, boardingSplitLodging, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: boarding_split_lodging")
    default List<BoardingSplitLodging> select(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectList(this::selectMany, selectList, boardingSplitLodging, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: boarding_split_lodging")
    default List<BoardingSplitLodging> selectDistinct(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectDistinct(this::selectMany, selectList, boardingSplitLodging, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: boarding_split_lodging")
    default Optional<BoardingSplitLodging> selectByPrimaryKey(Long id_) {
        return selectOne(c ->
            c.where(id, isEqualTo(id_))
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: boarding_split_lodging")
    default int update(UpdateDSLCompleter completer) {
        return MyBatis3Utils.update(this::update, boardingSplitLodging, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: boarding_split_lodging")
    static UpdateDSL<UpdateModel> updateAllColumns(BoardingSplitLodging row, UpdateDSL<UpdateModel> dsl) {
        return dsl.set(appointmentId).equalTo(row::getAppointmentId)
                .set(petDetailId).equalTo(row::getPetDetailId)
                .set(petId).equalTo(row::getPetId)
                .set(startDateTime).equalTo(row::getStartDateTime)
                .set(endDateTime).equalTo(row::getEndDateTime)
                .set(lodgingId).equalTo(row::getLodgingId)
                .set(price).equalTo(row::getPrice)
                .set(currency).equalTo(row::getCurrency)
                .set(createdAt).equalTo(row::getCreatedAt)
                .set(isApplicable).equalTo(row::getIsApplicable);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: boarding_split_lodging")
    static UpdateDSL<UpdateModel> updateSelectiveColumns(BoardingSplitLodging row, UpdateDSL<UpdateModel> dsl) {
        return dsl.set(appointmentId).equalToWhenPresent(row::getAppointmentId)
                .set(petDetailId).equalToWhenPresent(row::getPetDetailId)
                .set(petId).equalToWhenPresent(row::getPetId)
                .set(startDateTime).equalToWhenPresent(row::getStartDateTime)
                .set(endDateTime).equalToWhenPresent(row::getEndDateTime)
                .set(lodgingId).equalToWhenPresent(row::getLodgingId)
                .set(price).equalToWhenPresent(row::getPrice)
                .set(currency).equalToWhenPresent(row::getCurrency)
                .set(createdAt).equalToWhenPresent(row::getCreatedAt)
                .set(isApplicable).equalToWhenPresent(row::getIsApplicable);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: boarding_split_lodging")
    default int updateByPrimaryKeySelective(BoardingSplitLodging row) {
        return update(c ->
            c.set(appointmentId).equalToWhenPresent(row::getAppointmentId)
            .set(petDetailId).equalToWhenPresent(row::getPetDetailId)
            .set(petId).equalToWhenPresent(row::getPetId)
            .set(startDateTime).equalToWhenPresent(row::getStartDateTime)
            .set(endDateTime).equalToWhenPresent(row::getEndDateTime)
            .set(lodgingId).equalToWhenPresent(row::getLodgingId)
            .set(price).equalToWhenPresent(row::getPrice)
            .set(currency).equalToWhenPresent(row::getCurrency)
            .set(createdAt).equalToWhenPresent(row::getCreatedAt)
            .set(isApplicable).equalToWhenPresent(row::getIsApplicable)
            .where(id, isEqualTo(row::getId))
        );
    }
}