package com.moego.svc.appointment.mapper.pg;

import static com.moego.svc.appointment.mapper.pg.DailyReportSendLogDynamicSqlSupport.*;
import static org.mybatis.dynamic.sql.SqlBuilder.isEqualTo;

import com.moego.lib.common.autoconfigure.datasource.DynamicDataSource;
import com.moego.svc.appointment.domain.DailyReportSendLog;
import com.moego.svc.appointment.mapper.typehandler.DailyReportContentHandler;
import com.moego.svc.appointment.mapper.typehandler.DailyReportSendMethodHandler;
import jakarta.annotation.Generated;
import java.util.Collection;
import java.util.List;
import java.util.Optional;
import org.apache.ibatis.annotations.InsertProvider;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Options;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Result;
import org.apache.ibatis.annotations.ResultMap;
import org.apache.ibatis.annotations.Results;
import org.apache.ibatis.annotations.SelectProvider;
import org.apache.ibatis.type.JdbcType;
import org.mybatis.dynamic.sql.BasicColumn;
import org.mybatis.dynamic.sql.delete.DeleteDSLCompleter;
import org.mybatis.dynamic.sql.insert.render.InsertStatementProvider;
import org.mybatis.dynamic.sql.select.CountDSLCompleter;
import org.mybatis.dynamic.sql.select.SelectDSLCompleter;
import org.mybatis.dynamic.sql.select.render.SelectStatementProvider;
import org.mybatis.dynamic.sql.update.UpdateDSL;
import org.mybatis.dynamic.sql.update.UpdateDSLCompleter;
import org.mybatis.dynamic.sql.update.UpdateModel;
import org.mybatis.dynamic.sql.util.SqlProviderAdapter;
import org.mybatis.dynamic.sql.util.mybatis3.CommonCountMapper;
import org.mybatis.dynamic.sql.util.mybatis3.CommonDeleteMapper;
import org.mybatis.dynamic.sql.util.mybatis3.CommonSelectMapper;
import org.mybatis.dynamic.sql.util.mybatis3.CommonUpdateMapper;
import org.mybatis.dynamic.sql.util.mybatis3.MyBatis3Utils;

@Mapper
public interface DailyReportSendLogMapper extends CommonSelectMapper, CommonCountMapper, CommonDeleteMapper, CommonUpdateMapper, DynamicDataSource<DailyReportSendLogMapper> {
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: daily_report_send_log")
    BasicColumn[] selectList = BasicColumn.columnList(id, dailyReportId, contentJson, description, serviceDate, sendTime, updateBy, createTime, updateTime, errorMessage, sentSuccess, sendMethod);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: daily_report_send_log")
    @InsertProvider(type=SqlProviderAdapter.class, method="insert")
    @Options(useGeneratedKeys=true,keyProperty="row.id")
    int insert(InsertStatementProvider<DailyReportSendLog> insertStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: daily_report_send_log")
    @InsertProvider(type=SqlProviderAdapter.class, method="insertMultipleWithGeneratedKeys")
    @Options(useGeneratedKeys=true,keyProperty="records.id")
    int insertMultiple(@Param("insertStatement") String insertStatement, @Param("records") List<DailyReportSendLog> records);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: daily_report_send_log")
    @SelectProvider(type=SqlProviderAdapter.class, method="select")
    @Results(id="DailyReportSendLogResult", value = {
        @Result(column="id", property="id", jdbcType=JdbcType.BIGINT, id=true),
        @Result(column="daily_report_id", property="dailyReportId", jdbcType=JdbcType.BIGINT),
        @Result(column="content_json", property="contentJson", typeHandler=DailyReportContentHandler.class, jdbcType=JdbcType.OTHER),
        @Result(column="description", property="description", jdbcType=JdbcType.VARCHAR),
        @Result(column="service_date", property="serviceDate", jdbcType=JdbcType.DATE),
        @Result(column="send_time", property="sendTime", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="update_by", property="updateBy", jdbcType=JdbcType.BIGINT),
        @Result(column="create_time", property="createTime", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="update_time", property="updateTime", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="error_message", property="errorMessage", jdbcType=JdbcType.VARCHAR),
        @Result(column="sent_success", property="sentSuccess", jdbcType=JdbcType.BIT),
        @Result(column="send_method", property="sendMethod", typeHandler=DailyReportSendMethodHandler.class, jdbcType=JdbcType.INTEGER)
    })
    List<DailyReportSendLog> selectMany(SelectStatementProvider selectStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: daily_report_send_log")
    @SelectProvider(type=SqlProviderAdapter.class, method="select")
    @ResultMap("DailyReportSendLogResult")
    Optional<DailyReportSendLog> selectOne(SelectStatementProvider selectStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: daily_report_send_log")
    default long count(CountDSLCompleter completer) {
        return MyBatis3Utils.countFrom(this::count, dailyReportSendLog, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: daily_report_send_log")
    default int delete(DeleteDSLCompleter completer) {
        return MyBatis3Utils.deleteFrom(this::delete, dailyReportSendLog, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: daily_report_send_log")
    default int deleteByPrimaryKey(Long id_) {
        return delete(c -> 
            c.where(id, isEqualTo(id_))
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: daily_report_send_log")
    default int insertMultiple(Collection<DailyReportSendLog> records) {
        return MyBatis3Utils.insertMultipleWithGeneratedKeys(this::insertMultiple, records, dailyReportSendLog, c ->
            c.map(dailyReportId).toProperty("dailyReportId")
            .map(contentJson).toProperty("contentJson")
            .map(description).toProperty("description")
            .map(serviceDate).toProperty("serviceDate")
            .map(sendTime).toProperty("sendTime")
            .map(updateBy).toProperty("updateBy")
            .map(createTime).toProperty("createTime")
            .map(updateTime).toProperty("updateTime")
            .map(errorMessage).toProperty("errorMessage")
            .map(sentSuccess).toProperty("sentSuccess")
            .map(sendMethod).toProperty("sendMethod")
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: daily_report_send_log")
    default int insertSelective(DailyReportSendLog row) {
        return MyBatis3Utils.insert(this::insert, row, dailyReportSendLog, c ->
            c.map(dailyReportId).toPropertyWhenPresent("dailyReportId", row::getDailyReportId)
            .map(contentJson).toPropertyWhenPresent("contentJson", row::getContentJson)
            .map(description).toPropertyWhenPresent("description", row::getDescription)
            .map(serviceDate).toPropertyWhenPresent("serviceDate", row::getServiceDate)
            .map(sendTime).toPropertyWhenPresent("sendTime", row::getSendTime)
            .map(updateBy).toPropertyWhenPresent("updateBy", row::getUpdateBy)
            .map(createTime).toPropertyWhenPresent("createTime", row::getCreateTime)
            .map(updateTime).toPropertyWhenPresent("updateTime", row::getUpdateTime)
            .map(errorMessage).toPropertyWhenPresent("errorMessage", row::getErrorMessage)
            .map(sentSuccess).toPropertyWhenPresent("sentSuccess", row::getSentSuccess)
            .map(sendMethod).toPropertyWhenPresent("sendMethod", row::getSendMethod)
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: daily_report_send_log")
    default Optional<DailyReportSendLog> selectOne(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectOne(this::selectOne, selectList, dailyReportSendLog, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: daily_report_send_log")
    default List<DailyReportSendLog> select(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectList(this::selectMany, selectList, dailyReportSendLog, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: daily_report_send_log")
    default List<DailyReportSendLog> selectDistinct(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectDistinct(this::selectMany, selectList, dailyReportSendLog, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: daily_report_send_log")
    default Optional<DailyReportSendLog> selectByPrimaryKey(Long id_) {
        return selectOne(c ->
            c.where(id, isEqualTo(id_))
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: daily_report_send_log")
    default int update(UpdateDSLCompleter completer) {
        return MyBatis3Utils.update(this::update, dailyReportSendLog, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: daily_report_send_log")
    static UpdateDSL<UpdateModel> updateAllColumns(DailyReportSendLog row, UpdateDSL<UpdateModel> dsl) {
        return dsl.set(dailyReportId).equalTo(row::getDailyReportId)
                .set(contentJson).equalTo(row::getContentJson)
                .set(description).equalTo(row::getDescription)
                .set(serviceDate).equalTo(row::getServiceDate)
                .set(sendTime).equalTo(row::getSendTime)
                .set(updateBy).equalTo(row::getUpdateBy)
                .set(createTime).equalTo(row::getCreateTime)
                .set(updateTime).equalTo(row::getUpdateTime)
                .set(errorMessage).equalTo(row::getErrorMessage)
                .set(sentSuccess).equalTo(row::getSentSuccess)
                .set(sendMethod).equalTo(row::getSendMethod);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: daily_report_send_log")
    static UpdateDSL<UpdateModel> updateSelectiveColumns(DailyReportSendLog row, UpdateDSL<UpdateModel> dsl) {
        return dsl.set(dailyReportId).equalToWhenPresent(row::getDailyReportId)
                .set(contentJson).equalToWhenPresent(row::getContentJson)
                .set(description).equalToWhenPresent(row::getDescription)
                .set(serviceDate).equalToWhenPresent(row::getServiceDate)
                .set(sendTime).equalToWhenPresent(row::getSendTime)
                .set(updateBy).equalToWhenPresent(row::getUpdateBy)
                .set(createTime).equalToWhenPresent(row::getCreateTime)
                .set(updateTime).equalToWhenPresent(row::getUpdateTime)
                .set(errorMessage).equalToWhenPresent(row::getErrorMessage)
                .set(sentSuccess).equalToWhenPresent(row::getSentSuccess)
                .set(sendMethod).equalToWhenPresent(row::getSendMethod);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: daily_report_send_log")
    default int updateByPrimaryKeySelective(DailyReportSendLog row) {
        return update(c ->
            c.set(dailyReportId).equalToWhenPresent(row::getDailyReportId)
            .set(contentJson).equalToWhenPresent(row::getContentJson)
            .set(description).equalToWhenPresent(row::getDescription)
            .set(serviceDate).equalToWhenPresent(row::getServiceDate)
            .set(sendTime).equalToWhenPresent(row::getSendTime)
            .set(updateBy).equalToWhenPresent(row::getUpdateBy)
            .set(createTime).equalToWhenPresent(row::getCreateTime)
            .set(updateTime).equalToWhenPresent(row::getUpdateTime)
            .set(errorMessage).equalToWhenPresent(row::getErrorMessage)
            .set(sentSuccess).equalToWhenPresent(row::getSentSuccess)
            .set(sendMethod).equalToWhenPresent(row::getSendMethod)
            .where(id, isEqualTo(row::getId))
        );
    }
}