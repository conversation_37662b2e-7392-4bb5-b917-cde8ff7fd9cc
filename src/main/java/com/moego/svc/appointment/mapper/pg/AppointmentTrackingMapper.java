package com.moego.svc.appointment.mapper.pg;

import static com.moego.svc.appointment.mapper.pg.AppointmentTrackingDynamicSqlSupport.*;
import static org.mybatis.dynamic.sql.SqlBuilder.isEqualTo;

import com.moego.lib.common.autoconfigure.datasource.DynamicDataSource;
import com.moego.svc.appointment.domain.AppointmentTracking;
import com.moego.svc.appointment.mapper.typehandler.AddressHandler;
import jakarta.annotation.Generated;
import java.util.Collection;
import java.util.List;
import java.util.Optional;
import org.apache.ibatis.annotations.InsertProvider;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Options;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Result;
import org.apache.ibatis.annotations.ResultMap;
import org.apache.ibatis.annotations.Results;
import org.apache.ibatis.annotations.SelectProvider;
import org.apache.ibatis.type.JdbcType;
import org.mybatis.dynamic.sql.BasicColumn;
import org.mybatis.dynamic.sql.delete.DeleteDSLCompleter;
import org.mybatis.dynamic.sql.insert.render.InsertStatementProvider;
import org.mybatis.dynamic.sql.select.CountDSLCompleter;
import org.mybatis.dynamic.sql.select.SelectDSLCompleter;
import org.mybatis.dynamic.sql.select.render.SelectStatementProvider;
import org.mybatis.dynamic.sql.update.UpdateDSL;
import org.mybatis.dynamic.sql.update.UpdateDSLCompleter;
import org.mybatis.dynamic.sql.update.UpdateModel;
import org.mybatis.dynamic.sql.util.SqlProviderAdapter;
import org.mybatis.dynamic.sql.util.mybatis3.CommonCountMapper;
import org.mybatis.dynamic.sql.util.mybatis3.CommonDeleteMapper;
import org.mybatis.dynamic.sql.util.mybatis3.CommonSelectMapper;
import org.mybatis.dynamic.sql.util.mybatis3.CommonUpdateMapper;
import org.mybatis.dynamic.sql.util.mybatis3.MyBatis3Utils;

@Mapper
public interface AppointmentTrackingMapper extends CommonSelectMapper, CommonCountMapper, CommonDeleteMapper, CommonUpdateMapper, DynamicDataSource<AppointmentTrackingMapper> {
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: appointment_tracking")
    BasicColumn[] selectList = BasicColumn.columnList(id, companyId, appointmentId, staffLocationStatus, locationSharingStaffId, locationSharingDeviceId, lastInTransitAt, estimatedTravelSeconds, lastEstimateAt, travelDistance, delayedStatus, estimatedTravelSecondsFromLastInTransit, fromLastInTransitLastEstimateAt, lastInTransitAppointmentId, travelDistanceFromLastInTransit, delayedStatusFromLastInTransit, customerAddress, staffAddress, createdAt, updatedAt);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: appointment_tracking")
    @InsertProvider(type=SqlProviderAdapter.class, method="insert")
    @Options(useGeneratedKeys=true,keyProperty="row.id")
    int insert(InsertStatementProvider<AppointmentTracking> insertStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: appointment_tracking")
    @InsertProvider(type=SqlProviderAdapter.class, method="insertMultipleWithGeneratedKeys")
    @Options(useGeneratedKeys=true,keyProperty="records.id")
    int insertMultiple(@Param("insertStatement") String insertStatement, @Param("records") List<AppointmentTracking> records);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: appointment_tracking")
    @SelectProvider(type=SqlProviderAdapter.class, method="select")
    @Results(id="AppointmentTrackingResult", value = {
        @Result(column="id", property="id", jdbcType=JdbcType.BIGINT, id=true),
        @Result(column="company_id", property="companyId", jdbcType=JdbcType.BIGINT),
        @Result(column="appointment_id", property="appointmentId", jdbcType=JdbcType.BIGINT),
        @Result(column="staff_location_status", property="staffLocationStatus", jdbcType=JdbcType.INTEGER),
        @Result(column="location_sharing_staff_id", property="locationSharingStaffId", jdbcType=JdbcType.BIGINT),
        @Result(column="location_sharing_device_id", property="locationSharingDeviceId", jdbcType=JdbcType.VARCHAR),
        @Result(column="last_in_transit_at", property="lastInTransitAt", jdbcType=JdbcType.BIGINT),
        @Result(column="estimated_travel_seconds", property="estimatedTravelSeconds", jdbcType=JdbcType.BIGINT),
        @Result(column="last_estimate_at", property="lastEstimateAt", jdbcType=JdbcType.BIGINT),
        @Result(column="travel_distance", property="travelDistance", jdbcType=JdbcType.BIGINT),
        @Result(column="delayed_status", property="delayedStatus", jdbcType=JdbcType.INTEGER),
        @Result(column="estimated_travel_seconds_from_last_in_transit", property="estimatedTravelSecondsFromLastInTransit", jdbcType=JdbcType.BIGINT),
        @Result(column="from_last_in_transit_last_estimate_at", property="fromLastInTransitLastEstimateAt", jdbcType=JdbcType.BIGINT),
        @Result(column="last_in_transit_appointment_id", property="lastInTransitAppointmentId", jdbcType=JdbcType.BIGINT),
        @Result(column="travel_distance_from_last_in_transit", property="travelDistanceFromLastInTransit", jdbcType=JdbcType.BIGINT),
        @Result(column="delayed_status_from_last_in_transit", property="delayedStatusFromLastInTransit", jdbcType=JdbcType.INTEGER),
        @Result(column="customer_address", property="customerAddress", typeHandler=AddressHandler.class, jdbcType=JdbcType.OTHER),
        @Result(column="staff_address", property="staffAddress", typeHandler=AddressHandler.class, jdbcType=JdbcType.OTHER),
        @Result(column="created_at", property="createdAt", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="updated_at", property="updatedAt", jdbcType=JdbcType.TIMESTAMP)
    })
    List<AppointmentTracking> selectMany(SelectStatementProvider selectStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: appointment_tracking")
    @SelectProvider(type=SqlProviderAdapter.class, method="select")
    @ResultMap("AppointmentTrackingResult")
    Optional<AppointmentTracking> selectOne(SelectStatementProvider selectStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: appointment_tracking")
    default long count(CountDSLCompleter completer) {
        return MyBatis3Utils.countFrom(this::count, appointmentTracking, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: appointment_tracking")
    default int delete(DeleteDSLCompleter completer) {
        return MyBatis3Utils.deleteFrom(this::delete, appointmentTracking, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: appointment_tracking")
    default int deleteByPrimaryKey(Long id_) {
        return delete(c -> 
            c.where(id, isEqualTo(id_))
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: appointment_tracking")
    default int insertMultiple(Collection<AppointmentTracking> records) {
        return MyBatis3Utils.insertMultipleWithGeneratedKeys(this::insertMultiple, records, appointmentTracking, c ->
            c.map(companyId).toProperty("companyId")
            .map(appointmentId).toProperty("appointmentId")
            .map(staffLocationStatus).toProperty("staffLocationStatus")
            .map(locationSharingStaffId).toProperty("locationSharingStaffId")
            .map(locationSharingDeviceId).toProperty("locationSharingDeviceId")
            .map(lastInTransitAt).toProperty("lastInTransitAt")
            .map(estimatedTravelSeconds).toProperty("estimatedTravelSeconds")
            .map(lastEstimateAt).toProperty("lastEstimateAt")
            .map(travelDistance).toProperty("travelDistance")
            .map(delayedStatus).toProperty("delayedStatus")
            .map(estimatedTravelSecondsFromLastInTransit).toProperty("estimatedTravelSecondsFromLastInTransit")
            .map(fromLastInTransitLastEstimateAt).toProperty("fromLastInTransitLastEstimateAt")
            .map(lastInTransitAppointmentId).toProperty("lastInTransitAppointmentId")
            .map(travelDistanceFromLastInTransit).toProperty("travelDistanceFromLastInTransit")
            .map(delayedStatusFromLastInTransit).toProperty("delayedStatusFromLastInTransit")
            .map(customerAddress).toProperty("customerAddress")
            .map(staffAddress).toProperty("staffAddress")
            .map(createdAt).toProperty("createdAt")
            .map(updatedAt).toProperty("updatedAt")
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: appointment_tracking")
    default int insertSelective(AppointmentTracking row) {
        return MyBatis3Utils.insert(this::insert, row, appointmentTracking, c ->
            c.map(companyId).toPropertyWhenPresent("companyId", row::getCompanyId)
            .map(appointmentId).toPropertyWhenPresent("appointmentId", row::getAppointmentId)
            .map(staffLocationStatus).toPropertyWhenPresent("staffLocationStatus", row::getStaffLocationStatus)
            .map(locationSharingStaffId).toPropertyWhenPresent("locationSharingStaffId", row::getLocationSharingStaffId)
            .map(locationSharingDeviceId).toPropertyWhenPresent("locationSharingDeviceId", row::getLocationSharingDeviceId)
            .map(lastInTransitAt).toPropertyWhenPresent("lastInTransitAt", row::getLastInTransitAt)
            .map(estimatedTravelSeconds).toPropertyWhenPresent("estimatedTravelSeconds", row::getEstimatedTravelSeconds)
            .map(lastEstimateAt).toPropertyWhenPresent("lastEstimateAt", row::getLastEstimateAt)
            .map(travelDistance).toPropertyWhenPresent("travelDistance", row::getTravelDistance)
            .map(delayedStatus).toPropertyWhenPresent("delayedStatus", row::getDelayedStatus)
            .map(estimatedTravelSecondsFromLastInTransit).toPropertyWhenPresent("estimatedTravelSecondsFromLastInTransit", row::getEstimatedTravelSecondsFromLastInTransit)
            .map(fromLastInTransitLastEstimateAt).toPropertyWhenPresent("fromLastInTransitLastEstimateAt", row::getFromLastInTransitLastEstimateAt)
            .map(lastInTransitAppointmentId).toPropertyWhenPresent("lastInTransitAppointmentId", row::getLastInTransitAppointmentId)
            .map(travelDistanceFromLastInTransit).toPropertyWhenPresent("travelDistanceFromLastInTransit", row::getTravelDistanceFromLastInTransit)
            .map(delayedStatusFromLastInTransit).toPropertyWhenPresent("delayedStatusFromLastInTransit", row::getDelayedStatusFromLastInTransit)
            .map(customerAddress).toPropertyWhenPresent("customerAddress", row::getCustomerAddress)
            .map(staffAddress).toPropertyWhenPresent("staffAddress", row::getStaffAddress)
            .map(createdAt).toPropertyWhenPresent("createdAt", row::getCreatedAt)
            .map(updatedAt).toPropertyWhenPresent("updatedAt", row::getUpdatedAt)
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: appointment_tracking")
    default Optional<AppointmentTracking> selectOne(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectOne(this::selectOne, selectList, appointmentTracking, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: appointment_tracking")
    default List<AppointmentTracking> select(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectList(this::selectMany, selectList, appointmentTracking, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: appointment_tracking")
    default List<AppointmentTracking> selectDistinct(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectDistinct(this::selectMany, selectList, appointmentTracking, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: appointment_tracking")
    default Optional<AppointmentTracking> selectByPrimaryKey(Long id_) {
        return selectOne(c ->
            c.where(id, isEqualTo(id_))
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: appointment_tracking")
    default int update(UpdateDSLCompleter completer) {
        return MyBatis3Utils.update(this::update, appointmentTracking, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: appointment_tracking")
    static UpdateDSL<UpdateModel> updateAllColumns(AppointmentTracking row, UpdateDSL<UpdateModel> dsl) {
        return dsl.set(companyId).equalTo(row::getCompanyId)
                .set(appointmentId).equalTo(row::getAppointmentId)
                .set(staffLocationStatus).equalTo(row::getStaffLocationStatus)
                .set(locationSharingStaffId).equalTo(row::getLocationSharingStaffId)
                .set(locationSharingDeviceId).equalTo(row::getLocationSharingDeviceId)
                .set(lastInTransitAt).equalTo(row::getLastInTransitAt)
                .set(estimatedTravelSeconds).equalTo(row::getEstimatedTravelSeconds)
                .set(lastEstimateAt).equalTo(row::getLastEstimateAt)
                .set(travelDistance).equalTo(row::getTravelDistance)
                .set(delayedStatus).equalTo(row::getDelayedStatus)
                .set(estimatedTravelSecondsFromLastInTransit).equalTo(row::getEstimatedTravelSecondsFromLastInTransit)
                .set(fromLastInTransitLastEstimateAt).equalTo(row::getFromLastInTransitLastEstimateAt)
                .set(lastInTransitAppointmentId).equalTo(row::getLastInTransitAppointmentId)
                .set(travelDistanceFromLastInTransit).equalTo(row::getTravelDistanceFromLastInTransit)
                .set(delayedStatusFromLastInTransit).equalTo(row::getDelayedStatusFromLastInTransit)
                .set(customerAddress).equalTo(row::getCustomerAddress)
                .set(staffAddress).equalTo(row::getStaffAddress)
                .set(createdAt).equalTo(row::getCreatedAt)
                .set(updatedAt).equalTo(row::getUpdatedAt);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: appointment_tracking")
    static UpdateDSL<UpdateModel> updateSelectiveColumns(AppointmentTracking row, UpdateDSL<UpdateModel> dsl) {
        return dsl.set(companyId).equalToWhenPresent(row::getCompanyId)
                .set(appointmentId).equalToWhenPresent(row::getAppointmentId)
                .set(staffLocationStatus).equalToWhenPresent(row::getStaffLocationStatus)
                .set(locationSharingStaffId).equalToWhenPresent(row::getLocationSharingStaffId)
                .set(locationSharingDeviceId).equalToWhenPresent(row::getLocationSharingDeviceId)
                .set(lastInTransitAt).equalToWhenPresent(row::getLastInTransitAt)
                .set(estimatedTravelSeconds).equalToWhenPresent(row::getEstimatedTravelSeconds)
                .set(lastEstimateAt).equalToWhenPresent(row::getLastEstimateAt)
                .set(travelDistance).equalToWhenPresent(row::getTravelDistance)
                .set(delayedStatus).equalToWhenPresent(row::getDelayedStatus)
                .set(estimatedTravelSecondsFromLastInTransit).equalToWhenPresent(row::getEstimatedTravelSecondsFromLastInTransit)
                .set(fromLastInTransitLastEstimateAt).equalToWhenPresent(row::getFromLastInTransitLastEstimateAt)
                .set(lastInTransitAppointmentId).equalToWhenPresent(row::getLastInTransitAppointmentId)
                .set(travelDistanceFromLastInTransit).equalToWhenPresent(row::getTravelDistanceFromLastInTransit)
                .set(delayedStatusFromLastInTransit).equalToWhenPresent(row::getDelayedStatusFromLastInTransit)
                .set(customerAddress).equalToWhenPresent(row::getCustomerAddress)
                .set(staffAddress).equalToWhenPresent(row::getStaffAddress)
                .set(createdAt).equalToWhenPresent(row::getCreatedAt)
                .set(updatedAt).equalToWhenPresent(row::getUpdatedAt);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: appointment_tracking")
    default int updateByPrimaryKeySelective(AppointmentTracking row) {
        return update(c ->
            c.set(companyId).equalToWhenPresent(row::getCompanyId)
            .set(appointmentId).equalToWhenPresent(row::getAppointmentId)
            .set(staffLocationStatus).equalToWhenPresent(row::getStaffLocationStatus)
            .set(locationSharingStaffId).equalToWhenPresent(row::getLocationSharingStaffId)
            .set(locationSharingDeviceId).equalToWhenPresent(row::getLocationSharingDeviceId)
            .set(lastInTransitAt).equalToWhenPresent(row::getLastInTransitAt)
            .set(estimatedTravelSeconds).equalToWhenPresent(row::getEstimatedTravelSeconds)
            .set(lastEstimateAt).equalToWhenPresent(row::getLastEstimateAt)
            .set(travelDistance).equalToWhenPresent(row::getTravelDistance)
            .set(delayedStatus).equalToWhenPresent(row::getDelayedStatus)
            .set(estimatedTravelSecondsFromLastInTransit).equalToWhenPresent(row::getEstimatedTravelSecondsFromLastInTransit)
            .set(fromLastInTransitLastEstimateAt).equalToWhenPresent(row::getFromLastInTransitLastEstimateAt)
            .set(lastInTransitAppointmentId).equalToWhenPresent(row::getLastInTransitAppointmentId)
            .set(travelDistanceFromLastInTransit).equalToWhenPresent(row::getTravelDistanceFromLastInTransit)
            .set(delayedStatusFromLastInTransit).equalToWhenPresent(row::getDelayedStatusFromLastInTransit)
            .set(customerAddress).equalToWhenPresent(row::getCustomerAddress)
            .set(staffAddress).equalToWhenPresent(row::getStaffAddress)
            .set(createdAt).equalToWhenPresent(row::getCreatedAt)
            .set(updatedAt).equalToWhenPresent(row::getUpdatedAt)
            .where(id, isEqualTo(row::getId))
        );
    }
}