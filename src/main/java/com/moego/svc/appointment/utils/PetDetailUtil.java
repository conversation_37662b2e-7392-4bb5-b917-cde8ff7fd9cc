package com.moego.svc.appointment.utils;

import static com.moego.idl.models.appointment.v1.PetDetailDateType.PET_DETAIL_DATE_EVERYDAY;
import static com.moego.idl.models.appointment.v1.PetDetailDateType.PET_DETAIL_DATE_EVERYDAY_EXCEPT_CHECKIN_DAY;
import static com.moego.idl.models.appointment.v1.PetDetailDateType.PET_DETAIL_DATE_EVERYDAY_INCLUDE_CHECKOUT_DAY;
import static com.moego.idl.models.appointment.v1.PetDetailDateType.PET_DETAIL_DATE_TYPE_UNSPECIFIED;
import static com.moego.lib.common.exception.ExceptionUtil.bizException;
import static java.util.Comparator.comparingLong;

import com.moego.common.enums.ServiceItemEnum;
import com.moego.common.utils.DateUtil;
import com.moego.idl.models.appointment.v1.GroomingServiceCalendarDef;
import com.moego.idl.models.appointment.v1.PetDetailDateType;
import com.moego.idl.models.appointment.v1.PetServiceCalendarDef;
import com.moego.idl.models.appointment.v1.PetServiceScheduleDef;
import com.moego.idl.models.appointment.v1.SelectedAddOnDef;
import com.moego.idl.models.appointment.v1.SelectedServiceDef;
import com.moego.idl.models.appointment.v1.WorkMode;
import com.moego.idl.models.business_customer.v1.BusinessPetSizeModel;
import com.moego.idl.models.errors.v1.Code;
import com.moego.idl.models.offering.v1.CustomizedServiceView;
import com.moego.idl.models.offering.v1.ServiceItemType;
import com.moego.idl.models.offering.v1.ServiceOverrideType;
import com.moego.idl.models.offering.v1.ServicePriceUnit;
import com.moego.idl.models.offering.v1.ServiceScopeType;
import com.moego.idl.models.offering.v1.ServiceType;
import com.moego.lib.common.exception.ExceptionUtil;
import com.moego.lib.common.util.JsonUtil;
import com.moego.svc.appointment.converter.GroomingServiceOperationConverter;
import com.moego.svc.appointment.converter.PetDetailConverter;
import com.moego.svc.appointment.domain.EvaluationServiceDetail;
import com.moego.svc.appointment.domain.MoeGroomingAppointment;
import com.moego.svc.appointment.domain.MoeGroomingPetDetail;
import com.moego.svc.appointment.domain.MoeGroomingServiceOperation;
import com.moego.svc.appointment.dto.GroomingOnlyDTO;
import com.moego.svc.appointment.dto.PetDetailDTO;
import jakarta.annotation.Nullable;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.Duration;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.function.BiFunction;
import java.util.function.BinaryOperator;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

/**
 * <AUTHOR>
 * @since 2024/2/6
 */
@Slf4j
public class PetDetailUtil {

    /**
     * Calculates the quantity of add-ons for a given pet detail.
     *
     * @param petDetail     The pet detail for which the add-on quantity is calculated.
     * @param petServiceMap A map where the key is the petId and the value is another map of serviceId to petDetail.
     * @return The quantity of add-ons.
     */
    public static int getAddOnQuantity(
            MoeGroomingPetDetail petDetail, Map<Integer, Map<Integer, MoeGroomingPetDetail>> petServiceMap) {
        int quantityPerDay = Optional.ofNullable(petDetail.getQuantityPerDay()).orElse(1);
        if (datePoint(petDetail)) {
            return quantityPerDay;
        }
        if (specificDates(petDetail)) {
            return getSpecificDates(petDetail).size() * quantityPerDay;
        }

        MoeGroomingPetDetail associatedService =
                getAssociatedService(petDetail, petServiceMap.getOrDefault(petDetail.getPetId(), Map.of()));
        if (associatedService == null) {
            return quantityPerDay;
        }
        if (Objects.equals(associatedService.getServiceItemType().intValue(), ServiceItemType.GROOMING_VALUE)) {
            return quantityPerDay;
        }

        if (Objects.equals(associatedService.getServiceItemType().intValue(), ServiceItemType.BOARDING_VALUE)) {
            int days =
                    switch (getDateType(petDetail)) {
                        case PET_DETAIL_DATE_EVERYDAY_EXCEPT_CHECKIN_DAY, PET_DETAIL_DATE_EVERYDAY -> calculateDays(
                                        associatedService)
                                - 1;
                        case PET_DETAIL_DATE_EVERYDAY_INCLUDE_CHECKOUT_DAY -> calculateDays(associatedService);
                        case PET_DETAIL_DATE_FIRST_DAY, PET_DETAIL_DATE_LAST_DAY -> quantityPerDay;
                        default -> throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR, "date type not found");
                    };
            return days * quantityPerDay;
        }

        return getServiceQuantity(associatedService, petServiceMap) * quantityPerDay;
    }

    public static boolean isServiceTypeNormal(Integer serviceType) {
        if (serviceType == null) {
            return false;
        }
        ServiceType type = ServiceType.forNumber(serviceType);
        return type != null && !ServiceType.SERVICE_TYPE_UNSPECIFIED.equals(type);
    }

    // 计算 service 的 date type
    private static PetDetailDateType getServiceDateType(MoeGroomingPetDetail petDetail) {
        if (petDetail.getServiceItemType() != ServiceItemType.DAYCARE_VALUE) {
            return PetDetailDateType.PET_DETAIL_DATE_DATE_POINT;
        }
        return getAddOnDateType(petDetail);
    }

    // 计算 addOn 的 date type
    private static PetDetailDateType getAddOnDateType(MoeGroomingPetDetail petDetail) {
        if (datePoint(petDetail)) {
            return PetDetailDateType.PET_DETAIL_DATE_DATE_POINT;
        }
        if (specificDates(petDetail)) {
            return PetDetailDateType.PET_DETAIL_DATE_SPECIFIC_DATE;
        }
        return PetDetailDateType.PET_DETAIL_DATE_EVERYDAY;
    }

    // 计算 service 或 addOn 的 date type
    // 需要根据 service type 区分 service/addOn
    public static PetDetailDateType getDateType(MoeGroomingPetDetail petDetail) {
        if (!isServiceTypeNormal(petDetail.getServiceType())) {
            return null;
        }
        if (Objects.nonNull(petDetail.getDateType())
                && !Objects.equals(petDetail.getDateType(), PetDetailDateType.PET_DETAIL_DATE_DATE_POINT_VALUE)) {
            return PetDetailDateType.forNumber(petDetail.getDateType());
        }
        if (petDetail.getServiceType().equals(ServiceType.SERVICE_VALUE)) {
            return getServiceDateType(petDetail);
        } else {
            return getAddOnDateType(petDetail);
        }
    }

    public static Map<Integer, PetDetailDateType> getDateTypeMap(Collection<MoeGroomingPetDetail> petDetails) {
        Map<Integer, PetDetailDateType> result = new HashMap<>();
        if (CollectionUtils.isEmpty(petDetails)) {
            return result;
        }
        petDetails.forEach(petDetail -> {
            PetDetailDateType dateType = getDateType(petDetail);
            if (dateType != null) {
                result.put(petDetail.getId(), dateType);
            }
        });

        return result;
    }

    public static boolean datePoint(MoeGroomingPetDetail petDetail) {
        return StringUtils.hasText(petDetail.getStartDate()) && Objects.nonNull(petDetail.getStartTime());
    }

    public static boolean specificDates(MoeGroomingPetDetail petDetail) {
        return !getSpecificDates(petDetail).isEmpty();
    }

    public static List<String> getSpecificDates(MoeGroomingPetDetail petDetail) {
        List<String> result = new ArrayList<>();
        if (StringUtils.hasText(petDetail.getSpecificDates())) {
            result = JsonUtil.toList(petDetail.getSpecificDates(), String.class);
        }
        return result;
    }

    private static int calculateNights(MoeGroomingPetDetail petDetail) {
        if (!StringUtils.hasText(petDetail.getStartDate()) || !StringUtils.hasText(petDetail.getEndDate())) {
            throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR, "start date and end date must be specified");
        }
        int between = (int) ChronoUnit.DAYS.between(
                LocalDate.parse(petDetail.getStartDate()), LocalDate.parse(petDetail.getEndDate()));
        return between <= 0 ? 1 : between;
    }

    private static int calculateDays(MoeGroomingPetDetail petDetail) {
        if (!StringUtils.hasText(petDetail.getStartDate()) || !StringUtils.hasText(petDetail.getEndDate())) {
            throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR, "start date and end date must be specified");
        }
        int between = (int) ChronoUnit.DAYS.between(
                LocalDate.parse(petDetail.getStartDate()), LocalDate.parse(petDetail.getEndDate()));
        return Math.max(between + 1, 1);
    }

    private static int calculateHours(MoeGroomingPetDetail petDetail) {
        if (petDetail.getStartTime() == null || petDetail.getEndTime() == null) {
            throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR, "start time and end time must be specified");
        }
        LocalDateTime start =
                buildDateTime(petDetail.getStartDate(), petDetail.getStartTime().intValue());
        LocalDateTime end =
                buildDateTime(petDetail.getEndDate(), petDetail.getEndTime().intValue());
        long minutesBetween = Duration.between(start, end).toMinutes();
        return BigDecimal.valueOf(Math.ceil((double) minutesBetween / 60)).intValue();
    }

    // service 之间没有层级关系，逻辑上是多天 appointment 每天的服务类型
    private static int getServiceQuantity(
            MoeGroomingPetDetail petDetail, Map<Integer, Map<Integer, MoeGroomingPetDetail>> petServiceMap) {
        // boarding 下支持挂载有多天的 daycare 服务
        if (Objects.equals(petDetail.getServiceItemType().intValue(), ServiceItemType.DAYCARE_VALUE)) {
            return calculateDaycareServiceQuantity(petDetail, petServiceMap);
        }
        return switch (Objects.requireNonNull(ServicePriceUnit.forNumber(petDetail.getPriceUnit()))) {
            case PER_SESSION -> 1;
            case PER_NIGHT -> calculateNights(petDetail);
            case PER_HOUR -> calculateHours(petDetail);
            case PER_DAY -> calculateDays(petDetail);
            default -> throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR, "price unit not found");
        };
    }

    private static int calculateDaycareServiceQuantity(
            MoeGroomingPetDetail petDetail, Map<Integer, Map<Integer, MoeGroomingPetDetail>> petServiceMap) {
        //
        if (datePoint(petDetail)) {
            return switch (Objects.requireNonNull(ServicePriceUnit.forNumber(petDetail.getPriceUnit()))) {
                case PER_SESSION, PER_NIGHT, PER_DAY -> 1;
                case PER_HOUR -> calculateHours(petDetail);
                default -> throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR, "price unit not found");
            };
        }

        // 下面的情况为 daycare 被挂载在 boarding 下，根据 main service 或 specificDates 计算 quantity
        var dayCount = getDayCount(petDetail, petServiceMap.get(petDetail.getPetId()));

        return switch (Objects.requireNonNull(ServicePriceUnit.forNumber(petDetail.getPriceUnit()))) {
            case PER_SESSION, PER_NIGHT, PER_DAY -> dayCount;
            case PER_HOUR -> BigDecimal.valueOf(
                            Math.ceil((double) (petDetail.getEndTime() - petDetail.getStartTime()) * dayCount / 60))
                    .intValue();
            default -> throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR, "price unit not found");
        };
    }

    private static int getDayCount(
            MoeGroomingPetDetail petDetail, Map<Integer /* service id */, MoeGroomingPetDetail> petServiceMap) {
        if (Objects.isNull(petDetail.getDateType())) {
            return 1;
        }
        MoeGroomingPetDetail associatedService = getAssociatedService(petDetail, petServiceMap);
        if (associatedService == null) {
            log.error("Associated service not found, appointment id: [{}]", petDetail.getGroomingId());
            throw ExceptionUtil.bizException(Code.CODE_SERVER_ERROR, "Associated service not found");
        }
        return switch (PetDetailDateType.forNumber(petDetail.getDateType())) {
            case PET_DETAIL_DATE_DATE_POINT, PET_DETAIL_DATE_FIRST_DAY, PET_DETAIL_DATE_LAST_DAY -> 1;
            case PET_DETAIL_DATE_SPECIFIC_DATE -> getSpecificDates(petDetail).size();
            case PET_DETAIL_DATE_EVERYDAY_INCLUDE_CHECKOUT_DAY -> calculateDays(associatedService);
            case PET_DETAIL_DATE_EVERYDAY, PET_DETAIL_DATE_EVERYDAY_EXCEPT_CHECKIN_DAY -> calculateDays(
                            associatedService)
                    - 1;
            default -> throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR, "date type not found");
        };
    }

    /**
     * Calculate the quantity of a pet detail
     * @param petDetail pet detail
     * @param petServiceMap petId -> serviceId -> serviceDetail, from com.moego.svc.appointment.utils.PetDetailUtil#getPetServiceMap
     * @return quantity
     */
    public static int getQuantity(
            MoeGroomingPetDetail petDetail, Map<Integer, Map<Integer, MoeGroomingPetDetail>> petServiceMap) {
        return switch (Objects.requireNonNull(ServiceType.forNumber(petDetail.getServiceType()))) {
            case SERVICE -> getServiceQuantity(petDetail, petServiceMap);
            case ADDON -> getAddOnQuantity(petDetail, petServiceMap);
            default -> throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR, "service type not found");
        };
    }

    public static BigDecimal calculateAmount(List<MoeGroomingPetDetail> petDetails) {
        Map<Integer, Map<Integer, MoeGroomingPetDetail>> petServiceMap = getPetServiceMap(petDetails);
        return petDetails.stream()
                .map(petDetail ->
                        petDetail.getServicePrice().multiply(BigDecimal.valueOf(getQuantity(petDetail, petServiceMap))))
                .reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    public static BigDecimal calculateAmount(
            List<MoeGroomingPetDetail> petDetails, List<EvaluationServiceDetail> evaluations) {
        var petDetailsAmount = calculateAmount(petDetails);
        var evaluationAmount = evaluations.stream()
                .map(EvaluationServiceDetail::getServicePrice)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        return petDetailsAmount.add(evaluationAmount);
    }

    // 过滤出 service 类型的 petDetail，并按 petId -> serviceId -> serviceDetail 的形式返回
    public static Map<Integer, Map<Integer, MoeGroomingPetDetail>> getPetServiceMap(
            List<MoeGroomingPetDetail> appointmentPetDetails) {
        return appointmentPetDetails.stream()
                .filter(petDetail -> Objects.equals(petDetail.getServiceType(), ServiceType.SERVICE_VALUE))
                .collect(Collectors.groupingBy(
                        MoeGroomingPetDetail::getPetId,
                        Collectors.toMap(MoeGroomingPetDetail::getServiceId, Function.identity(), (p1, p2) -> p1)));
    }

    /**
     * Retrieves the associated service for petDetail with specific dates or everyday.
     * If an associated service ID is recorded, it returns the corresponding service.
     * Otherwise, it infers the associated service based on the following logic:
     * - For service:
     * - Only supports multiple daycare services under a main boarding service appointment.
     * - For addOn:
     * - Supports multiple addOns under various main service appointments.
     *
     * @param petServiceMap A map of serviceId (service type only) to serviceDetail for a certain pet.
     * @return The associated service detail, or null if not found.
     */
    @Nullable
    public static MoeGroomingPetDetail getAssociatedService(
            MoeGroomingPetDetail petDetail, Map<Integer, MoeGroomingPetDetail> petServiceMap) {
        if (petDetail.getAssociatedServiceId() != null && petDetail.getAssociatedServiceId() > 0) {
            return petServiceMap.get(petDetail.getAssociatedServiceId().intValue());
        }
        var mainPetDetail = getMainService(petServiceMap.values());
        if (mainPetDetail == null) {
            return null;
        }

        // for addOn
        if (petDetail.getServiceType() == ServiceType.ADDON_VALUE) {
            return mainPetDetail;
        }

        // for service
        if (petDetail.getServiceType() == ServiceType.SERVICE_VALUE
                && petDetail.getServiceItemType() == ServiceItemType.DAYCARE_VALUE
                && mainPetDetail.getServiceItemType() == ServiceItemType.BOARDING_VALUE) {
            return mainPetDetail;
        }
        return null;
    }

    public static Integer calculateServiceTypeInclude(
            List<MoeGroomingPetDetail> petDetails, List<EvaluationServiceDetail> evaluationList) {
        ArrayList<Integer> serviceItemList = new ArrayList<>(petDetails.stream()
                .map(MoeGroomingPetDetail::getServiceItemType)
                .map(Integer::valueOf)
                .toList());
        if (!CollectionUtils.isEmpty(evaluationList)) {
            serviceItemList.add(ServiceItemEnum.EVALUATION.getServiceItem());
        }
        return ServiceItemEnum.convertBitValueList(serviceItemList);
    }

    /**
     * Build LocalDateTime based on date and minute of the day
     * If the minute of day exceeds 1440, it is added to the date
     *
     * @param date        yyyy-MM-dd
     * @param minuteOfDay minutes of the day
     * @return LocalDateTime
     */
    public static LocalDateTime buildDateTime(String date, Integer minuteOfDay) {
        LocalDate localDate = LocalDate.parse(date);
        if (minuteOfDay < 0) {
            int daysToSubtract = Math.abs(minuteOfDay) / 1440 + 1;
            localDate = localDate.minusDays(daysToSubtract);
            minuteOfDay = 1440 - Math.abs(minuteOfDay) % 1440;
        } else if (minuteOfDay >= 1440) {
            int daysToAdd = minuteOfDay / 1440;
            localDate = localDate.plusDays(daysToAdd);
            minuteOfDay = minuteOfDay % 1440;
        }
        return LocalDateTime.of(localDate, LocalTime.ofSecondOfDay(minuteOfDay * 60));
    }

    /**
     * Calculate the end date and end time based on the start date, start time and duration
     *
     * @param startDate yyyy-MM-dd
     * @param startTime minutes of the day
     * @param duration  service duration, minutes
     * @return end date and end time
     */
    public static Pair<String, Integer> calculateEndDateAndEndTime(
            String startDate, Integer startTime, Integer duration) {
        LocalDateTime startDateTime = buildDateTime(startDate, startTime);
        LocalDateTime endDateTime = startDateTime.plusMinutes(duration);
        return Pair.of(
                endDateTime.toLocalDate().toString(), endDateTime.toLocalTime().toSecondOfDay() / 60);
    }

    /**
     * Calculate the service time based on the start date, start time, end date and end time
     *
     * @param petDetail selected pet and service detail
     * @return end date and end time
     */
    public static Integer calculateServiceTime(MoeGroomingPetDetail petDetail) {
        if (!StringUtils.hasText(petDetail.getStartDate())
                || !StringUtils.hasText(petDetail.getEndDate())
                || petDetail.getStartTime() == null
                || petDetail.getEndTime() == null) {
            throw ExceptionUtil.bizException(
                    Code.CODE_PARAMS_ERROR, "start date, start time, end date and end time must be specified");
        }
        LocalDateTime startDateTime =
                buildDateTime(petDetail.getStartDate(), petDetail.getStartTime().intValue());
        LocalDateTime endDateTime =
                buildDateTime(petDetail.getEndDate(), petDetail.getEndTime().intValue());
        return Math.toIntExact(ChronoUnit.MINUTES.between(startDateTime, endDateTime));
    }

    public static Number calculateDaycareServiceTime(Number startTime, Number endTime) {
        if (Objects.isNull(startTime) || Objects.isNull(endTime)) {
            throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR, "start time or end time must be specified");
        }
        return limitEndTime(endTime).intValue() - startTime.intValue();
    }

    public static Integer calculateAppointmentTime(MoeGroomingAppointment appointment) {
        if (!StringUtils.hasText(appointment.getAppointmentDate())
                || !StringUtils.hasText(appointment.getAppointmentEndDate())
                || appointment.getAppointmentStartTime() == null
                || appointment.getAppointmentEndTime() == null) {
            throw ExceptionUtil.bizException(
                    Code.CODE_PARAMS_ERROR, "start date, start time, end date and end time must be specified");
        }
        LocalDateTime startDateTime =
                buildDateTime(appointment.getAppointmentDate(), appointment.getAppointmentStartTime());
        LocalDateTime endDateTime =
                buildDateTime(appointment.getAppointmentEndDate(), appointment.getAppointmentEndTime());
        return Math.toIntExact(ChronoUnit.MINUTES.between(startDateTime, endDateTime));
    }

    public static int calculateOffsetMinute(String oldDate, Integer oldTime, String newDate, Integer newTime) {
        LocalDateTime oldDateTime = buildDateTime(oldDate, oldTime);
        LocalDateTime newDateTime = buildDateTime(newDate, newTime);
        return Math.toIntExact(ChronoUnit.MINUTES.between(oldDateTime, newDateTime));
    }

    public static int calculateOffsetMinute(LocalDateTime oldDateTime, LocalDateTime newDateTime) {
        return Math.toIntExact(ChronoUnit.MINUTES.between(oldDateTime, newDateTime));
    }

    /**
     * Retrieves the main service from the given petDetails.
     *
     * @param petDetails A collection of petDetails for one pet.
     * @return main service
     */
    @Nullable
    static MoeGroomingPetDetail getMainService(Collection<MoeGroomingPetDetail> petDetails) {
        var mainServiceItemType = getMainServiceItemType(petDetails);
        return petDetails.stream()
                .filter(v -> Objects.equals(v.getServiceType(), ServiceType.SERVICE_VALUE))
                .filter(v -> v.getServiceItemType().intValue() == mainServiceItemType.getNumber())
                .findFirst()
                .orElse(null);
    }

    /**
     * Retrieves the main service item type from the given petDetails.
     *
     * @param petDetails A collection of petDetails for one pet.
     * @return main service item type
     */
    static ServiceItemType getMainServiceItemType(Collection<MoeGroomingPetDetail> petDetails) {
        List<ServiceItemType> serviceItemTypes = getServiceItemTypes(petDetails);
        if (serviceItemTypes.contains(ServiceItemType.BOARDING)) {
            return ServiceItemType.BOARDING;
        }
        if (serviceItemTypes.contains(ServiceItemType.DAYCARE)) {
            return ServiceItemType.DAYCARE;
        }
        if (serviceItemTypes.contains(ServiceItemType.EVALUATION)) {
            return ServiceItemType.EVALUATION;
        }
        return ServiceItemType.GROOMING;
    }

    /**
     * Retrieves the distinct service item types from the given pet details.
     *
     * @param petDetails A collection of petDetails.
     * @return A list of distinct service item types.
     */
    static List<ServiceItemType> getServiceItemTypes(Collection<MoeGroomingPetDetail> petDetails) {
        if (CollectionUtils.isEmpty(petDetails)) {
            return List.of();
        }
        return petDetails.stream()
                .filter(v -> Objects.equals(v.getServiceType(), ServiceType.SERVICE_VALUE))
                .map(k -> PetDetailConverter.INSTANCE.mapServiceItemType(
                        k.getServiceItemType().intValue()))
                .distinct()
                .toList();
    }

    public static boolean isGroomingOnly(List<MoeGroomingPetDetail> petDetails) {
        return petDetails.stream()
                .allMatch(petDetail ->
                        Objects.equals(petDetail.getServiceItemType().intValue(), ServiceItemType.GROOMING_VALUE));
    }

    public static boolean isGroomingOnly(
            List<PetServiceScheduleDef> defs, Map<Long, Map<Long, CustomizedServiceView>> petServiceMap) {
        return defs.stream()
                .flatMap(def -> def.getServiceSchedulesList().stream().map(selectedServiceDef -> {
                    Map<Long, CustomizedServiceView> serviceMap = petServiceMap.get(def.getPetId());
                    if (serviceMap == null || !serviceMap.containsKey(selectedServiceDef.getServiceId())) {
                        throw ExceptionUtil.bizException(Code.CODE_SERVICE_NOT_FOUND);
                    }
                    CustomizedServiceView service = serviceMap.get(selectedServiceDef.getServiceId());
                    return service.getServiceItemType();
                }))
                .allMatch(itemType -> Objects.equals(itemType, ServiceItemType.GROOMING));
    }

    public static boolean hasBoarding(List<MoeGroomingPetDetail> petDetails) {
        return petDetails.stream()
                .anyMatch(petDetail ->
                        Objects.equals(petDetail.getServiceItemType().intValue(), ServiceItemType.BOARDING_VALUE));
    }

    private static LocalDateTime getFirstPetServiceStartDateTime(List<PetServiceScheduleDef> defs) {
        if (CollectionUtils.isEmpty(defs)) {
            throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR, "Grooming appointment must specify pet details");
        }
        PetServiceScheduleDef firstPetServiceScheduleDef = defs.get(0);
        List<LocalDateTime> dateTimes = new ArrayList<>();
        firstPetServiceScheduleDef.getServiceSchedulesList().forEach(serviceDef -> {
            if (serviceDef.hasOrder()) {
                dateTimes.add(buildDateTime(serviceDef.getStartDate(), serviceDef.getStartTime()));
            }
        });
        firstPetServiceScheduleDef.getAddOnSchedulesList().forEach(addOnDef -> {
            if (addOnDef.hasOrder() && addOnDef.hasStartDate() && addOnDef.hasStartTime()) {
                dateTimes.add(buildDateTime(addOnDef.getStartDate(), addOnDef.getStartTime()));
            }
        });
        return dateTimes.stream()
                .sorted()
                .findFirst()
                .orElseThrow(() -> ExceptionUtil.bizException(
                        Code.CODE_PARAMS_ERROR, "Grooming service must specify start date and start time"));
    }

    public static GroomingOnlyDTO buildGroomingOnlyDTO(
            List<PetServiceScheduleDef> defs, boolean allPetsStartAtSameTime) {
        GroomingOnlyDTO groomingOnlyDTO = new GroomingOnlyDTO();
        groomingOnlyDTO.setAllPetsStartAtSameTime(allPetsStartAtSameTime);
        groomingOnlyDTO.setServiceDateTime(getFirstPetServiceStartDateTime(defs));
        groomingOnlyDTO.setPetServiceDateTime(groomingOnlyDTO.getServiceDateTime());
        Map<Long, Map<Long, Integer>> petServiceOrderMap = new HashMap<>();
        groomingOnlyDTO.setPetServiceOrderMap(petServiceOrderMap);
        defs.forEach(def -> {
            def.getServiceSchedulesList().forEach(serviceScheduleDef -> petServiceOrderMap
                    .computeIfAbsent(def.getPetId(), k -> new HashMap<>())
                    .put(serviceScheduleDef.getServiceId(), serviceScheduleDef.getOrder()));
            def.getAddOnSchedulesList().forEach(addOnScheduleDef -> petServiceOrderMap
                    .computeIfAbsent(def.getPetId(), k -> new HashMap<>())
                    .put(addOnScheduleDef.getAddOnId(), addOnScheduleDef.getOrder()));
        });
        return groomingOnlyDTO;
    }

    public static boolean getAllPetsStartAtSameTime(List<MoeGroomingPetDetail> petDetails) {
        if (CollectionUtils.isEmpty(petDetails)) {
            return false;
        }
        List<Integer> petIds = petDetails.stream()
                .map(MoeGroomingPetDetail::getPetId)
                .distinct()
                .toList();
        if (petIds.size() == 1) {
            return false;
        }
        return petDetails.stream()
                        .collect(Collectors.toMap(
                                MoeGroomingPetDetail::getPetId, MoeGroomingPetDetail::getStartTime, Long::min))
                        .values()
                        .stream()
                        .distinct()
                        .count()
                == 1;
    }

    private static LocalDateTime getFirstPetService(List<MoeGroomingPetDetail> petDetails) {
        if (CollectionUtils.isEmpty(petDetails)) {
            throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR, "Grooming appointment must specify pet details");
        }
        List<LocalDateTime> dateTimes = new ArrayList<>();
        petDetails.forEach(petDetail -> dateTimes.add(
                buildDateTime(petDetail.getStartDate(), petDetail.getStartTime().intValue())));
        return dateTimes.stream()
                .sorted()
                .findFirst()
                .orElseThrow(() -> ExceptionUtil.bizException(
                        Code.CODE_PARAMS_ERROR, "Grooming service must specify start date and start time"));
    }

    public static GroomingOnlyDTO buildGroomingOnlyDTO(List<MoeGroomingPetDetail> petDetails) {
        GroomingOnlyDTO groomingOnlyDTO = new GroomingOnlyDTO();
        groomingOnlyDTO.setAllPetsStartAtSameTime(getAllPetsStartAtSameTime(petDetails));
        groomingOnlyDTO.setServiceDateTime(getFirstPetService(petDetails));
        groomingOnlyDTO.setPetServiceDateTime(groomingOnlyDTO.getServiceDateTime());
        return groomingOnlyDTO;
    }

    public static String buildPetDetailKey(MoeGroomingPetDetail petDetail) {
        return String.format(
                "%d-%d-%d", petDetail.getPetId(), petDetail.getServiceId(), petDetail.getAssociatedServiceId());
    }

    /**
     * Get staff id list from all pet service
     */
    public static List<Integer> getStaffIdList(List<MoeGroomingPetDetail> petDetails) {
        List<Integer> staffIds = new ArrayList<>();
        if (!CollectionUtils.isEmpty(petDetails)) {
            staffIds.addAll(petDetails.stream()
                    .map(MoeGroomingPetDetail::getStaffId)
                    .filter(Objects::nonNull)
                    .distinct()
                    .toList());
        }
        return staffIds;
    }

    /**
     * 校验 Grooming & Daycare service 时间是否在 boarding 时间范围内
     *
     * @param petDetails all pet details
     * @param boarding   boarding service to check
     * @return conflict pet details
     */
    public static List<MoeGroomingPetDetail> getBoardingConflictService(
            MoeGroomingPetDetail boarding, List<MoeGroomingPetDetail> petDetails) {
        var start = LocalDate.parse(boarding.getStartDate());
        var end = LocalDate.parse(boarding.getEndDate());

        var toChecks = petDetails.stream()
                .filter(petDetail -> Objects.equals(petDetail.getPetId(), boarding.getPetId()))
                .filter(k -> Objects.equals(k.getServiceType(), ServiceType.SERVICE_VALUE))
                // 排除 boarding service
                .filter(k -> !Objects.equals(k.getServiceItemType().intValue(), ServiceItemType.BOARDING_VALUE))
                .toList();

        return toChecks.stream()
                .filter(k -> isServiceOutOfTimeRange(start, end, k))
                .toList();
    }

    /**
     * 校验 Daycare service 时间是否在 boarding 时间范围内
     * 校验 Grooming service 时间是否在 daycare 时间范围内
     *
     * @param petDetails all pet details
     * @param daycare    daycare service to check
     * @return conflict pet details
     */
    public static List<MoeGroomingPetDetail> getDaycareConflictService(
            MoeGroomingPetDetail daycare, List<MoeGroomingPetDetail> petDetails) {
        var toChecks = petDetails.stream()
                .filter(petDetail -> Objects.equals(petDetail.getPetId(), daycare.getPetId()))
                .filter(k -> Objects.equals(k.getServiceType(), ServiceType.SERVICE_VALUE))
                // 排除 daycare service
                .filter(k -> !Objects.equals(k.getServiceItemType().intValue(), ServiceItemType.DAYCARE_VALUE))
                .toList();

        MoeGroomingPetDetail boarding = toChecks.stream()
                .filter(petDetail -> Objects.equals(petDetail.getServiceType(), ServiceType.SERVICE_VALUE))
                .filter(petDetail ->
                        Objects.equals(petDetail.getServiceItemType().intValue(), ServiceItemType.BOARDING_VALUE))
                .findFirst()
                .orElse(null);

        // boarding 为主 service
        // 检查 daycare service 是否都在 boarding service 日期范围内
        if (boarding != null) {
            if (isServiceOutOfTimeRange(
                    LocalDate.parse(boarding.getStartDate()), LocalDate.parse(boarding.getEndDate()), daycare)) {
                return List.of(boarding);
            } else {
                return new ArrayList<>();
            }
        }

        // daycare 为主 service
        var daycareStart = LocalDate.parse(daycare.getStartDate());
        var daycareEnd = LocalDate.parse(daycare.getEndDate());
        return toChecks.stream()
                .filter(k -> isServiceOutOfTimeRange(daycareStart, daycareEnd, k))
                .toList();
    }

    /**
     * 校验 pet detail 时间是否在时间范围内
     */
    public static Boolean isServiceOutOfTimeRange(
            LocalDate startDate, LocalDate endDate, MoeGroomingPetDetail petDetail) {

        PetDetailDateType dateType = getDateType(petDetail);
        if (dateType == null) {
            dateType = PET_DETAIL_DATE_TYPE_UNSPECIFIED;
        }
        switch (dateType) {
            case PET_DETAIL_DATE_EVERYDAY,
                    PET_DETAIL_DATE_EVERYDAY_INCLUDE_CHECKOUT_DAY,
                    PET_DETAIL_DATE_EVERYDAY_EXCEPT_CHECKIN_DAY,
                    PET_DETAIL_DATE_LAST_DAY,
                    PET_DETAIL_DATE_FIRST_DAY -> {
                return false;
            }
            case PET_DETAIL_DATE_SPECIFIC_DATE -> {
                List<String> specificDates = getSpecificDates(petDetail);
                String minDate = specificDates.stream().min(String::compareTo).orElse("");
                String maxDate = specificDates.stream().max(String::compareTo).orElse("");
                // 数据异常，不让通过校验
                if (!StringUtils.hasText(minDate) || !StringUtils.hasText(maxDate)) {
                    return true;
                }
                return LocalDate.parse(minDate).isBefore(startDate)
                        || LocalDate.parse(maxDate).isAfter(endDate);
            }
            default -> {
                return LocalDate.parse(petDetail.getStartDate()).isBefore(startDate)
                        || LocalDate.parse(petDetail.getEndDate()).isAfter(endDate);
            }
        }
    }

    public static Number limitEndTime(Number endTime) {
        return endTime.intValue() > 1439 ? 1439 : endTime;
    }

    /**
     * check if the pet detail need to update order only for daycare service
     *
     * @param before
     * @param after
     * @return
     */
    public static boolean needUpdateOrder(List<MoeGroomingPetDetail> before, List<MoeGroomingPetDetail> after) {
        record PetDetailKey(
                Integer petId,
                String startDate,
                String endDate,
                String specificDates,
                Integer dateType,
                Integer quantityPerDay) {}

        Function<MoeGroomingPetDetail, PetDetailKey> keyExtractor = detail -> new PetDetailKey(
                detail.getPetId(),
                detail.getStartDate(),
                detail.getEndDate(),
                detail.getSpecificDates(),
                detail.getDateType(),
                detail.getQuantityPerDay());

        var beforeSet = before.stream().map(keyExtractor).collect(Collectors.toSet());

        var afterSet = after.stream().map(keyExtractor).collect(Collectors.toSet());

        return !Objects.equals(beforeSet, afterSet);
    }

    public static int getQuantityPerDay(SelectedAddOnDef selectedAddOnDef) {
        return selectedAddOnDef.hasQuantityPerDay() && selectedAddOnDef.getQuantityPerDay() != 0
                ? selectedAddOnDef.getQuantityPerDay()
                : 1;
    }

    /**
     * Generate all dates between start date and end date
     * e.g. 2024-02-01 to 2024-02-03
     * return [2024-02-01, 2024-02-02]
     */
    public static List<String> generateAllDatesBetweenByNight(String startDate, String endDate) {
        LocalDate start = LocalDate.parse(startDate);
        LocalDate end = LocalDate.parse(endDate);
        List<String> dates = new ArrayList<>();
        for (LocalDate date = start; date.isBefore(end); date = date.plusDays(1)) {
            dates.add(date.toString());
        }
        return dates;
    }

    private static List<String> getBoardingDates(
            MoeGroomingPetDetail petDetail, Map<Integer, Map<Integer, MoeGroomingPetDetail>> petServiceMap) {
        return switch (Objects.requireNonNull(ServiceType.forNumber(petDetail.getServiceType()))) {
            case SERVICE -> getBoardingServiceDates(petDetail, petServiceMap);
            case ADDON -> getBoardingAddOnDates(petDetail, petServiceMap);
            default -> List.of();
        };
    }

    private static List<String> getBoardingServiceDates(
            MoeGroomingPetDetail petDetail, Map<Integer, Map<Integer, MoeGroomingPetDetail>> petServiceMap) {
        if (Objects.equals(ServicePriceUnit.PER_NIGHT.getNumber(), petDetail.getPriceUnit())) {
            return generateAllDatesBetweenByNight(petDetail.getStartDate(), petDetail.getEndDate());
        }
        return DateUtil.generateAllDatesBetween(petDetail.getStartDate(), petDetail.getEndDate());
    }

    private static List<String> getBoardingAddOnDates(
            MoeGroomingPetDetail petDetail, Map<Integer, Map<Integer, MoeGroomingPetDetail>> petServiceMap) {
        if (specificDates(petDetail)) {
            return JsonUtil.toList(petDetail.getSpecificDates(), String.class);
        }
        if (datePoint(petDetail)) {
            return List.of(petDetail.getStartDate());
        }

        MoeGroomingPetDetail associatedService =
                getAssociatedService(petDetail, petServiceMap.get(petDetail.getPetId()));
        if (associatedService == null) {
            log.error("Associated service not found, appointment id: [{}]", petDetail.getGroomingId());
            throw ExceptionUtil.bizException(Code.CODE_SERVER_ERROR, "Associated service not found");
        }
        // every day not include the checkout day
        if (Objects.equals(PetDetailDateType.PET_DETAIL_DATE_EVERYDAY_VALUE, petDetail.getDateType())) {
            return generateAllDatesBetweenByNight(associatedService.getStartDate(), associatedService.getEndDate());
        }
        // every day include the checkout day
        return DateUtil.generateAllDatesBetween(associatedService.getStartDate(), associatedService.getEndDate());
    }

    public static List<String> getDaycareDates(
            MoeGroomingPetDetail petDetail, Map<Integer, Map<Integer, MoeGroomingPetDetail>> petServiceMap) {
        if (specificDates(petDetail)) {
            return JsonUtil.toList(petDetail.getSpecificDates(), String.class);
        }
        if (datePoint(petDetail)) {
            return List.of(petDetail.getStartDate());
        }

        MoeGroomingPetDetail associatedService =
                getAssociatedService(petDetail, petServiceMap.get(petDetail.getPetId()));
        if (associatedService == null) {
            log.error("Associated service not found, appointment id: [{}]", petDetail.getGroomingId());
            throw ExceptionUtil.bizException(Code.CODE_SERVER_ERROR, "Associated service not found");
        }
        if (Objects.equals(
                ServiceItemType.BOARDING_VALUE,
                associatedService.getServiceItemType().intValue())) {
            // every day not include the checkout day
            if (Objects.equals(PetDetailDateType.PET_DETAIL_DATE_EVERYDAY_VALUE, petDetail.getDateType())) {
                return generateAllDatesBetweenByNight(associatedService.getStartDate(), associatedService.getEndDate());
            }
            // every day include the checkout day
            return DateUtil.generateAllDatesBetween(associatedService.getStartDate(), associatedService.getEndDate());
        }
        return getDaycareDates(associatedService, petServiceMap);
    }

    public static List<String> getGroomingDates(
            MoeGroomingPetDetail petDetail, Map<Integer, Map<Integer, MoeGroomingPetDetail>> petServiceMap) {
        if (datePoint(petDetail)) {
            return List.of(petDetail.getStartDate());
        }
        if (specificDates(petDetail)) {
            return JsonUtil.toList(petDetail.getSpecificDates(), String.class);
        }

        MoeGroomingPetDetail associatedService =
                getAssociatedService(petDetail, petServiceMap.get(petDetail.getPetId()));
        if (associatedService == null) {
            log.error("Associated service not found, appointment id: [{}]", petDetail.getGroomingId());
            throw ExceptionUtil.bizException(Code.CODE_SERVER_ERROR, "Associated service not found");
        }
        if (Objects.equals(
                ServiceItemType.BOARDING_VALUE,
                associatedService.getServiceItemType().intValue())) {
            // every day not include the checkout day
            if (Objects.equals(PetDetailDateType.PET_DETAIL_DATE_EVERYDAY_VALUE, petDetail.getDateType())) {
                return generateAllDatesBetweenByNight(associatedService.getStartDate(), associatedService.getEndDate());
            }
            // every day include the checkout day
            return DateUtil.generateAllDatesBetween(associatedService.getStartDate(), associatedService.getEndDate());
        }
        return getDaycareDates(associatedService, petServiceMap);
    }

    public static List<String> getServiceDates(
            MoeGroomingPetDetail petDetail, Map<Integer, Map<Integer, MoeGroomingPetDetail>> petServiceMap) {
        return switch (PetDetailConverter.INSTANCE.mapServiceItemType(
                petDetail.getServiceItemType().intValue())) {
            case BOARDING -> getBoardingDates(petDetail, petServiceMap);
            case DAYCARE -> getDaycareDates(petDetail, petServiceMap);
            case GROOMING -> getGroomingDates(petDetail, petServiceMap);
            case DOG_WALKING -> getGroomingDates(petDetail, petServiceMap); // same as grooming
            default -> List.of();
        };
    }

    private static List<Integer> getPetIdsInOrder(List<MoeGroomingPetDetail> allPetDetails) {
        return allPetDetails.stream()
                .collect(Collectors.groupingBy(
                        MoeGroomingPetDetail::getPetId,
                        Collectors.minBy(comparingLong(MoeGroomingPetDetail::getUpdateTime))))
                .values()
                .stream()
                .filter(Optional::isPresent)
                .map(Optional::get)
                .sorted(comparingLong(MoeGroomingPetDetail::getUpdateTime)
                        .thenComparing(MoeGroomingPetDetail::getStartDate)
                        .thenComparingLong(MoeGroomingPetDetail::getStartTime))
                .map(MoeGroomingPetDetail::getPetId)
                .toList();
    }

    public static List<PetDetailDTO> buildGroomingOnlyPetDetails(
            List<MoeGroomingPetDetail> allPetDetails,
            List<MoeGroomingServiceOperation> allOperations,
            LocalDateTime appointmentDateTime,
            boolean allPetsStartAtSameTime) {
        var petToDetails = allPetDetails.stream().collect(Collectors.groupingBy(MoeGroomingPetDetail::getPetId));
        var petDetailToOperations = allOperations.stream()
                .collect(Collectors.groupingBy(MoeGroomingServiceOperation::getGroomingServiceId));
        List<PetDetailDTO> result = new ArrayList<>();
        var petServiceDateTime = appointmentDateTime;

        // Processing each pet in order of addition
        var petIdsInOrder = getPetIdsInOrder(allPetDetails);
        for (Integer petId : petIdsInOrder) {
            // 1. 为当前 pet 构造 pet detail 信息
            var petDetails = buildPetDetailDTOForPet(
                    petToDetails.getOrDefault(petId, List.of()), petDetailToOperations, petServiceDateTime);

            // 2. 减少当前 pet 与上一只 pet 的时间间隔
            reducePreviewPetScheduleGap(petDetails, result);

            // 3. 更新下一只 pet 的起始时间
            petServiceDateTime = getNextPetServiceDateTime(allPetsStartAtSameTime, appointmentDateTime, petDetails);

            result.addAll(petDetails);
        }
        return result;
    }

    private static List<PetDetailDTO> buildPetDetailDTOForPet(
            List<MoeGroomingPetDetail> petDetails,
            Map<Integer, List<MoeGroomingServiceOperation>> petDetailToOperations,
            LocalDateTime serviceDateTime) {
        List<PetDetailDTO> result = new ArrayList<>();

        for (MoeGroomingPetDetail petDetail : petDetails) {
            petDetail.setStartDate(serviceDateTime.toLocalDate().toString());
            petDetail.setStartTime((long) serviceDateTime.toLocalTime().toSecondOfDay() / 60);
            var endDateTime = serviceDateTime.plusMinutes(petDetail.getServiceTime());
            petDetail.setEndDate(endDateTime.toLocalDate().toString());
            petDetail.setEndTime((long) endDateTime.toLocalTime().toSecondOfDay() / 60);

            var operationStartTime = petDetail.getStartTime();
            var operations = petDetailToOperations.getOrDefault(petDetail.getId(), List.of());
            for (MoeGroomingServiceOperation operation : operations) {
                operation.setStartTime(operationStartTime.intValue());
                if (Objects.equals(petDetail.getWorkMode(), WorkMode.SEQUENCE_VALUE)) {
                    operationStartTime = operationStartTime + operation.getDuration();
                }
            }

            serviceDateTime = serviceDateTime.plusMinutes(petDetail.getServiceTime());
            result.add(new PetDetailDTO()
                    .setPetId(petDetail.getPetId().longValue())
                    .setPetDetail(petDetail)
                    .setOperations(operations));
        }
        return result;
    }

    /**
     * Pet detail: 描述 pet 选择的 service，以及 service 的服务 staff(single-staff or multi-staff) <br>
     * 根据 pet/service/staff 构造 pet detail 基础信息 <br>
     * 基础规则如下： <br>
     * 1. 同一只 Pet 的多个 Service 时间是从上往下排列的 <br>
     * 2. Single-staff 的时长就是 service 的时长 <br>
     * 3. Multi-staff 的时长取决于 work mode，work together 时时长取其一，work sequence 时时长取其和 <br>
     * 4. All pets start at same time 开关打开时，所有 pet 的开始时间一致，否则下一只 pet 的开始时间为上一只 pet 的结束时间 <br>
     *
     * @param petServices            selected pet and service details
     * @param petServiceMap          pet id -> service id -> service detail
     * @param appointmentDateTime    appointment date time
     * @param allPetsStartAtSameTime all pets start at same time
     * @return pet detail list with operations, feedings and medications
     */
    public static List<PetDetailDTO> buildGroomingOnlyPetDetails(
            List<PetServiceCalendarDef> petServices,
            Map<Long, Map<Long, CustomizedServiceView>> petServiceMap,
            LocalDateTime appointmentDateTime,
            boolean allPetsStartAtSameTime) {
        var petServiceDateTime = appointmentDateTime;
        List<PetDetailDTO> result = new ArrayList<>();

        // Each pet
        for (PetServiceCalendarDef petService : petServices) {
            // 1. 为当前 pet 构造 pet detail 信息
            var petDetails = buildPetDetailDTOForPet(petService, petServiceMap, petServiceDateTime);

            // 2. 减少当前 pet 与上一只 pet 的时间间隔
            reducePreviewPetScheduleGap(petDetails, result);

            // 3. 更新下一只 pet 的起始时间
            petServiceDateTime = getNextPetServiceDateTime(allPetsStartAtSameTime, appointmentDateTime, petDetails);

            result.addAll(petDetails);
        }
        return result;
    }

    private static void reducePreviewPetScheduleGap(
            List<PetDetailDTO> previewPetDetails, List<PetDetailDTO> beforePreviewPetDetails) {
        if (CollectionUtils.isEmpty(previewPetDetails) || CollectionUtils.isEmpty(beforePreviewPetDetails)) {
            return;
        }

        int upwardGapMinutes = calculatePreviewPetUpwardGap(previewPetDetails, beforePreviewPetDetails);

        if (upwardGapMinutes < 0) {
            offsetPetDetailsAndOperations(previewPetDetails, upwardGapMinutes);
        }
    }

    public static void reducePreviewPetScheduleGap(
            List<MoeGroomingPetDetail> previewPetDetails,
            List<MoeGroomingServiceOperation> previewOperations,
            List<MoeGroomingPetDetail> beforePetDetails,
            List<MoeGroomingServiceOperation> beforeOperations) {
        if (CollectionUtils.isEmpty(previewPetDetails) || CollectionUtils.isEmpty(beforePetDetails)) {
            return;
        }

        var previewPetDetailIdToOperations = previewOperations.stream()
                .collect(Collectors.groupingBy(MoeGroomingServiceOperation::getGroomingServiceId));
        var previewPetDetailAndOperations = previewPetDetails.stream()
                .map(petDetail -> new PetDetailDTO()
                        .setPetId(petDetail.getPetId().longValue())
                        .setPetDetail(petDetail)
                        .setOperations(previewPetDetailIdToOperations.getOrDefault(petDetail.getId(), List.of())))
                .toList();

        var beforePetDetailIdToOperations = beforeOperations.stream()
                .collect(Collectors.groupingBy(MoeGroomingServiceOperation::getGroomingServiceId));
        var beforePetDetailAndOperations = beforePetDetails.stream()
                .map(petDetail -> new PetDetailDTO()
                        .setPetId(petDetail.getPetId().longValue())
                        .setPetDetail(petDetail)
                        .setOperations(beforePetDetailIdToOperations.getOrDefault(petDetail.getId(), List.of())))
                .toList();

        reducePreviewPetScheduleGap(previewPetDetailAndOperations, beforePetDetailAndOperations);
    }

    /**
     * 将当前 preview 的 pet details 与之前的 pet details 进行比较，计算出最小 gap 作为向前平移的时间
     *
     * @param previewPetDetails preview current pet details and operations
     * @param beforePetDetails  preview current pet before details and operations
     * @return upward gap minutes
     */
    private static int calculatePreviewPetUpwardGap(
            List<PetDetailDTO> previewPetDetails, List<PetDetailDTO> beforePetDetails) {
        if (CollectionUtils.isEmpty(previewPetDetails) || CollectionUtils.isEmpty(beforePetDetails)) {
            return 0;
        }

        var staffToMaxEndDateTime = getMaxEndDateTimePerStaff(beforePetDetails);

        var firstServiceStaff = getFirstServiceStaff(previewPetDetails);
        var isFirstServiceStaffExists = firstServiceStaff.isPresent()
                && staffToMaxEndDateTime.containsKey(firstServiceStaff.get().getKey());
        if (!isFirstServiceStaffExists) {
            return 0;
        }

        var staffToMinStartDateTime = getMinStartDateTimePerStaff(previewPetDetails);

        int gapMinutes = Integer.MIN_VALUE;
        for (Map.Entry<Integer, LocalDateTime> entry : staffToMinStartDateTime.entrySet()) {
            var staffId = entry.getKey();
            var minStartDateTime = entry.getValue();

            if (staffToMaxEndDateTime.containsKey(staffId)) {
                var maxEndDateTime = staffToMaxEndDateTime.get(staffId);
                var newGap = ChronoUnit.MINUTES.between(minStartDateTime, maxEndDateTime);
                gapMinutes = Math.max(gapMinutes, (int) newGap);
            }
        }

        return gapMinutes != Integer.MIN_VALUE ? gapMinutes : 0;
    }

    private static Optional<Map.Entry<Integer, LocalDateTime>> getFirstServiceStaff(List<PetDetailDTO> petDetails) {
        return petDetails.stream()
                .flatMap(dto -> {
                    var petDetail = dto.getPetDetail();
                    if (!CollectionUtils.isEmpty(dto.getOperations())) {
                        return dto.getOperations().stream()
                                .map(operation -> Map.entry(
                                        operation.getStaffId(),
                                        buildDateTime(petDetail.getStartDate(), operation.getStartTime())));
                    } else {
                        return Stream.of(Map.entry(
                                petDetail.getStaffId(),
                                buildDateTime(
                                        petDetail.getStartDate(),
                                        petDetail.getStartTime().intValue())));
                    }
                })
                .min(Map.Entry.comparingByValue());
    }

    public static void offsetPetDetailsAndOperations(List<PetDetailDTO> petDetails, int offsetMinutes) {
        petDetails.forEach(dto -> offsetPetDetailAndOperations(dto.getPetDetail(), dto.getOperations(), offsetMinutes));
    }

    public static void offsetPetDetailsAndOperations(
            List<MoeGroomingPetDetail> petDetails, List<MoeGroomingServiceOperation> operations, int offsetMinutes) {
        // Pet detail id -> Service operations
        var serviceOperationMap =
                operations.stream().collect(Collectors.groupingBy(MoeGroomingServiceOperation::getGroomingServiceId));

        petDetails.forEach(petDetail -> offsetPetDetailAndOperations(
                petDetail, serviceOperationMap.getOrDefault(petDetail.getId(), List.of()), offsetMinutes));
    }

    private static void offsetPetDetailAndOperations(
            MoeGroomingPetDetail petDetail, List<MoeGroomingServiceOperation> operations, int offsetMinutes) {
        offsetPetDetail(petDetail, offsetMinutes);
        operations.forEach(o -> o.setStartTime(buildDateTime(petDetail.getStartDate(), o.getStartTime())
                        .plusMinutes(offsetMinutes)
                        .toLocalTime()
                        .toSecondOfDay()
                / 60));
    }

    public static void offsetPetDetail(MoeGroomingPetDetail petDetail, int offsetMinutes) {
        var newStartDateTime = calculateEndDateAndEndTime(
                petDetail.getStartDate(), petDetail.getStartTime().intValue(), offsetMinutes);
        var newEndDateTime = calculateEndDateAndEndTime(
                petDetail.getEndDate(), petDetail.getEndTime().intValue(), offsetMinutes);
        petDetail.setStartDate(newStartDateTime.first());
        petDetail.setStartTime(newStartDateTime.second().longValue());
        petDetail.setEndDate(newEndDateTime.first());
        petDetail.setEndTime(newEndDateTime.second().longValue());
    }

    /**
     * All pets start at same time 开关打开时，所有 pet 的开始时间一致，否则下一只 pet 的开始时间为上一只 pet 的结束时间
     *
     * @param allPetsStartAtSameTime all pets start at same time
     * @param appointmentDateTime    appointment date time
     * @param petDetails             current pet details
     * @return next pet service date time
     */
    private static LocalDateTime getNextPetServiceDateTime(
            boolean allPetsStartAtSameTime, LocalDateTime appointmentDateTime, List<PetDetailDTO> petDetails) {
        if (allPetsStartAtSameTime) {
            return appointmentDateTime;
        }
        return petDetails.stream()
                .map(dto -> buildDateTime(
                        dto.getPetDetail().getEndDate(),
                        dto.getPetDetail().getEndTime().intValue()))
                .max(Comparator.naturalOrder())
                .orElseThrow(() -> ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR, "Pet detail is empty"));
    }

    private static Map<Integer, LocalDateTime> getMinStartDateTimePerStaff(List<PetDetailDTO> petDetails) {
        return getMinOrMaxDateTimePerStaff(
                petDetails,
                petDetail -> buildDateTime(
                        petDetail.getStartDate(), petDetail.getStartTime().intValue()),
                (petDetail, operation) -> buildDateTime(petDetail.getStartDate(), operation.getStartTime()),
                (existing, replacement) -> existing.isBefore(replacement) ? existing : replacement);
    }

    private static Map<Integer, LocalDateTime> getMaxEndDateTimePerStaff(List<PetDetailDTO> petDetails) {
        return getMinOrMaxDateTimePerStaff(
                petDetails,
                petDetail -> buildDateTime(
                        petDetail.getEndDate(), petDetail.getEndTime().intValue()),
                (petDetail, operation) -> buildDateTime(petDetail.getStartDate(), operation.getStartTime())
                        .plusMinutes(operation.getDuration()),
                (existing, replacement) -> existing.isAfter(replacement) ? existing : replacement);
    }

    private static Map<Integer, LocalDateTime> getMinOrMaxDateTimePerStaff(
            List<PetDetailDTO> petDetails,
            Function<MoeGroomingPetDetail, LocalDateTime> petDetailTimeExtractor,
            BiFunction<MoeGroomingPetDetail, MoeGroomingServiceOperation, LocalDateTime> operationTimeExtractor,
            BinaryOperator<LocalDateTime> timeComparison) {

        return petDetails.stream()
                .filter(dto -> hasStaffAndDateTime(dto.getPetDetail()))
                .flatMap(dto -> {
                    if (!CollectionUtils.isEmpty(dto.getOperations())) {
                        return dto.getOperations().stream()
                                .map(operation -> Map.entry(
                                        operation.getStaffId(),
                                        operationTimeExtractor.apply(dto.getPetDetail(), operation)));
                    }
                    return Stream.of(Map.entry(
                            dto.getPetDetail().getStaffId(), petDetailTimeExtractor.apply(dto.getPetDetail())));
                })
                .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue, timeComparison));
    }

    private static boolean hasStaffAndDateTime(MoeGroomingPetDetail petDetail) {
        return petDetail != null
                && petDetail.getStaffId() != null
                && petDetail.getStaffId() > 0
                && StringUtils.hasText(petDetail.getStartDate())
                && StringUtils.hasText(petDetail.getEndDate())
                && petDetail.getStartTime() != null
                && petDetail.getEndTime() != null;
    }

    private static List<PetDetailDTO> buildPetDetailDTOForPet(
            PetServiceCalendarDef petService,
            Map<Long, Map<Long, CustomizedServiceView>> petServiceMap,
            LocalDateTime serviceDateTime) {
        var serviceMap = petServiceMap.getOrDefault(petService.getPetId(), Map.of());
        List<PetDetailDTO> petDetails = new ArrayList<>();

        for (GroomingServiceCalendarDef serviceSchedule : petService.getGroomingServicesList()) {
            var selectedService = PetDetailConverter.INSTANCE.toSelectedServiceDef(serviceSchedule);
            var service = serviceMap.get(serviceSchedule.getServiceId());
            var petDetail = buildGroomingOnlyService(petService.getPetId(), selectedService, service, serviceDateTime);
            petDetail.setAssociatedServiceId(serviceSchedule.getAssociatedServiceId());
            var operations = GroomingServiceOperationConverter.INSTANCE.buildServiceOperations(
                    selectedService.getOperationsList(), petDetail);

            serviceDateTime = serviceDateTime.plusMinutes(petDetail.getServiceTime());
            petDetails.add(new PetDetailDTO()
                    .setPetId(petService.getPetId())
                    .setPetDetail(petDetail)
                    .setOperations(operations)
                    .setService(service));
        }
        return petDetails;
    }

    public static MoeGroomingPetDetail buildGroomingOnlyService(
            Long petId, SelectedServiceDef def, CustomizedServiceView service, LocalDateTime serviceDateTime) {
        MoeGroomingPetDetail petDetail = buildGroomingPetDetail(petId, service, def);
        petDetail.setServiceItemType((byte) ServiceItemType.GROOMING_VALUE);
        petDetail.setStartDate(serviceDateTime.toLocalDate().toString());
        petDetail.setStartTime((long) (serviceDateTime.toLocalTime().toSecondOfDay() / 60));
        LocalDateTime endDateTime = serviceDateTime.plusMinutes(petDetail.getServiceTime());
        petDetail.setEndDate(endDateTime.toLocalDate().toString());
        petDetail.setEndTime((long) (endDateTime.toLocalTime().toSecondOfDay() / 60));
        return petDetail;
    }

    /**
     * Grooming only 的 grooming service/add-on 必须指定 staff，并且支持 multi-staff
     * All-in-One 的 grooming service/add-on 可以指定 staff，也可以不指定
     *
     * @param petId   pet id
     * @param service service detail
     * @param def     selected service def
     * @return pet detail
     */
    public static MoeGroomingPetDetail buildGroomingPetDetail(
            Long petId, CustomizedServiceView service, SelectedServiceDef def) {
        var petDetail = buildPetDetail(petId, service, def);

        int duration = def.hasServiceTime() ? def.getServiceTime() : service.getDuration();
        petDetail.setServiceTime(duration);

        var scopeTypeTime = buildScopeTypeTime(def, service);
        petDetail.setScopeTypeTime(scopeTypeTime.getNumber());

        petDetail.setStaffId((int) def.getStaffId());

        if (def.hasEnableOperation() && def.getEnableOperation()) {
            petDetail.setEnableOperation(Boolean.TRUE);
            petDetail.setWorkMode(def.getWorkModeValue());
        }
        return petDetail;
    }

    public static ServiceScopeType buildScopeTypeTime(SelectedServiceDef def, CustomizedServiceView service) {
        if (def.hasScopeTypeTime()) {
            return def.getScopeTypeTime();
        } else if (Objects.equals(service.getDurationOverrideType(), ServiceOverrideType.CLIENT)) {
            return ServiceScopeType.ONLY_THIS;
        } else {
            return ServiceScopeType.DO_NOT_SAVE;
        }
    }

    public static ServiceScopeType buildScopeTypePrice(SelectedServiceDef def, CustomizedServiceView service) {
        if (def.hasScopeTypePrice()) {
            return def.getScopeTypePrice();
        } else if (Objects.equals(service.getPriceOverrideType(), ServiceOverrideType.CLIENT)) {
            return ServiceScopeType.ONLY_THIS;
        } else {
            return ServiceScopeType.DO_NOT_SAVE;
        }
    }

    public static MoeGroomingPetDetail buildPetDetail(
            Long petId, CustomizedServiceView service, SelectedServiceDef def) {
        MoeGroomingPetDetail petDetail = new MoeGroomingPetDetail();
        petDetail.setPetId(petId.intValue());
        petDetail.setServiceId((int) service.getId());
        petDetail.setServiceType(service.getTypeValue());
        petDetail.setServiceItemType((byte) service.getServiceItemTypeValue());
        petDetail.setPriceUnit(service.getPriceUnitValue());
        petDetail.setUpdateTime(DateUtil.get10Timestamp());
        // 有传用传，没传自取
        double price = def.hasServicePrice() ? def.getServicePrice() : service.getPrice();
        petDetail.setServicePrice(BigDecimal.valueOf(price));

        var scopeTypePrice = buildScopeTypePrice(def, service);
        petDetail.setScopeTypePrice(scopeTypePrice.getNumber());

        // 默认值
        petDetail.setScopeTypeTime(ServiceScopeType.DO_NOT_SAVE_VALUE);
        if (def.hasDurationOverrideType()) {
            petDetail.setDurationOverrideType(def.getDurationOverrideType());
        }
        if (def.hasPriceOverrideType()) {
            petDetail.setPriceOverrideType(def.getPriceOverrideType());
        }
        if (def.hasDateType()) {
            petDetail.setDateType(def.getDateTypeValue());
        }
        if (!def.getStartDate().isEmpty()) {
            petDetail.setStartDate(def.getStartDate());
        }
        if (def.hasEndDate()) {
            petDetail.setEndDate(def.getEndDate());
        }
        return petDetail;
    }

    /**
     * 将 pet detail 的 everyday 解析为静态的startDate 和 endDate.
     *
     * @param allPetDetails 若干预约下所有的 pet details. serviceType 字段需要被正确赋值
     * @return 入参的拷贝，返回参数的编辑不影响入参 petDetails
     */
    public static List<MoeGroomingPetDetail> getWithActualDatesInfo(List<MoeGroomingPetDetail> allPetDetails) {
        if (CollectionUtils.isEmpty(allPetDetails)) {
            return List.of();
        }
        List<MoeGroomingPetDetail> result = new ArrayList<>();

        var appointmentPetDetails =
                allPetDetails.stream().collect(Collectors.groupingBy(MoeGroomingPetDetail::getGroomingId));
        appointmentPetDetails.forEach((appointmentId, petDetails) -> {
            var petServiceMap = getPetServiceMap(petDetails);
            petDetails.forEach(curPetDetail -> {
                var newPetDetail = PetDetailConverter.INSTANCE.entityToEntity(curPetDetail);
                PetDetailDateType dateType = PetDetailUtil.getDateType(newPetDetail);
                switch (dateType) {
                    case PET_DETAIL_DATE_EVERYDAY: {
                        var actualDatesInfo = getActualDatesInfo(curPetDetail, petServiceMap);
                        newPetDetail.setStartDate(actualDatesInfo.getStartDate());
                        newPetDetail.setEndDate(actualDatesInfo.getEndDate());
                        newPetDetail.setSpecificDates(actualDatesInfo.getSpecificDates());
                        // 对于关联到 boarding 上的 everyday 配置，不计算最后一天
                        if (actualDatesInfo.getServiceItemType() == ServiceItemType.BOARDING_VALUE) {
                            newPetDetail.setEndDate(LocalDate.parse(actualDatesInfo.getEndDate())
                                    .plusDays(-1)
                                    .toString());
                        }
                        break;
                    }
                    case PET_DETAIL_DATE_EVERYDAY_INCLUDE_CHECKOUT_DAY: {
                        var actualDatesInfo = getActualDatesInfo(curPetDetail, petServiceMap);
                        newPetDetail.setStartDate(actualDatesInfo.getStartDate());
                        newPetDetail.setEndDate(actualDatesInfo.getEndDate());
                        newPetDetail.setSpecificDates(actualDatesInfo.getSpecificDates());
                        break;
                    }
                    case PET_DETAIL_DATE_EVERYDAY_EXCEPT_CHECKIN_DAY: {
                        var actualDatesInfo = getActualDatesInfo(curPetDetail, petServiceMap);
                        newPetDetail.setStartDate(actualDatesInfo.getStartDate());
                        newPetDetail.setEndDate(actualDatesInfo.getEndDate());
                        newPetDetail.setSpecificDates(actualDatesInfo.getSpecificDates());
                        // 对于关联到 boarding 上的 everyday 配置，不计算最后一天
                        if (actualDatesInfo.getServiceItemType() == ServiceItemType.BOARDING_VALUE) {
                            newPetDetail.setStartDate(LocalDate.parse(actualDatesInfo.getEndDate())
                                    .plusDays(1)
                                    .toString());
                        }
                        break;
                    }
                    case PET_DETAIL_DATE_LAST_DAY: {
                        var actualDatesInfo = getActualDatesInfo(curPetDetail, petServiceMap);
                        newPetDetail.setStartDate(actualDatesInfo.getEndDate());
                        newPetDetail.setEndDate(actualDatesInfo.getEndDate());
                        newPetDetail.setSpecificDates(actualDatesInfo.getSpecificDates());
                        break;
                    }
                    case PET_DETAIL_DATE_FIRST_DAY: {
                        var actualDatesInfo = getActualDatesInfo(curPetDetail, petServiceMap);
                        newPetDetail.setStartDate(actualDatesInfo.getStartDate());
                        newPetDetail.setEndDate(actualDatesInfo.getStartDate());
                        newPetDetail.setSpecificDates(actualDatesInfo.getSpecificDates());
                        break;
                    }
                    default:
                        break;
                }
                result.add(newPetDetail);
            });
        });

        return result;
    }

    /**
     * @return 包含静态日期信息的 petDetail，即 datePoint 或 specificDates 类型的 petDetail
     */
    static MoeGroomingPetDetail getActualDatesInfo(
            MoeGroomingPetDetail petDetail, Map<Integer, Map<Integer, MoeGroomingPetDetail>> petServiceMap) {
        var dateType = PetDetailUtil.getDateType(petDetail);
        if (Objects.equals(dateType, PetDetailDateType.PET_DETAIL_DATE_DATE_POINT)
                || Objects.equals(dateType, PetDetailDateType.PET_DETAIL_DATE_SPECIFIC_DATE)
                || Objects.equals(dateType, PetDetailDateType.PET_DETAIL_DATE_FIRST_DAY)
                || Objects.equals(dateType, PetDetailDateType.PET_DETAIL_DATE_LAST_DAY)) {
            return petDetail;
        }
        var associatedService = PetDetailUtil.getAssociatedService(
                petDetail, petServiceMap.getOrDefault(petDetail.getPetId(), Map.of()));
        if (associatedService == null) {
            log.error("Associated service not found, appointment id: [{}]", petDetail.getGroomingId());
            throw bizException(Code.CODE_SERVER_ERROR, "Associated service not found");
        }
        return getActualDatesInfo(associatedService, petServiceMap);
    }

    public static Map<Integer, ServiceItemEnum> getPetMainCareType(List<MoeGroomingPetDetail> allPetDetails) {
        return allPetDetails.stream()
                .collect(Collectors.groupingBy(
                        MoeGroomingPetDetail::getPetId,
                        Collectors.collectingAndThen(
                                Collectors.mapping(MoeGroomingPetDetail::getServiceItemType, Collectors.toSet()),
                                serviceTypes -> ServiceItemEnum.getMainServiceItemType(serviceTypes.stream()
                                        .map(Byte::intValue)
                                        .toList()))));
    }

    public static Long getPetSizeId(String weight, List<BusinessPetSizeModel> petSizeList) {
        if (!StringUtils.hasText(weight)) {
            return null;
        }
        BigDecimal weightDecimal;
        try {
            weightDecimal = new BigDecimal(weight);
        } catch (NumberFormatException e) {
            return null;
        }

        var roundedWeight = weightDecimal.setScale(0, RoundingMode.HALF_UP).intValue();
        BusinessPetSizeModel targetSize = petSizeList.stream()
                .filter(size -> size.getWeightLow() <= roundedWeight && size.getWeightHigh() >= roundedWeight)
                .findFirst()
                .orElse(null);
        if (targetSize == null) {
            return null;
        }
        return targetSize.getId();
    }

    public static int calculatePetDetailDateCount(MoeGroomingPetDetail petDetail) {
        return calculateDays(petDetail);
    }
}
