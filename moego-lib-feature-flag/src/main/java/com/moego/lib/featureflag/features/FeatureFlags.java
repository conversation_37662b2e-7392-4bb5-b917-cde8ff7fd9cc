package com.moego.lib.featureflag.features;

import com.moego.lib.featureflag.FeatureFlag;
import jakarta.annotation.Nonnull;

/**
 * All feature flags.
 *
 * <p> Add new feature flags here.
 *
 * <AUTHOR>
 */
public enum FeatureFlags implements FeatureFlag {
    NEW_SMART_SCHEDULE_SQL("new-smart-schedule-sql"),
    NEW_ORDER_FLOW("new_order_flow"),
    INVOICE_OVERRIDE_UPDATE("invoice_override_update"),
    CLOSE_AUTO_REMINDER_SERVICE("close_auto_reminder_service"),
    PAYROLL_NEW_LOGIC_FOR_PACKAGE("payroll_package_new_logic"),
    BOOK_BY_SLOT_WITH_RESERVATION("book_by_slot_with_reservation"),
    VALIDATE_PET_DETAILS("validate_pet_details"),
    PAYROLL_NEW_LOGIC_FOR_BD("payroll_bd_new_logic"),
    OB_NEW_CLIENT_PET_COF_EDIT_FLOW("ob_new_client_pet_cof_edit_flow"),
    FULFILLMENT_FLOW("fulfillment_flow"),
    ALLOW_GROUP_CLASS("allow_group_class"),
    ALLOW_DOG_WALKING("allow_dog_walking"),
    DYNAMIC_DEPOSIT("dynamic_deposit"),
    RESIZE_MULTI_SERVICE_CARD("resize_multi_service_card"),
    ENTERPRISE_DISCOUNT_REDEEM_LINEITEM("enterprise_discount_redeem_lineitem"),
    /**
     * 在支付完成后，不把 appointment status 改为 finished
     *
     * @see <a href="https://moego.atlassian.net/browse/MER-4505">MER-4505</a>
     */
    FULL_PAYMENT_NO_STATUS_CHANGE("full_payment_no_status_change"),
    CUSTOM_SMS_SUBSCRIPTION_AMOUNT("custom_sms_subscription_amount"),
    ENTERPRISE_EVALUATION_REQUIREMENTS("enterprise_evaluation_requirements"),
    ENABLE_LEAD_MANAGEMENT("leads_management_whitelist_ob"),
    ENABLE_MULTI_PET_BY_SLOT("enable_multi_pet_by_slot"),
    CUSTOM_SMS_PACKAGE_PRICE("custom_sms_package_price"),
    CUSTOMIZED_EMAIL_DOMAIN("customized_email_domain"),
    CATS_IN_THE_CITY_CUSTOMIZED_FEEDBACK("cats_in_the_city_customized_feedback"),
    CAMERA_OB_GROOMING_IS_SHOW("camera_ob_grooming_is_show"),
    ENABLE_MEMBERSHIP("enable_membership"),
    TRACE_FOR_AUTO_REMAINDER("trace_for_auto_reminder_service"),
    ENABLE_NEW_LODGING_OCCUPANCY("enable_new_lodging_occupancy"),
    TIME_SLOT_FREE_SERVICE("time_slot_free_service"),
    ;

    private final String feature;

    FeatureFlags(String feature) {
        this.feature = feature;
    }

    @Nonnull
    @Override
    public String getFeature() {
        return feature;
    }
}
